import * as Repack from '@callstack/repack';
import {ReanimatedPlugin} from '@callstack/repack-plugin-reanimated';
import rspack from '@rspack/core';
import Dotenv from 'dotenv-webpack';
import {getSharedDependencies} from 'msb-digibank-sdk';
import path from 'node:path';
import {fileURLToPath} from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/** @type {(env: import('@callstack/repack').EnvOptions) => import('@rspack/core').Configuration} */
export default env => {
  console.log('[Rspack: Use MFv1]');

  const environment = process.env.ENV || 'local';
  const isLocalEnv = environment === 'local';

  const {mode, platform} = env;

  process.env.BABEL_ENV = mode;
  const suffix = isLocalEnv ? '' : `.${environment}`;
  const envFilePath = `.env${suffix}`;

  /**
   * <PERSON><PERSON>u hình cho các envs
   * Local: Dùng inline assets
   * Other(dev, pilot, sit, uat, prod): Dùng remote assets
   */

  // Common rules for all environments
  /** @type {import('@rspack/core').ModuleOptions['rules']} */
  let rules = [
    ...Repack.getJsTransformRules(),
    {
      test: /\.jsx?$/,
      type: 'javascript/auto',
      exclude: /node_modules\/(?!react-native-(modal-datetime-picker|keyboard-aware-scroll-view)|rn-qr-generator)/,
      use: {
        loader: '@callstack/repack/flow-loader',
        options: {all: true},
      },
    },
  ];

  const dotenv = new Dotenv({
    path: envFilePath,
    safe: false,
    allowEmptyValues: true,
    systemvars: true,
  });

  /**
   * Workaround để load env trong webpack config file
   * Sử dụng reflect hàm getEnvs của plugin dotenv
   * Nếu env không có PUBLIC_PATH_REMOTE_ASSETS thì sử dụng inline assets
   */
  const publicPath = dotenv.getEnvs().env.PUBLIC_PATH_REMOTE_ASSETS;

  // Environment-specific rules
  const injectInlineRule = !publicPath;
  if (injectInlineRule) {
    /** @type {import('@rspack/core').ModuleOptions['rules']} */
    const localAssetsRule = [
      {
        test: Repack.getAssetExtensionsRegExp(Repack.ASSET_EXTENSIONS),
        use: {
          loader: '@callstack/repack/assets-loader',
          options: {
            inline: true,
          },
        },
      },
    ];

    rules = [...rules, ...localAssetsRule];
    console.log('inject local rules');
  } else {
    /** @type {import('@rspack/core').ModuleOptions['rules']} */
    const otherAssetRules = [
      {
        test: Repack.getAssetExtensionsRegExp(),
        exclude: [path.join(__dirname, 'src/assets/images/inline'), path.join(__dirname, 'src/assets/images/remote')],
        use: '@callstack/repack/assets-loader',
      },
      {
        test: /\.svg$/,
        use: [
          {
            loader: '@svgr/webpack',
            options: {
              native: true,
              dimensions: false,
            },
          },
        ],
      },

      {
        test: Repack.getAssetExtensionsRegExp(),
        include: [path.join(__dirname, 'src/assets/images/inline')],
        use: {
          loader: '@callstack/repack/assets-loader',
          options: {
            inline: true,
          },
        },
      },
      {
        test: Repack.getAssetExtensionsRegExp(),
        include: [path.join(__dirname, 'src/assets/images/remote')],
        use: {
          loader: '@callstack/repack/assets-loader',
          options: {
            remote: {
              enabled: true,
              /**
               * Hãy đảm bảo là public path là CDN đã chứa remote assets
               * Chạy cmd: yarn build -> output: build/outputs/[platform]/remotes
               * Cách 1: Sử dụng http-server để làm local server chứa remote assets
               * bằng cách chạy cmd: yarn serve-remote-assets:ios hoặc yarn serve-remote-assets:android
               * Đường sử dụng như bên dưới
               *
               * Cách 2: Start docker image ở host app để dử dụng nginx
               * Đường dẫn đến remote assets sẽ như bên dưới
               *
               * publicPath sẽ được lấy từ PUBLIC_PATH_REMOTE_ASSETS trong file env hiện tại
               */
              publicPath: publicPath,
            },
          },
        },
      },
    ];

    rules = [...rules, ...otherAssetRules];
    console.log('inject other rules');
  }

  return {
    mode,
    context: __dirname,
    entry: {},
    experiments: {
      incremental: mode === 'development',
    },
    resolve: {
      ...Repack.getResolveOptions(platform),
      tsConfig: {
        configFile: path.resolve(__dirname, './tsconfig.json'),
        references: 'auto',
      },
    },
    output: {
      path: path.join(__dirname, 'build/outputs', platform),
      uniqueName: 'MSBDigibank-CardModule',
    },
    module: {
      rules,
    },
    plugins: [
      dotenv,
      new Repack.RepackPlugin({
        output: {
          auxiliaryAssetsPath: injectInlineRule ? undefined : path.join('build/outputs', platform, 'remotes'),
        },
        extraChunks: injectInlineRule
          ? undefined
          : [
              {
                include: /.*/,
                type: 'remote',
                outputPath: path.join('build/outputs', platform, 'remotes'),
              },
            ],
      }),
      new Repack.plugins.ModuleFederationPluginV1({
        name: 'CardModule',
        filename: 'CardModule.container.bundle',
        dts: false,
        /**
         * Module expose phải bắt đầu bằng `./`
         * Không hợp lệ:
         * ```js
         * exposes: {
         *  'CardModuleMain': './src/CardModuleMain.tsx',
         * },
         * ```
         * Hợp lệ:
         * ```js
         * exposes: {
         *  './CardModuleMain': './src/CardModuleMain.tsx',
         * },
         * ```
         */
        exposes: {
          './CardModuleMain': './src/CardModuleMain.tsx',
          './AssetCreditCard': './src/presentation/components/asset-credit-card/index.tsx',
        },
        runtimePlugins: [path.resolve(__dirname, 'remote-fallback-plugin.ts')],
        shared: getSharedDependencies({eager: false}),
      }),
      new Repack.plugins.CodeSigningPlugin({
        enabled: mode === 'production',
        privateKeyPath: './code-signing.pem',
      }),

      new ReanimatedPlugin(),
      new rspack.IgnorePlugin({
        resourceRegExp: /^@react-native-masked-view/,
      }),
      new rspack.IgnorePlugin({
        resourceRegExp: /^react-native-worklets-core/,
      }),
      new rspack.IgnorePlugin({
        resourceRegExp: /^@shopify\/react-native-skia/,
      }),
      new rspack.IgnorePlugin({
        resourceRegExp: /^react-native-reanimated\/src\/reanimated2\/core/,
      }),
    ],
  };
};
