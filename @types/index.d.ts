import {BottomSheetActions, PopupActions, ToastActions} from 'msb-host-shared-module';
import {
  Control,
  FieldPathValue,
  FieldValues,
  Path,
  UseFormGetValues,
  UseFormSetError,
  UseFormSetValue,
  UseFormTrigger,
} from 'react-hook-form';
import {z} from 'zod';

export {};
declare global {
  type Nullish<T> = T | null | undefined;
  var _MSB_TEST_BOTTOM_SHEET: React.RefObject<BottomSheetActions | null> | null;
  var _MSB_TEST_POPUP: React.RefObject<PopupActions | null> | null;
  var _MSB_TEST_TOAST: React.RefObject<ToastActions | null> | null;
  type ZodShape<T> = {
    // Require all the keys from T
    [key in keyof T]-?: undefined extends T[key] ? z.ZodOptionalType<z.ZodType<T[key]>> : z.ZodType<T[key]>;
  };
  type FallbackComponentParams = {
    id?: 'TransferSourceAccount' | 'HelperModuleMain';
  };
  type CardFlow = 'DEFAULT' | 'OPEN_NOW' | 'ASSET_CREDIT_CARD';
  type DefaultSegmentStackParamList = {
    CardStack: {
      flow: CardFlow;
    };
  };
  type AssetCommonRef = {
    onRefresh?: () => Promise<void>;
  };

  /**
   * react-hook-form
   */

  interface FormControl<T extends FieldValues> {
    testID?: string;
    control: Control<T, any, T>;
    /**
     * Trigger name field of react hook form
     */
    nameTrigger: keyof T;
    /**
     * If trigger exists, it will validate this field after value changed
     */
    /**
     * Selected value
     */
    defaultValue?: FieldPathValue<T, Path<T>>;
    getValues?: UseFormGetValues<T>;
    trigger?: UseFormTrigger<T>;
    setValue?: UseFormSetValue<T>;
    setError?: UseFormSetError<T>;
  }
}
