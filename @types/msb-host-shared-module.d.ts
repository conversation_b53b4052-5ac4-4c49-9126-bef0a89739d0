import {BottomSheetProps, PopupProps} from 'msb-host-shared-module';
import {IDeeplinkService} from 'msb-host-shared-module/dist/IDeeplinkService';
import {IDomainService} from 'msb-host-shared-module/dist/IDomainService';
import {IHttpClient} from 'msb-host-shared-module/dist/IHttpClient';

export type PopupExtraProps = PopupProps & {
  onConfirmAfterAnimation?: () => void;
};

export interface CustomIDomainService extends Omit<IDomainService, 'showPopup'> {
  showPopup(data: PopupExtraProps): void;
  showBottomSheet(
    data: BottomSheetProps & {
      enableDynamicSizing?: boolean;
    },
  ): void;
}

export interface IHostSharedModule {
  httpClient: IHttpClient;
  domainService: CustomIDomainService;
  domainService2: DomainService2;
  deeplinkService: IDeeplinkService;
  locale: string;
}
declare module 'msb-host-shared-module' {
  export const hostSharedModule: {
    d: IHostSharedModule;
  };
}

export interface DomainService2 {
  init: (data: string) => void;
}
