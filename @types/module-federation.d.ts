interface SourceAccountModel {
  id: string;
  productKindName: string;
  legalEntityIds: string[];
  productId: string;
  productTypeName: string;
  externalProductId: string;
  externalArrangementId: string;
  userPreferences?: SourceAccountUserPreferencesModel;
  product: SourceAccountProductModel;
  state?: any | null;
  parentId?: string | null;
  subscriptions?: any | null;
  isDefault: string;
  cifNo: string;
  virtualAccountInfos?: any[];
  additions?: any | null;
  name: string;
  bookedBalance?: number | null;
  availableBalance?: number | null;
  creditLimit?: number | null;
  currency: string;
  externalTransferAllowed?: boolean | null;
  urgentTransferAllowed?: boolean | null;
  accountOpeningDate?: string | null;
  accountHolderNames?: string | null;
  bankAlias?: string;
  BBAN?: string;
  IBAN?: string | null;
  BIC?: string | null;
  amountQR?: string | null;
  contentQR?: string | null;
}

declare module 'TransferModule/SourceAccount' {
  import React from 'react';
  import {StyleProp, ViewStyle} from 'react-native';
  type SourceAccountProps = {
    containerStyle?: StyleProp<ViewStyle>;
    title: string; // example: 'Tài khoản nguồn'
    onSelectAccount: (account: SourceAccountModel) => void; // get account has selected from source account list
    handleFilterAccounts?: (accounts: SourceAccountModel[]) => SourceAccountModel[]; // filter with conditions accounts has selected from source account list
    errorTitle?: string; // if error, show error
    hasCreditCard?: boolean;
    sourceAccounts?: SourceAccountModel[]; // custom account list
    disabled?: boolean; // if true, disable select account
    defaultTagVisible?: boolean; // if false, not show default tag bellow account info. Default is true
    accNo?: string; // custom first account has selected from source account list
    request?: any;
  };
  const Default: React.ComponentType<SourceAccountProps>;
  export default Default;
}

declare module 'HelperModule/TermAndCondition' {
  import React from 'react';
  type TermAndConditionProps = {
    type: string; // example "ONBOARDING"
    titleButton?: string; // title button confirm
    requireReadCondition?: boolean; // if true require read pdf and show checkbox read
    tncListInit?: TncCodes[]; // with a array tnc different with common
    onConfirmationTncSuccess?: (result: any) => void; // handle action when call api confirm success
    onConfirmationTncError?: () => void; // handle action when call api confirm error
    onPressHightlightText?: () => void; // handle action press hightlight text show more term and condition
  };
  const Default: React.ComponentType<TermAndConditionProps>;
  export default Default;
}

declare module 'HelperModule/TermAndCondition' {
  type TermAndConditionProps = {
    onPressHightlightText?: () => void; // handle action press hightlight text show more term and condition
  };
  const Default: React.ComponentType<TermAndConditionProps>;
  export default Default;
}
