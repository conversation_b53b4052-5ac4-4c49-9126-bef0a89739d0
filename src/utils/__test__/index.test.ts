import moment from 'moment';
import {ToastType, hostSharedModule} from 'msb-host-shared-module';
import {Platform} from 'react-native';
import {
  clearFormatAmount,
  convertDateTimeTo,
  createLazyLoadingManager,
  debounceTimes,
  formatAmountForeign,
  formatAmountText,
  getDateRange,
  isValidPin,
  propsToStyle,
  showPopup,
  showToastMessage,
  toDefinedOrDefaultValue,
  waitForFinishingModalAnimation,
  waitForShowingModal,
} from '../index';

jest.doMock('react-native/Libraries/Utilities/Platform.android.js', () => ({
  OS: 'android',
  select: jest.fn(),
}));

jest.doMock('react-native/Libraries/Utilities/Platform.ios.js', () => ({
  OS: 'android',
  select: jest.fn(),
}));

describe('utils', () => {
  it('returns false if pin is not in correct format', () => {
    expect(isValidPin('012345')).toBe(false);
    expect(isValidPin('12345')).toBe(false);
    expect(isValidPin('abcdef')).toBe(false);
  });

  it('returns false if pin is sequential increasing or decreasing', () => {
    expect(isValidPin('123456')).toBe(false);
    expect(isValidPin('654321')).toBe(false);
  });

  it('returns true if pin is valid', () => {
    expect(isValidPin('135792')).toBe(true);
  });
});

describe('convertDateTimeTo', () => {
  it('converts to correct date format', () => {
    expect(convertDateTimeTo('2023-01-01', 'DD/MM/YYYY')).toBe('01/01/2023');
  });
});

describe('clearFormatAmount', () => {
  it('returns "" if input is empty', () => {
    expect(clearFormatAmount('')).toBe('');
    expect(clearFormatAmount(null)).toBe('');
    expect(clearFormatAmount(undefined)).toBe('');
  });

  it('returns number if input is number', () => {
    expect(clearFormatAmount(1234)).toBe(1234);
  });

  it('removes commas from string', () => {
    expect(clearFormatAmount('1,234,567')).toBe('1234567');
  });
});

describe('formatAmountForeign', () => {
  it('formats numbers greater than 3 digits', () => {
    expect(formatAmountForeign('1234567')).toBe('1,234,567');
  });

  it('should assign "0" when textValue is empty string', () => {
    const result = formatAmountForeign('');
    expect(result).toBe('0');
  });

  it('remove 0 if numbers equal 2 digits and start with 0', () => {
    expect(formatAmountForeign('01')).toBe('1');
  });

  it('keeps numbers less than 4 digits unchanged', () => {
    expect(formatAmountForeign('123')).toBe('123');
  });

  it('removes non-digit characters', () => {
    expect(formatAmountForeign('1a2b3c')).toBe('123');
  });
});

describe('formatAmountText', () => {
  it('returns 0 if input is empty', () => {
    expect(formatAmountText('')).toBe(0);
    expect(formatAmountText(null)).toBe(0);
  });

  it('formats positive numbers', () => {
    expect(formatAmountText('1234567')).toBe('1,234,567');
    expect(formatAmountText(1234567)).toBe('1,234,567');
  });

  it('formats negative numbers', () => {
    expect(formatAmountText(-1234567)).toBe('-1,234,567');
  });

  it('formats numbers with decimal part', () => {
    expect(formatAmountText('1234.56')).toBe('1,234.56');
    expect(formatAmountText(-1234.56)).toBe('-1,234.56');
  });
});

describe('debounceTimes', () => {
  it('only calls function after delay', () => {
    const fn = jest.fn();
    const debounced = debounceTimes(fn, 1000);
    debounced();
    expect(fn).not.toHaveBeenCalled();
    jest.runAllTimers();
    expect(fn).toHaveBeenCalledTimes(1);
  });
});

describe('waitForFinishingModalAnimation on non-Android platforms', () => {
  it('should resolve immediately on non-iOS platforms', async () => {
    Platform.OS = 'ios';
    await expect(waitForFinishingModalAnimation()).resolves.toBeUndefined();
  });
});

describe('waitForFinishingModalAnimation on Android platforms', () => {
  it('should delay resolution on Android platforms', async () => {
    Platform.OS = 'android';
    waitForFinishingModalAnimation();
    jest.runAllTimers();
    expect(setTimeout).toHaveBeenCalledTimes(1);
  });
});

describe('toDefinedOrDefaultValue', () => {
  it('should return the default value if the input is undefined', () => {
    expect(toDefinedOrDefaultValue(undefined, 'default')).toBe('default');
  });

  it('should return the default value if the input is null', () => {
    expect(toDefinedOrDefaultValue(null, 'default')).toBe('default');
  });

  it('should return the input value if it is defined', () => {
    expect(toDefinedOrDefaultValue('value', 'default')).toBe('value');
  });

  it('should return undefined if no default value is provided and the input is undefined', () => {
    expect(toDefinedOrDefaultValue(undefined)).toBeUndefined();
  });
});

describe('waitForShowingModal on non-iOS platforms', () => {
  it('should resolve immediately on non-iOS platforms', async () => {
    Platform.OS = 'android';
    await expect(waitForShowingModal()).resolves.toBeUndefined();
  });
});

describe('waitForShowingModal on iOS platforms', () => {
  it('should delay resolution on iOS platforms', async () => {
    Platform.OS = 'ios';
    waitForShowingModal(300);
    jest.runAllTimers();
    expect(setTimeout).toHaveBeenCalledTimes(1);
  });
});

describe('propsToStyle', () => {
  it('filters out undefined styles or styles with undefined properties', () => {
    const arr = [{color: 'red'}, undefined, {fontSize: undefined}, {fontSize: 12}];
    expect(propsToStyle(arr)).toEqual([{color: 'red'}, {fontSize: 12}]);
  });
});

describe('createLazyLoadingManager', () => {
  it('manages loading state correctly', () => {
    const setLoadingFirst = jest.fn();
    const setLoadingMore = jest.fn();
    const manager = createLazyLoadingManager(setLoadingFirst, setLoadingMore);

    manager.showLoading(true);
    expect(setLoadingFirst).toHaveBeenCalledWith(true);

    manager.showLoading(false);
    expect(setLoadingMore).toHaveBeenCalledWith(true);

    manager.hideLoading(true);
    expect(setLoadingFirst).toHaveBeenCalledWith(false);

    manager.hideLoading(false);
    expect(setLoadingMore).toHaveBeenCalledWith(false);
  });
});

describe('showPopup', () => {
  it('should call showPopup with correct data', () => {
    const popupProps = {title: 'Test Popup', message: 'This is a test popup'};
    showPopup(popupProps);

    expect(hostSharedModule.d.domainService.showPopup).toHaveBeenCalledWith(popupProps);
  });
});

describe('showToastMessage', () => {
  it('should call showToast with correct message and type', () => {
    const message = 'Test Toast';
    const type = ToastType.ERROR;
    showToastMessage(message, type);

    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({message, type});
  });

  it('should call showToast with default type if not provided', () => {
    const message = 'Test Toast';
    showToastMessage(message);

    expect(hostSharedModule.d.domainService.showToast).toHaveBeenCalledWith({message, type: undefined});
  });
});

describe('getDateRange', () => {
  const fixedNow = moment.utc('2025-05-23T10:00:00.000Z');

  beforeAll(() => {
    jest.spyOn(global.Date, 'now').mockImplementation(() => fixedNow.valueOf());
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('should return correct range for 7 days', () => {
    const {startDate, endDate} = getDateRange(7);
    expect(startDate.format('YYYY-MM-DD')).toBe('2025-05-16');
    expect(endDate.format('YYYY-MM-DD')).toBe('2025-05-23');
  });

  it('should return correct range for 15 days', () => {
    const {startDate, endDate} = getDateRange(15);
    expect(startDate.format('YYYY-MM-DD')).toBe('2025-05-08');
    expect(endDate.format('YYYY-MM-DD')).toBe('2025-05-23');
  });

  it('start and end dates should be at start of day', () => {
    const {startDate, endDate} = getDateRange(7);
    expect(startDate.hour()).toBe(0);
    expect(startDate.minute()).toBe(0);
    expect(startDate.second()).toBe(0);

    expect(endDate.hour()).toBe(0);
    expect(endDate.minute()).toBe(0);
    expect(endDate.second()).toBe(0);
  });
});
