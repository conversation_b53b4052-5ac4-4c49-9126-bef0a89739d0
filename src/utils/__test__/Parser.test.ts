import {filterNullishValues, getEnumFromValue, safeJSONParse, transformExpDate} from '../Parser';

describe('safeJSONParse', () => {
  it('should correctly parse valid JSON string', () => {
    const validJSON = '{"name": "<PERSON>", "age": 30}';
    const expected = {name: '<PERSON>', age: 30};
    expect(safeJSONParse<{name: string; age: number}>(validJSON)).toEqual(expected);
  });

  it('should handle arrays correctly', () => {
    const arrayJSON = '[1, 2, 3, 4]';
    const expected = [1, 2, 3, 4];
    expect(safeJSONParse<number[]>(arrayJSON)).toEqual(expected);
  });

  it('should return undefined for undefined input', () => {
    expect(safeJSONParse<any>(undefined)).toBeUndefined();
  });

  it('should return undefined for null input', () => {
    expect(safeJSONParse<any>(null)).toBeUndefined();
  });

  it('should return undefined for empty string', () => {
    expect(safeJSONParse<any>('')).toBeUndefined();
  });

  it('should return undefined for invalid JSON and log error', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    const invalidJSON = '{name: "John", age: 30}'; // Missing quotes around property name

    expect(safeJSONParse<any>(invalidJSON)).toBeUndefined();
    expect(consoleSpy).toHaveBeenCalledWith('[safeJSONParse error]:', expect.any(Error));

    consoleSpy.mockRestore();
  });
});

describe('transformExpDate', () => {
  it('should correctly format a valid expiration date', () => {
    expect(transformExpDate('0525')).toBe('05/25');
    expect(transformExpDate('1230')).toBe('12/30');
  });

  it('should return empty string for undefined input', () => {
    expect(transformExpDate()).toBe('');
  });

  it('should return empty string for null input', () => {
    expect(transformExpDate(null as unknown as string)).toBe('');
  });

  it('should return empty string for empty string input', () => {
    expect(transformExpDate('')).toBe('');
  });

  it('should return empty string for input with incorrect length', () => {
    expect(transformExpDate('123')).toBe('');
    expect(transformExpDate('12345')).toBe('');
  });
});

enum TestEnum {
  FIRST = 'first',
  SECOND = 'second',
  THIRD = 'third',
}

enum NumericEnum {
  ONE = 1,
  TWO = 2,
  THREE = 3,
}

describe('getEnumFromValue', () => {
  it('should return the correct enum key when given a valid string value', () => {
    expect(getEnumFromValue(TestEnum, 'first')).toBe(TestEnum.FIRST);
    expect(getEnumFromValue(TestEnum, 'second')).toBe(TestEnum.SECOND);
    expect(getEnumFromValue(TestEnum, 'third')).toBe(TestEnum.THIRD);
  });

  it('should return the correct enum key when given a valid numeric value', () => {
    expect(getEnumFromValue(NumericEnum, 1)).toBe(NumericEnum.ONE);
    expect(getEnumFromValue(NumericEnum, 2)).toBe(NumericEnum.TWO);
    expect(getEnumFromValue(NumericEnum, 3)).toBe(NumericEnum.THREE);
  });

  it('should return undefined when value is not found in enum', () => {
    expect(getEnumFromValue(TestEnum, 'fourth')).toBeUndefined();
    expect(getEnumFromValue(NumericEnum, 4)).toBeUndefined();
  });

  it('should return undefined when value is undefined', () => {
    expect(getEnumFromValue(TestEnum)).toBeUndefined();
  });

  it('should return undefined when value is null', () => {
    expect(getEnumFromValue(TestEnum, null)).toBeUndefined();
  });

  it('should return undefined when value is empty string', () => {
    expect(getEnumFromValue(TestEnum, '')).toBeUndefined();
  });
});

describe('filterNullishValues', () => {
  it('should remove null and undefined values', () => {
    const input = {
      cardId: '123',
      page: 1,
      size: 10,
      from: '2024-01-01',
      to: '2024-01-31',
      keySearch: '',
      amountMin: null,
      amountMax: undefined,
      transType: null,
      categoryCode: ['A', 'B'],
    };

    const expected = {
      cardId: '123',
      page: 1,
      size: 10,
      from: '2024-01-01',
      to: '2024-01-31',
      keySearch: '',
      categoryCode: ['A', 'B'],
    };

    expect(filterNullishValues(input)).toEqual(expected);
  });

  it('should return same object if no nullish values', () => {
    const input = {
      cardId: '123',
      page: 1,
      size: 10,
      from: '2024-01-01',
      to: '2024-01-31',
      keySearch: 'abc',
      amountMin: 1000,
      amountMax: 5000,
      transType: 'TRANSFER',
      categoryCode: ['C01'],
    };

    expect(filterNullishValues(input)).toEqual(input);
  });

  it('should return empty object if all values are null or undefined', () => {
    const input = {
      cardId: null,
      page: undefined,
      size: null,
      from: undefined,
      to: null,
      keySearch: undefined,
      amountMin: null,
      amountMax: undefined,
      transType: null,
      categoryCode: undefined,
    };

    expect(filterNullishValues(input)).toEqual({});
  });
});
