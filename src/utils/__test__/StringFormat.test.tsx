import {translate} from '@locales';
import {fireEvent, render} from '@presentation/__test__/test-utils';
import React from 'react';
import {TextStyle} from 'react-native';
import {
  convertViToEn,
  formatMoney,
  formatUrl,
  generateAmountSuggestions,
  highlightText,
  highlightTextOnPress,
  isEmpty,
  isNotNullOrUndefined,
  isNullOrUndefined,
  normalizeUpperString,
  parseCurrencyToNumber,
} from '../StringFormat';

describe('formatUrl', () => {
  it('replaces placeholders with parameters', () => {
    expect(formatUrl('Hello {0}, you have {1} messages', 'Alice', 5)).toBe('Hello Alice, you have 5 messages');
  });

  it('returns original string if no placeholders', () => {
    expect(formatUrl('No placeholders here')).toBe('No placeholders here');
  });
});

describe('formatMoney', () => {
  it('formats integer amount correctly', () => {
    expect(formatMoney(1000)).toBe('1,000');
  });

  it('formats decimal amount correctly', () => {
    expect(formatMoney(1234.56)).toBe('1,234.56');
  });

  it('formats zero correctly', () => {
    expect(formatMoney(0)).toBe('0');
  });
});

describe('isNullOrUndefined', () => {
  it('returns true for null or undefined', () => {
    expect(isNullOrUndefined(null)).toBe(true);
    expect(isNullOrUndefined(undefined)).toBe(true);
  });

  it('returns false for other values', () => {
    expect(isNullOrUndefined(0)).toBe(false);
    expect(isNullOrUndefined('')).toBe(false);
    expect(isNullOrUndefined(false)).toBe(false);
  });
});

describe('isEmpty', () => {
  it('returns true for null, undefined, or empty string', () => {
    expect(isEmpty(null)).toBe(true);
    expect(isEmpty(undefined)).toBe(true);
    expect(isEmpty('')).toBe(true);
  });

  it('returns false for non-empty values', () => {
    expect(isEmpty('abc')).toBe(false);
    expect(isEmpty(0)).toBe(false);
    expect(isEmpty(false)).toBe(false);
  });
});

describe('isNotNullOrUndefined', () => {
  it('returns true for non-null and non-undefined values', () => {
    expect(isNotNullOrUndefined(1)).toBe(true);
    expect(isNotNullOrUndefined('a')).toBe(true);
    expect(isNotNullOrUndefined(false)).toBe(true);
  });

  it('returns false for null or undefined', () => {
    expect(isNotNullOrUndefined(null)).toBe(false);
    expect(isNotNullOrUndefined(undefined)).toBe(false);
  });
});

describe('normalizeUpperString', () => {
  it('returns uppercased and trimmed string', () => {
    expect(normalizeUpperString('  hello world  ')).toBe('HELLO WORLD');
  });

  it('returns empty string if input is undefined', () => {
    expect(normalizeUpperString()).toBe('');
  });
});

describe('highlightText', () => {
  it('returns original text if txtSearch is empty', () => {
    const result = highlightText('Hello world', '', {fontWeight: 'bold'}, {});
    expect(Array.isArray(result)).toBe(false);
    expect((result as React.ReactElement<any>).props.children).toBe('Hello world');
  });

  it('highlights matching text', () => {
    const result = highlightText('Hello world', 'world', {fontWeight: 'bold'}, {});
    // result is an array of <Text> elements, check their content and style
    const children = Array.isArray(result) ? result : result.props.children;
    const texts = React.Children.toArray(children) as React.ReactElement<any>[];
    expect(texts.some(child => child.props?.style && child.props.children === 'world')).toBe(true);
  });
});

describe('parseCurrencyToNumber', () => {
  it('parses US format correctly', () => {
    expect(parseCurrencyToNumber('1,234,567.89')).toBe(1234567.89);
    expect(parseCurrencyToNumber('123,456')).toBe(123456);
    expect(parseCurrencyToNumber('987.65')).toBe(987.65);
  });

  it('parses EU format correctly', () => {
    expect(parseCurrencyToNumber('1.234.567,89')).toBe(1234567.89);
    expect(parseCurrencyToNumber('123.456')).toBe(123.456);
    expect(parseCurrencyToNumber('987,65')).toBe(98765);
  });

  it('handles mixed/ambiguous cases safely', () => {
    expect(parseCurrencyToNumber('1,234')).toBe(1234);
    expect(parseCurrencyToNumber('1.234')).toBe(1.234);
    expect(parseCurrencyToNumber('1.234,00')).toBe(1234);
    expect(parseCurrencyToNumber('1,234.00')).toBe(1234.0);
  });

  it('returns 0 for invalid input', () => {
    expect(parseCurrencyToNumber('abc')).toBe(0);
    expect(parseCurrencyToNumber('')).toBe(0);
    expect(parseCurrencyToNumber(' ')).toBe(0);
    expect(parseCurrencyToNumber('1,2,3')).toBe(123); // treat as malformed US
  });

  it('handles inputs with extra whitespace', () => {
    expect(parseCurrencyToNumber('  1,234.56 ')).toBe(1234.56);
    expect(parseCurrencyToNumber('\t1.234,56\n')).toBe(1234.56);
  });
});

describe('convertViToEn', () => {
  const cases: Array<{
    description: string;
    input: string;
    expected: string;
    upper?: boolean;
  }> = [
    {
      description: 'should not affect strings without Vietnamese characters',
      input: 'hello world',
      expected: 'hello world',
    },
    {
      description: 'should handle mixed Vietnamese and non-Vietnamese content',
      input: 'điện máy Xanh - best price!',
      expected: 'dien may xanh - best price!',
    },
    {
      description: 'should convert Vietnamese characters to English equivalents',
      input: 'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễ',
      expected: 'aaaaaaaaaaaaaaaaaeeeeeeeeeee',
    },
    {
      description: 'should convert Vietnamese characters in full sentence',
      input: 'Trường đại học Bách Khoa Hà Nội',
      expected: 'truong dai hoc bach khoa ha noi',
    },
    {
      description: 'should remove Vietnamese tone combining characters',
      input: 'àáãạả', // a with combining accents
      expected: 'aaaaa',
    },
    {
      description: 'should handle input with special Vietnamese characters like đ',
      input: 'đặng thị thúy',
      expected: 'dang thi thuy',
    },
    {
      description: 'should return uppercase when toUpperCase is true',
      input: 'đặng thị thúy',
      expected: 'DANG THI THUY',
      upper: true,
    },
    {
      description: 'should handle empty string',
      input: '',
      expected: '',
    },
  ];

  cases.forEach(({description, input, expected, upper}) => {
    it(description, () => {
      expect(convertViToEn(input, upper)).toBe(expected);
    });
  });
});

describe('highlightTextOnPress', () => {
  const descStyle: TextStyle = {color: 'black'};
  const descBoldStyle: TextStyle = {fontWeight: 'bold', color: 'black'};

  it('returns original text if txtSearch is empty', () => {
    const {getByText} = render(highlightTextOnPress('Hello world', [], descBoldStyle, descStyle));
    expect(getByText('Hello world')).toBeTruthy();
  });

  it('highlights matched keyword', () => {
    const mockOnPress = jest.fn();

    const txtSearch = [{text: 'world', onPress: mockOnPress}];
    const {getByText} = render(highlightTextOnPress('Hello world', txtSearch, descBoldStyle, descStyle));

    const highlightedText = getByText('world');
    expect(JSON.stringify(highlightedText.props.style)).toContain('"fontWeight":"bold"');

    fireEvent.press(highlightedText);
    expect(mockOnPress).toHaveBeenCalled();
  });

  it('returns original text if input string is empty', () => {
    const {getByText} = render(highlightTextOnPress('', [{text: 'test'}], descBoldStyle, descStyle));
    expect(getByText('')).toBeTruthy();
  });

  it('ignores invalid txtSearch items', () => {
    const txtSearch = [{text: ''}, null as any];
    const {getByText} = render(highlightTextOnPress('Some text here', txtSearch, descBoldStyle, descStyle));
    expect(getByText('Some text here')).toBeTruthy();
  });

  it('applies normal style to unmatched text', () => {
    const txtSearch = [{text: 'world'}];
    const {getByText} = render(highlightTextOnPress('Hello world', txtSearch, descBoldStyle, descStyle));

    const normalText = getByText('Hello ');
    expect(JSON.stringify(normalText.props.style)).not.toContain('"fontWeight":"bold"');
  });

  it('handles multiple keywords correctly', () => {
    const txtSearch = [{text: 'hello'}, {text: 'world'}];
    const {getByText} = render(highlightTextOnPress('Hello there, world!', txtSearch, descBoldStyle, descStyle));

    expect(JSON.stringify(getByText('Hello').props.style)).toContain('"fontWeight":"bold"');
    expect(JSON.stringify(getByText('world').props.style)).toContain('"fontWeight":"bold"');
    expect(JSON.stringify(getByText(' there, ').props.style)).not.toContain('"fontWeight":"bold"');
  });

  it('is case-insensitive when matching', () => {
    const txtSearch = [{text: 'HELLO'}];
    const {getByText} = render(highlightTextOnPress('hello world', txtSearch, descBoldStyle, descStyle));
    expect(JSON.stringify(getByText('hello').props.style)).toContain('"fontWeight":"bold"');
  });
});

describe('generateAmountSuggestions', () => {
  const defaultSuggestions = ['100,000', '200,000', '500,000', translate('common.done')];

  const testCases = [
    {input: '0', expected: defaultSuggestions, description: 'default suggestions when input is 0'},
    {input: '', expected: defaultSuggestions, description: 'default suggestions when input is empty'},
    {input: 'abc', expected: defaultSuggestions, description: 'default suggestions when input is NaN'},

    {
      input: '3',
      expected: ['30,000', '300,000', '3,000,000', translate('common.done')],
      description: 'suggestions for 1-digit input',
    },
    {
      input: '12',
      expected: ['12,000', '120,000', '1,200,000', translate('common.done')],
      description: 'suggestions for 2-digit input',
    },
    {
      input: '123',
      expected: ['12,300', '123,000', '1,230,000', translate('common.done')],
      description: 'suggestions for 3-digit input',
    },
    {
      input: '123456',
      expected: ['1,234,560', '12,345,600', '123,456,000', translate('common.done')],
      description: 'suggestions for 6-digit input',
    },
    {
      input: '12345678',
      expected: ['123,456,780', '1,234,567,800', translate('common.done')],
      description: 'suggestions for 8-digit input',
    },
    {
      input: '1234567890',
      expected: ['123,456,780', '1,234,567,800', translate('common.done')],
      description: 'suggestions for input > 9 digits',
    },
  ];

  testCases.forEach(({input, expected}) => {
    it(`returns correct suggestions for input ${input}`, () => {
      expect(generateAmountSuggestions(input)).toEqual(expected);
    });
  });
});
