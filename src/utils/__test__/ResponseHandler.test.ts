import {BaseErrorMapper} from '@core/BaseErrorMapper';
import {BaseResponse} from '@core/BaseResponse';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {handleResponse, ResponseCodes} from '../ResponseHandler';

function createMockResponse({
  ok = true,
  status = 200,
  headers = {},
  body = '',
}: {
  ok?: boolean;
  status?: number;
  headers?: Record<string, string>;
  body?: string;
}): Response {
  return {
    ok,
    status,
    headers: {
      get: (key: string) => headers[key] || null,
    },
    clone: function () {
      return this;
    },
    text: async () => body,
  } as unknown as Response;
}

describe('handleResponse', () => {
  it('returns error when response is not ok', async () => {
    const errorResponse = {ok: false, status: 400};
    const mockResponse = createMockResponse(errorResponse);

    const result = (await handleResponse(mockResponse)) as BaseResponse<typeof errorResponse, false>;
    expect(result.success).toBe(false);
    expect(result.status).toBe(400);
    expect(result.error.code).toBe(MSBErrorCode.Default);
    expect(result.error).toBeDefined();
  });

  it('returns timeout code if gateway time-out', async () => {
    const gatewayTimeoutResponse = {
      ok: false,
      status: 504,
      body: `<html><body><h1>504 Gateway Time-out</h1>
            The server didn't respond in time.
            </body></html>
    `,
    };
    const mockResponse = createMockResponse(gatewayTimeoutResponse);

    const result = (await handleResponse(mockResponse)) as BaseResponse<typeof gatewayTimeoutResponse, false>;
    expect(result.success).toBe(false);
    expect(result.status).toBe(504);
    expect(result.error.code).toBe(MSBErrorCode.Timeout);
    expect(result.error).toBeDefined();
  });

  it('returns error when response is not ok and have error mapper', async () => {
    const errorResponse = {ok: false, status: ResponseCodes.ServerError};
    const mockResponse = createMockResponse(errorResponse);
    const mockErrorMapper: BaseErrorMapper = jest.fn().mockReturnValue('errorMapper');
    const result = (await handleResponse(mockResponse, mockErrorMapper)) as BaseResponse<typeof errorResponse, false>;
    expect(result.success).toBe(false);
    expect(result.status).toBe(ResponseCodes.ServerError);
    expect(result.error.code).toBe('errorMapper');
    expect(result.error).toBeDefined();
  });

  it('returns success true when content-length is 0', async () => {
    const mockResponse = createMockResponse({
      ok: true,
      status: 201,
      headers: {'content-length': '0'},
    });
    const result = await handleResponse(mockResponse);
    expect(result.success).toBe(true);
    expect(result.status).toBe(201);
  });

  it('returns success true when body is empty', async () => {
    const mockResponse = createMockResponse({
      ok: true,
      status: 202,
      body: '',
    });
    const result = await handleResponse(mockResponse);
    expect(result.success).toBe(true);
    expect(result.status).toBe(202);
  });

  it('returns success true when data is empty object', async () => {
    const mockResponse = createMockResponse({
      ok: true,
      status: 203,
      body: '{}',
    });
    const result = await handleResponse(mockResponse);
    expect(result.success).toBe(true);
    expect(result.status).toBe(203);
  });

  it('returns success true when data is empty array', async () => {
    const mockResponse = createMockResponse({
      ok: true,
      status: 200,
      body: '[]',
    });
    const result = await handleResponse(mockResponse);
    expect(result.success).toBe(true);
    expect(result.status).toBe(200);
  });

  it('returns success true and data when body is valid', async () => {
    const successResponse = {
      ok: true,
      status: 200,
      body: '{"foo":"bar"}',
    };
    const mockResponse = createMockResponse(successResponse);
    const result = (await handleResponse<{foo: string}>(mockResponse)) as BaseResponse<typeof successResponse, true>;
    expect(result.success).toBe(true);
    expect(result.data).toEqual({foo: 'bar'});
    expect(result.status).toBe(200);
  });

  it('returns error when JSON parse fails', async () => {
    const invalidJsonResponse = {
      ok: true,
      status: 200,
      body: '{invalid json}',
    };
    const mockResponse = createMockResponse(invalidJsonResponse);
    const result = (await handleResponse(mockResponse)) as BaseResponse<typeof invalidJsonResponse, false>;
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error.code).toBe(MSBErrorCode.Default);
    expect(result.error.message).toBe('Invalid JSON response');
  });
});

describe('handleResponse - error body parsing', () => {
  it('returns error with code/message/context extracted from JSON error body', async () => {
    const errorBody = JSON.stringify({
      errors: [
        {
          key: 'ERROR_CODE',
          message: 'Lỗi chi tiết',
          context: ['context1', 'context2'],
        },
      ],
    });
    const mockResponse = createMockResponse({
      ok: false,
      status: 400,
      body: errorBody,
    });

    const result = (await handleResponse(mockResponse)) as BaseResponse<any, false>;
    expect(result.success).toBe(false);
    expect(result.status).toBe(400);
    expect(result.error.code).toBe('ERROR_CODE');
    expect(result.error.message).toBe('Lỗi chi tiết');
    expect(result.error.name).toBe('context1 context2');
  });

  it('returns error with message as plain text if body is not JSON', async () => {
    const errorBody = 'Lỗi plain text từ server';
    const mockResponse = createMockResponse({
      ok: false,
      status: 500,
      body: errorBody,
    });

    const result = (await handleResponse(mockResponse)) as BaseResponse<any, false>;
    expect(result.success).toBe(false);
    expect(result.status).toBe(500);
    expect(result.error.message).toBe('Lỗi plain text từ server');
    expect(result.error.code).toBe(MSBErrorCode.Default);
  });

  it('returns error with empty name if context type is not string[]', async () => {
    const errorBody = JSON.stringify({
      errors: [
        {
          key: 'PARSE_ERROR_CODE',
          message: 'Lỗi chi tiết',
          context: 'context1',
        },
      ],
    });
    const mockResponse = createMockResponse({
      ok: false,
      status: 444,
      body: errorBody,
    });

    const result = (await handleResponse(mockResponse)) as BaseResponse<any, false>;
    expect(result.success).toBe(false);
    expect(result.status).toBe(444);
    expect(result.error.message).toBe('Lỗi chi tiết');
    expect(result.error.code).toBe('PARSE_ERROR_CODE');
    expect(result.error.name).toBe('');
  });
});
