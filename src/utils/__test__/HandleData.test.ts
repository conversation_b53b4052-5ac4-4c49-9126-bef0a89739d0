import {BaseResponse} from '@core/BaseResponse';
import {CustomError} from '@core/MSBCustomError';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {Left, Right} from '@core/ResultState';
import {handleData} from '../HandleData';

jest.mock('@locales', () => ({
  translate: (key: string) => `translated:${key}`,
}));

describe('handleData', () => {
  const mapper = (data: any) => ({mapped: data});

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns Left with CustomError if response is invalid', async () => {
    const response = {success: false, status: 400, error: new CustomError('ERR', 'error')} as BaseResponse;
    const request = Promise.resolve(response);

    const result = (await handleData(request, mapper)) as Left;

    expect(result.status).toBe('ERROR');
    expect(result.error).toBeInstanceOf(CustomError);
  });

  it('returns Right with mapped data if response is valid', async () => {
    const response = {success: true, status: 200, data: {foo: 'bar'}} as BaseResponse;
    const request = Promise.resolve(response);

    const result = (await handleData(request, mapper)) as Right<typeof response>;
    expect(result.status).toBe('SUCCESS');
    expect(result.data).toEqual({mapped: {foo: 'bar'}});
  });

  it('returns Left with CustomError if exception is thrown', async () => {
    const request = Promise.reject(new Error('test error'));

    const result = (await handleData(request, mapper)) as Left;

    expect(result.status).toBe('ERROR');
    expect(result.error).toBeInstanceOf(CustomError);
    expect(result.error.code).toBe(MSBErrorCode.Default);
    expect(result.error.message).toBe('translated:errors.RDB.CA.0000.title');
  });

  it('returns Left with original CustomError if exception is CustomError', async () => {
    const request = Promise.reject(new CustomError('ERR', 'custom error'));

    const result = (await handleData(request, mapper)) as Left;

    expect(result.status).toBe('ERROR');
    expect(result.error).toBeInstanceOf(CustomError);
    expect(result.error.code).toBe('ERR');
    expect(result.error.message).toBe('custom error');
  });
});
