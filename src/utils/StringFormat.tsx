import {Text} from '@components/text';
import {translate} from '@locales';
import React from 'react';
import {TextStyle} from 'react-native';

function formatUrl(template: string, ...params: (string | number)[]): string {
  return params.reduce<string>((result, param, index) => {
    const pattern = new RegExp(`\\{${index}\\}`, 'g');
    return result.toString().replace(pattern, `${param}`);
  }, template);
}

function formatMoney(amount: number) {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: amount % 1 === 0 ? 0 : 2,
    maximumFractionDigits: 2,
  }).format(amount);
}

const isNullOrUndefined = (value: unknown): boolean => {
  return value === null || value === undefined;
};

const isEmpty = (value: unknown): boolean => {
  return value === null || value === undefined || value === '';
};

function isNotNullOrUndefined<T>(value: Nullish<T>): value is T {
  return value !== null && value !== undefined;
}

export {formatMoney, formatUrl, isEmpty, isNotNullOrUndefined, isNullOrUndefined};

export const normalizeUpperString = (str?: string): string => {
  return str?.toUpperCase().trim() ?? '';
};

export const highlightText = (
  str: string,
  txtSearch: string | string[],
  descBoldStyle: TextStyle,
  descStyle: TextStyle,
) => {
  if (!txtSearch || !str) {
    return <Text style={descStyle}>{str}</Text>;
  }

  const keywords = Array.isArray(txtSearch) ? txtSearch.filter(Boolean) : [txtSearch];

  if (keywords.length === 0) {
    return <Text style={descStyle}>{str}</Text>;
  }

  // Escape regex special characters and join with `|` for OR matching
  const escapedKeywords = keywords.map(word => word.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'));
  const regex = new RegExp(`(${escapedKeywords.join('|')})`, 'gi');

  return str.split(regex).map((part, index) => {
    const matched = keywords.some(keyword => keyword.toLowerCase() === part.toLowerCase());
    return (
      <Text key={`highlight-text-${part}-${index}`} style={matched ? descBoldStyle : descStyle}>
        {part}
      </Text>
    );
  });
};

interface HighlightText {
  text: string;
  onPress?: () => void;
}

export const highlightTextOnPress = (
  str: string,
  txtSearch: HighlightText[],
  descBoldStyle: TextStyle,
  descStyle: TextStyle,
) => {
  if (!txtSearch || !str) {
    return <Text style={descStyle}>{str}</Text>;
  }

  const keywords = Array.isArray(txtSearch) ? txtSearch.filter(item => item && item.text?.trim()) : [];

  if (keywords.length === 0) {
    return <Text style={descStyle}>{str}</Text>;
  }

  const escapedKeywords = keywords.map(k => k.text.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'));
  const regex = new RegExp(`(${escapedKeywords.join('|')})`, 'gi');

  return (
    <>
      {str.split(regex).map((part, index) => {
        const keyword = keywords.find(k => k.text.toLowerCase() === part.toLowerCase());

        const isMatch = Boolean(keyword);

        return (
          <Text
            key={`highlight-text-${part}-${index}`}
            style={isMatch ? descBoldStyle : descStyle}
            onPress={isMatch ? keyword?.onPress : undefined}>
            {part}
          </Text>
        );
      })}
    </>
  );
};

export const convertViToEn = (str: string, toUpperCase = false) => {
  str = str.toLowerCase();
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư

  return toUpperCase ? str.toUpperCase() : str;
};

export const parseCurrencyToNumber = (currencyText: string): number => {
  const trimmed = currencyText.trim();

  const hasComma = trimmed.includes(',');
  const hasDot = trimmed.includes('.');

  let normalized = trimmed;

  if (hasComma && hasDot) {
    const lastComma = trimmed.lastIndexOf(',');
    const lastDot = trimmed.lastIndexOf('.');

    if (lastComma > lastDot) {
      normalized = trimmed.replace(/\./g, '').replace(',', '.');
    } else {
      normalized = trimmed.replace(/,/g, '');
    }
  } else if (hasComma && !hasDot) {
    normalized = trimmed.replace(/\./g, '').replaceAll(',', '');
  }

  const result = parseFloat(normalized);
  return isNaN(result) ? 0 : result;
};

export const generateAmountSuggestions = (input: string): string[] => {
  if (!input || input.length === 0) {
    return ['100,000', '200,000', '500,000', translate('common.done')];
  }

  const num = parseInt(input, 10);
  console.log('num', num);
  if (isNaN(num) || num === 0) {
    return ['100,000', '200,000', '500,000', translate('common.done')];
  }

  const length = input.length;
  const suggestions: string[] = [];

  // 1 => chục nghìn, trăm nghìn, triệu
  if (length === 1) {
    suggestions.push(formatMoney(num * 10_000));
    suggestions.push(formatMoney(num * 100_000));
    suggestions.push(formatMoney(num * 1_000_000));
  }
  // 12 => chục nghìn, trăm nghìn, triệu
  else if (length === 2) {
    suggestions.push(formatMoney(num * 1_000));
    suggestions.push(formatMoney(num * 10_000));
    suggestions.push(formatMoney(num * 100_000));
  }
  // 123 => chục nghìn, trăm nghìn, triệu
  else if (length === 3) {
    suggestions.push(formatMoney(num * 100));
    suggestions.push(formatMoney(num * 1_000));
    suggestions.push(formatMoney(num * 10_000));
  }
  // 1234 => chục nghìn, trăm nghìn, triệu
  // 12_345 => trăm nghìn, triệu, chục triệu
  // 123_456 => triệu, chục triệu, trăm triệu
  // 1_234_456 => chục triệu, trăm triệu, tỷ
  else if (length >= 3 && length <= 7) {
    suggestions.push(formatMoney(num * 10));
    suggestions.push(formatMoney(num * 100));
    suggestions.push(formatMoney(num * 1000));
  }
  // 12_234_456 => trăm triệu, tỷ
  else if (length >= 8 && length < 9) {
    suggestions.push(formatMoney(num * 10));
    suggestions.push(formatMoney(num * 100));
  } else {
    // chỉ lấy đến 8 số đầu tiên nếu > 9
    const maxNum = parseInt(input.slice(0, 8), 10);
    suggestions.push(formatMoney(maxNum * 10));
    suggestions.push(formatMoney(maxNum * 100));
  }

  suggestions.push(translate('common.done'));
  return suggestions;
};
