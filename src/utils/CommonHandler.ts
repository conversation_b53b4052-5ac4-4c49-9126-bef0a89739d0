import {I18nKeys, translate} from '@locales';
import {hostSharedModule} from 'msb-host-shared-module';
import {PopupExtraProps} from '../../@types/msb-host-shared-module';

interface ShowErrorPopupParams extends PopupExtraProps {
  errorCode: string;
  defaultTitle?: string;
  defaultContent?: string;
  confirmBtnText?: string;
}

export const showErrorCodePopup = ({
  errorCode,
  defaultTitle,
  defaultContent,
  confirmBtnText,
  ...props
}: ShowErrorPopupParams) => {
  hostSharedModule.d.domainService?.showPopup({
    title: translate(`errors.${errorCode}.title` as I18n<PERSON><PERSON>s, {
      defaultValue: defaultTitle ?? translate('errors.CJ-0000.title'),
    }),
    content: translate(`errors.${errorCode}.description` as I18n<PERSON>eys, {
      defaultValue: defaultContent ?? translate('errors.CJ-0000.description'),
    }),
    errorCode,
    confirmBtnText: confirmBtnText ?? translate('errors.close'),
    ...props,
  });
};
