import {BaseErrorMapper} from '@core/BaseErrorMapper';
import {BaseResponse} from '@core/BaseResponse';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {ResponseErrors} from '@core/ResponseErrors';
import {toDefinedOrDefaultValue} from '.';

export const ResponseCodes = {
  Success: 200,
  BadRequest: 400,
  NotFound: 404,
  ServerError: 500,
  TokenInvalid: 401,
  Timeout: 408,
  Permission: 403,
  InvalidUser: 102,
  UserLocked: 101,
  UserNotActive: 999,
  UserExits: 402,
  DeviceExits: 405,
  Expires: 406,
  Used: 407,
  UserProfileExits: 409,
  EmailNotNull: 410,
  DecryptFail: 411,
  Way4NotFoundCardNumber: 2007,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
};

interface ParsedError {
  parsedCode: Nullish<string>;
  parsedMessage: Nullish<string>;
  parsedContext: Nullish<string>;
}

/**
 * Represents HTTP Server Error
 * - message - string - required: Any further information
 * - key - string: Error summary
 * - errors - array[ErrorItem]: Detailed error information
 * -- message - string - required: Any further information.
 * -- key - string - required: {capability-name}.api.{api-key-name}:
 * For generated validation errors this is the path in the document the error resolves to. e.g. object name + '.' + field
 * -- context - dictionary[string, string]: Context can be anything used to construct localised messages.
 * @returns {ParsedError}
 */
export const parseErrorResponse = async (getErrorText: () => Promise<Nullish<string>>): Promise<ParsedError> => {
  let parsedCode: Nullish<string>;
  let parsedMessage: Nullish<string>;
  let parsedContext: Nullish<string>;

  let errorText = '';
  try {
    const errorTextRaw = await getErrorText();
    if (!errorTextRaw) {
      return {
        parsedCode,
        parsedMessage,
        parsedContext,
      };
    }

    errorText = errorTextRaw;
    const errorJson = JSON.parse(errorText);

    if (errorJson && typeof errorJson === 'object' && !Array.isArray(errorJson) && Object.keys(errorJson).length > 0) {
      const parsedError = (errorJson as ResponseErrors).errors?.[0];
      parsedCode = parsedError?.key;
      parsedMessage = parsedError?.message;
      parsedContext =
        parsedError?.context && Array.isArray(parsedError?.context) ? parsedError?.context?.join(' ') : '';
    }
  } catch (parseError) {
    parsedMessage = errorText || '';
    console.error('[Response error]', parseError);
  }

  return {
    parsedCode,
    parsedMessage,
    parsedContext,
  };
};

export const baseErrorMapper: BaseErrorMapper = status => {
  if (!status) {
    return MSBErrorCode.Default;
  }

  const errorCodeMapping: Record<string, string> = {
    [ResponseCodes.GatewayTimeout.toString()]: MSBErrorCode.Timeout,
  };

  return errorCodeMapping[status.toString()] || MSBErrorCode.Default;
};

export const handleResponse = async <T = unknown>(
  response: Response,
  errorMapper?: BaseErrorMapper,
): Promise<BaseResponse<T>> => {
  if (!response.ok) {
    const {parsedCode, parsedMessage, parsedContext} = await parseErrorResponse(() => response.clone().text());

    return {
      error: {
        code: errorMapper?.(response.status) ?? parsedCode ?? baseErrorMapper(response.status),
        message: toDefinedOrDefaultValue(parsedMessage, ''),
        name: toDefinedOrDefaultValue(parsedContext, ''),
      },
      success: false,
      status: response.status,
    };
  }

  const contentLength = response.headers.get('content-length');
  if (contentLength === '0') {
    return {
      success: true,
      status: response.status,
    };
  }

  const text = await response.clone().text();

  if (!text || text.trim() === '') {
    return {
      success: true,
      status: response.status,
    };
  }

  try {
    const data = JSON.parse(text);

    if (
      data === null ||
      (Array.isArray(data) && data.length === 0) ||
      (typeof data === 'object' && Object.keys(data).length === 0)
    ) {
      return {
        success: true,
        status: response.status,
      };
    }

    return {
      data,
      success: true,
      status: response.status,
    };
  } catch (parseError) {
    console.error('parse error', parseError);

    return {
      error: {
        code: MSBErrorCode.Default,
        message: 'Invalid JSON response',
        name: '',
      },
      success: false,
      status: response.status,
    };
  }
};
