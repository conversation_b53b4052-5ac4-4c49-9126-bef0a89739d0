import {Block} from '@components/block';
import {Text} from '@components/text';
import {translate} from '@locales';
import {useNavigation} from '@react-navigation/native';
import {createMSBStyleSheet, EmptyType, MSBEmptyState, MSBPage, useMSBStyles} from 'msb-shared-component';
import React from 'react';

export const ScreenLoadFailMicroApp: React.FC<FallbackComponentParams> = ({id}) => {
  const navigation = useNavigation();
  const {
    styles,
    theme: {Typography, ColorField},
  } = useMSBStyles(styleSheet);

  switch (id) {
    case 'TransferSourceAccount':
      return (
        <Block gap={4}>
          <Text type={Typography?.small_medium}>{translate('select_card_to_open.linked_payment_account')}</Text>
          <Block
            borderWidth={1}
            borderRadius={8}
            height={80}
            paddingHorizontal={16}
            justifyContent="center"
            borderColor={ColorField.BorderDefault}>
            <Text type={Typography?.base_semiBold}>{translate('error_title')}</Text>
            <Text type={Typography?.small_regular}>{translate('error_desc')}</Text>
          </Block>
        </Block>
      );
    default:
      return (
        <MSBPage testID="cm.screenLoadFailMicroApp" isScrollable={false} style={styles.defaultContainer}>
          <MSBEmptyState
            type={EmptyType.Connection}
            emptyTitle={translate('error_title')}
            emptySubTitle={translate('error_desc')}
            onConfirm={() => navigation.goBack()}
          />
        </MSBPage>
      );
  }
};

const styleSheet = createMSBStyleSheet(({SizeGlobal, ColorGlobal, Shadow}) => {
  return {
    checkingAccountsMainContainer: {
      alignItems: 'center',
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeGlobal.Size300,
      elevation: 5,
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginHorizontal: SizeGlobal.Size400,
      paddingHorizontal: SizeGlobal.Size400,
      paddingVertical: SizeGlobal.Size400,
      ...Shadow.center,
    },
    defaultContainer: {justifyContent: 'center'},
  };
});
