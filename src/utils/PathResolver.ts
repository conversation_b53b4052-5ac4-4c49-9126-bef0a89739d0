const baseUrl = process.env.NODE_ENV === 'test' ? 'https://gateway.msb.com.vn/api' : process.env.API_URL || '';

export const PathResolver = {
  common: {
    getProvinces: () => `${baseUrl}/address-book/client-api/v1/provinces`,
    getDistricts: () => `${baseUrl}/address-book/client-api/v1/provinces/{0}/districts`,
    getWards: () => `${baseUrl}/address-book/client-api/v1/provinces/{0}/districts/{1}/wards`,
    getBranches: () => `${baseUrl}/address-book/client-api/v1/branches?size=1000`,
  },

  daily: {
    getListLinkAccount: () => `${baseUrl}/arrangement-manager-extension/client-api/v1/arrangements/search`,
  },

  card: {
    getListCard: () => `${baseUrl}/card-journey/client-api/v1/cards`,
    getDetailCard: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}`,
    changeCardStatus: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/status`,
    getTransactionStatus: () => `${baseUrl}/card-journey/client-api/v1/cards/tnx/requests/{0}`,
    getCardSecretInfo: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/secret-info`,
    requestCardSecretInfo: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/secret-info/request`,
    createNewPin: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/pin`,
    getDetailHistory: () => `${baseUrl}/card-journey/client-api/v1/transaction-histories`,
    getListHistory: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/transaction-histories`,
    updateDebitAccountPayment: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/account`,
    autoDebitPayment: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/auto-debit`,
    getFilterTransactionHistory: () => `${baseUrl}/card-journey/client-api/v1/cards/{0}/histories/search`,
    getAllowedCardProducts: () => `${baseUrl}/card-journey/client-api/v1/products`,
    initializeCardOpeningFlow: () => `${baseUrl}/card-journey/client-api/v1/card-issuance`,
    getReferralInformation: () => `${baseUrl}/card-journey/client-api/v1/collaborators/{0}`,
  },
};

export const injectParams = (url: string, params: Record<string, string>) => {
  const queryParams = Object.entries(params)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  return queryParams ? `${url}?${queryParams}` : url;
};
