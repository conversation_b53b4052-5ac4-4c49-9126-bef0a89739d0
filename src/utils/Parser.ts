export const safeJSONParse = <T>(str: string | undefined | null): T | undefined => {
  try {
    if (!str) {
      return undefined;
    }

    return JSON.parse(str) as T;
  } catch (e) {
    console.log('[safeJSONParse error]:', e);
    return undefined;
  }
};

export const transformExpDate = (expDate?: string): string => {
  if (!expDate || expDate.length !== 4) {
    return '';
  }

  const month = expDate.slice(0, 2);
  const year = expDate.slice(2);

  return `${month}/${year}`;
};

/**
 * Generic utility function to get enum value from string id
 * @param id The string value to look up in the enum
 * @param enumType The enum object to search within
 * @returns The corresponding enum value or undefined if not found
 */
export function getEnumFromValue<T extends Record<string, string | number>>(
  enumType: T,
  id?: string | number | null,
): T[keyof T] | undefined {
  if (!id) {
    return;
  }

  const result = Object.keys(enumType).find(key => enumType[key as keyof typeof enumType] === id) as
    | keyof typeof enumType
    | undefined;

  return result !== undefined ? enumType[result] : undefined;
}

export function filterNullishValues<T extends Record<string, any>>(input: T): Partial<T> {
  return Object.fromEntries(
    Object.entries(input).filter(([_, value]) => value !== null && value !== undefined),
  ) as Partial<T>;
}
