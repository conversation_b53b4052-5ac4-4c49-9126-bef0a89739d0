import lodash from 'lodash';
import moment from 'moment';
import {hostSharedModule, ToastType} from 'msb-host-shared-module';
import {Platform} from 'react-native';
import {CustomIDomainService} from '../../@types/msb-host-shared-module';

export const showPopup: CustomIDomainService['showPopup'] = data => {
  hostSharedModule?.d?.domainService?.showPopup(data);
};

export const showToastMessage = (message: string, type?: ToastType) => {
  hostSharedModule?.d?.domainService?.showToast({
    message,
    type,
  });
};

export const isValidPin = (pin: string, length = 6): boolean => {
  const isSequential = (pin_: string): boolean => {
    const digits = pin_.split('').map(Number);

    const isIncreasing = digits.every((d, i, arr) => i === 0 || d === arr[i - 1] + 1);

    const isDecreasing = digits.every((d, i, arr) => i === 0 || d === arr[i - 1] - 1);

    return isIncreasing || isDecreasing;
  };

  // Regex: length, start_with # 0
  const regex = new RegExp(`^[1-9]\\d{${length - 1}}$`);
  if (!regex.test(pin)) {
    return false;
  }
  if (isSequential(pin)) {
    return false;
  }
  return true;
};

export const getDisplayDate = (transDay: string, format: string): string => {
  const today = moment();
  const transactionDate = moment(transDay, format); // hoặc định dạng phù hợp nếu khác

  if (transactionDate.isSame(today, 'day')) {
    return 'Hôm nay';
  }

  if (transactionDate.isSame(today.clone().subtract(1, 'day'), 'day')) {
    return 'Hôm qua';
  }

  return transDay;
};

export const convertDateTimeTo = (txt: string, format: string) => {
  return moment(txt).format(format);
};

export const clearFormatAmount = (amountFormatText: any) => {
  if (lodash.isNumber(amountFormatText)) {
    return amountFormatText;
  }

  if (lodash.isEmpty(amountFormatText)) {
    return '';
  }

  return amountFormatText?.replace(/,/g, '');
};

export const formatAmountForeign = (textValue: any) => {
  textValue += '';
  textValue = textValue.replace(/[^\d]/g, '');
  if (textValue.length === 2 && textValue.indexOf('0') === 0) {
    textValue = textValue.replace('0', '');
  }
  if (textValue.length > 3) {
    let temp = '';
    let lengthString = textValue.length;

    while (lengthString > 3) {
      temp = `,${textValue.substr(lengthString - 3, lengthString)}${temp}`;
      textValue = textValue.substr(0, lengthString - 3);
      lengthString = textValue.length;
    }
    temp = textValue.substr(0, lengthString) + temp;
    textValue = temp;
  }

  if (textValue === '') {
    textValue = '0';
  }

  return textValue;
};

export const formatAmountText = (text: any) => {
  if (!text) {
    return 0;
  }

  if (typeof text === 'string') {
    text = clearFormatAmount(text);
  }
  const number = `${text}`.split('.');
  if (number.length > 1) {
    if (text >= 0) {
      return `${formatAmountForeign(number[0])}.${number[1]}`;
    }
    return `-${formatAmountForeign(number[0])}.${number[1]}`;
  }
  if (text >= 0) {
    return formatAmountForeign(number[0]);
  }
  return `-${formatAmountForeign(number[0])}`;
};

export const debounceTimes = (func: Function, delay: number) => {
  let timeout: ReturnType<typeof setTimeout>;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

export const waitForRendering = (delayInMilis = 300): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, delayInMilis));
};

/**
 * Ở đây xả ra mội lỗi với react-native-modal trên Android
 * Khi mà animation timing của modal chưa complete mà activity của transaction signing được bật lên
 * Làm cho trạng thái của React Native activity inactive và animation của modal chưa complete
 * Nên xảy ra hiện tượng khi transaction signing xong thì animation mới tiếp tục được play lúc đó nội dung modal đã được reset về rỗng
 * Gây hiệu ứng nháy khi hoàn thành
 * Workaround: Ở riêng bên Android sẽ cần đợi cho animation của modal được hoàn thành rồi mới gọi callback ở onConfirm
 */
export const waitForFinishingModalAnimation = (delayInMilis = 300): Promise<void> => {
  return new Promise(resolve => (Platform.OS === 'android' ? setTimeout(resolve, delayInMilis + 50) : resolve()));
};

export function toDefinedOrDefaultValue<T>(value: Nullish<T>, defaultValue: T): T;
export function toDefinedOrDefaultValue<T>(value: Nullish<T>, defaultValue?: undefined): T | undefined;
export function toDefinedOrDefaultValue<T>(value: Nullish<T>, defaultValue?: T): T | undefined {
  return value ?? defaultValue ?? undefined;
}

/**
 *
 * Sau khi transaction signing bên iOS trả về kết quả lỗi
 * Behavior: show popup thông báo xảy ra lỗi
 * Nếu ViewController chưa được đóng hoàn toàn nhưng react-native-modal vẫn được set thuộc tính visible false
 * Làm cho có một view đè lên react native ViewController làm cho người dùng không tương tác được
 * Workaround: Ở riêng bên iOS sẽ cần đợi 300 milis rồi mới show modal có content lỗi lên
 */
export const waitForShowingModal = (delayInMilis = 300): Promise<void> => {
  return new Promise(resolve => (Platform.OS === 'ios' ? setTimeout(resolve, delayInMilis + 50) : resolve()));
};

export const propsToStyle = (arrStyle: Array<any>) => {
  return arrStyle.filter(x => x !== undefined && !Object.values(x).some(v => v === undefined));
};

/**
 * Tạo bộ quản lý loading với hai trạng thái: loadingFirst và loadingMore
 * @param setLoadingFirst Hàm setState cho trạng thái loading ban đầu
 * @param setLoadingMore Hàm setState cho trạng thái loading thêm
 * @returns Đối tượng chứa các hàm để quản lý trạng thái loading
 */
export const createLazyLoadingManager = (
  setLoadingFirst: (value: boolean) => void,
  setLoadingMore: (value: boolean) => void,
) => {
  /**
   * Hiển thị trạng thái loading phù hợp
   * @param isFirstLoad Có phải là lần tải đầu tiên không
   */
  const showLoading = (isFirstLoad: boolean) => {
    if (isFirstLoad) {
      setLoadingFirst(true);
    } else {
      setLoadingMore(true);
    }
  };

  /**
   * Ẩn trạng thái loading phù hợp
   * @param isFirstLoad Có phải là lần tải đầu tiên không
   */
  const hideLoading = (isFirstLoad: boolean) => {
    if (isFirstLoad) {
      setLoadingFirst(false);
    } else {
      setLoadingMore(false);
    }
  };

  return {
    showLoading,
    hideLoading,
  };
};

export const getDateRange = (days: number) => {
  const endDate = moment().startOf('day');
  const startDate = moment().subtract(days, 'days').startOf('day');

  return {
    startDate,
    endDate,
  };
};
