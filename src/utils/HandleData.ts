import {BaseResponse, isValidResponse} from '@core/BaseResponse';
import {CustomError} from '@core/MSBCustomError';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {makeLeft, makeRight, ResultState} from '@core/ResultState';
import {translate} from '@locales';

export const handleData = async <TEntity, TDto>(
  request: Promise<BaseResponse<TDto>>,
  mapper: (response: TDto | undefined) => TEntity,
): Promise<ResultState<TEntity>> => {
  try {
    const response = await request;

    if (!isValidResponse(response)) {
      console.error('❌❌❌❌❌❌❌❌❌❌Repository handleData Null data or response', response);
      return makeLeft(response.error);
    }

    return makeRight(mapper(response?.data));
  } catch (error) {
    console.error('❌❌❌❌❌❌❌❌❌❌Repository handleData mapper data to DTO Error', error);
    if (error instanceof CustomError) {
      return makeLeft(error);
    }

    return makeLeft(new CustomError(MSBErrorCode.Default, translate('errors.RDB.CA.0000.title')));
  }
};
