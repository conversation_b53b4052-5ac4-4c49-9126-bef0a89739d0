import I18n from 'i18n-js';
import en from './en.json';
import vi from './vi.json';

import {i18n, I18nPath} from 'msb-communication-lib';

export const translations = {en, vi};

I18n.fallbacks = true;
I18n.translations = translations;

type ResourceType = typeof translations;
export type I18nKeys = I18nPath<ResourceType[keyof ResourceType]>;
export const translate = i18n.t<I18nKeys>;

export default false ? I18n : i18n;
