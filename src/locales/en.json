{"common": {"app_version": "Module version: {{version}}", "done": "Done"}, "error_title": "Temporary interruption", "error_desc": "Please try again, we apologize for the inconvenience", "list_card": {"credit": "Credit", "debit": "Debit"}, "history_card": {"title": "Transaction history", "search": "Amount, description, min 2 characters", "no_data": "No transactions have been made", "no_data_desc": "Transaction details will be displayed here", "search_no_data": "No result found", "search_no_data_desc": "Please search with another keyword or adjust the filter"}, "card": {"active_sub_card_title": "The supplementary card has not been activated", "active_sub_card_desc": "To access all card features, please activate your card", "lock_card_title": "Card lock confirmation", "lock_card_desc": "You will not be able to perform transactions on: online channels, ATM channel, card swiping at POS etc.", "unlock_card_title": "Card unlock confirmation", "unlock_card_desc": "You’ll experience seamless and convenient payment options, including online channels, ATM channel, card swiping at POS, and more...", "unlock_card_need_call_hotline_title": "Your card is being locked", "unlock_card_need_call_hotline_desc": "Please contact hotline or visit the nearest MSB's branch for support", "confirm": "Confirm", "call_hotline": "Contact hotline", "close": "Close", "list_card_empty_title": "You have not owned a card yet", "list_card_empty_desc": "Open your MSB card now to grab tons of exclusive deals.", "discover_card_desc": "Let's check out all MSB cards and grab exlusive perks just for you", "discover_card_desc_bold": "all MSB cards", "bottom_sheet": {"card_info": "Card information", "cardholder_name": "Cardholder name", "linked_account_number": "Linked account number", "debit_account_number": "Debited account number", "card_name": "Card name", "card_number": "Card number", "issue_date": "Issued date", "expiry_date": "Expired date", "cvv_code": "CVV code"}}, "card_detail": {"card_info": {"title": "Card information", "warning": "To avoid the risk of fraud or scams, please ensure that you keep your card information secure and don't share it with anyone", "copy_card_number_success": "Copy card number successfully"}}, "detail_history_card": {"transaction_id": "Transaction ID", "transaction_channel": "Transaction channel", "money_manager": "Manage spending", "money_audit": "Reconcile"}, "slide_card": {"available": "Available", "card_limit": "Card limit", "top_bar_title": "Card", "top_bar_action": "Open card", "content_active": "Please activate card to enjoy full card features", "active_button": "Activate card"}, "detail_card": {"availableBalance": "Balance", "service": "Card services", "detail_title": "Card details", "view_more": "View more", "instrument_title": "Spending in period", "remaining_installment": "Remaining installment", "instrument_description": "Total spending on card from the last statement date to the current time", "linked_account_title": "Linked account", "virtual_type": "Virtual card", "physical_type": "Physical card", "sub_card_bottom_sheet_title": "Supplementary card list", "owner_card_name_title": "Cardholder name", "sub_card_title": "Supplementary card"}, "status_card": {"active": "In active", "inactive": "Wait to activate", "block_temp": "Temporarily locked", "expired": "Card expired", "waiting_close": "Card pending cancellation", "close": "Card locked"}, "features": {"lock": "Lock card", "payment": "Pay card", "loyalty": "Loyalty", "view_info": "View card information"}, "announcement_results": {"active_card_success": "Activate card successfully", "active_card_fail": "Failed to activate card", "active_card_success_content": "Your card has been successfully activated. We wish you a great shopping experience with MSB.", "active_card_success_content_bold_text": "has been successfully activated", "active_card_error_content": "An interruption occurred while activating card. Please try again later or contact hotline for support.", "create_new_pin_error": "An interruption occurred while creating new PIN. Please try again or contact hotline for support.", "create_new_pin_success": "Create new PIN successfully", "create_new_pin_fail": "Failed to create new PIN", "lock_card_success": "Lock card successfully", "lock_card_fail": "Failed to lock card", "unlock_card_success": "Unlock card successfully", "unlock_card_fail": "Failed to unlock card", "update_debit_payment_account_success": "Change linked account successfully", "update_debit_payment_account_error": "Failed to change linked account", "update_debit_payment_account_error_content": "An interruption occurred while changing the linked card account. Please try again later or contact hotline for support.", "auto_debit_payment_success": "Set up automatic card payment successfully", "auto_debit_payment_error": "Failed to set up automatic card payment", "auto_debit_payment_error_content": "An interruption occurred while setting up automatic card payment. Please try again or contact hotline for support.", "defaultMessage": "An interruption occurred. Please try again or contact hotline for support.", "day": "day", "active_card": "<PERSON><PERSON><PERSON> ho<PERSON> thẻ", "open_card_success": "<PERSON><PERSON><PERSON> hành thẻ thành công", "open_card_success_content": "<PERSON><PERSON> lòng <PERSON>ch hoạt thẻ để bắt đầu sử dụng và tận hưởng vô vàn ưu đãi hấp dẫn cùng MSB", "open_card_success_content_bold": "<PERSON><PERSON><PERSON> ho<PERSON> thẻ", "open_card_error": "<PERSON><PERSON><PERSON> hành thẻ thất bại", "open_card_error_content": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ.", "experience_now": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> ngay"}, "feature_card": {"lock": "Lock card", "unlock": "Unlock card", "payment": "Pay card", "loyalty": "Loyalty", "installment": "Installment", "reissue": "Reissue card", "limit": "Set limit", "pfm": "Manage spending", "statement": "Statement", "new_pin": "Create new PIN", "auto_debit": "Auto card payment", "sms": "Register SMS", "security_info": "View card information", "active": "Activate card", "renewal": "Renew card", "linked_account": "Change linked account"}, "errors": {"errorOccurred": "Please try again, we apologize for the inconveniencey", "close": "Close", "messages": {"unexpected_error_with_support_info": "Please try again or contact hotline ******** for support, we apologize for the inconvenience", "temporary_disruption": "Temporary interruption", "retry_apology": "Please try again, we apologize for the inconvenience"}, "viewSecretInfo": {"title": "Temporary interruption", "defaultMessage": "Please try again, we apologize for the inconvenience", "notFoundCardNumberMessage": "Please try again or contact hotline ******** for support, we apologize for the inconvenience", "errorCode": "Error code", "closeButton": "Close"}, "CJ-0000": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "CI-0000": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "W4-2000": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "W4-2024": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "W4-2025": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "W4-2008": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "W4-3005": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-3004": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-3003": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2036": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2022": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2039": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2014": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2001": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2034": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2006": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2002": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2003": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2033": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2005": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2037": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2009": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-2012": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "W4-400": {"description": "Please try again or contact hotline for support, we apologize for the inconvenience"}, "W4-1996": {"description": "Please try again or contact hotline for support, we apologize for the inconvenience"}, "W4-2007": {"description": "Please try again or contact hotline for support, we apologize for the inconvenience"}, "W4-500": {"description": "Please try again or contact hotline for support, we apologize for the inconvenience"}, "W4-1002": {"description": "Please try again or contact hotline for support, we apologize for the inconvenience"}, "W4-1005": {"description": "Please try again or contact hotline for support, we apologize for the inconvenience"}, "W4-203": {"description": "An interruption occurred while creating new PIN. Please try again or contact hotline for support."}, "W4-1001": {"description": "An interruption occurred while creating new PIN. Please try again or contact hotline for support."}, "W4-1003": {"description": "An interruption occurred while creating new PIN. Please try again or contact hotline for support."}, "W4-2013": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "W4--1": {"description": "An interruption occurred while issuing card. Please try again or contact hotline for support."}, "RDB": {"CA": {"0000": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "0001": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "0018": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "0019": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "0020": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "0021": {"title": "Temporary interruption", "description": "Please try again, we apologize for the inconvenience"}, "0022": {"title": "You can't use this feature right now"}, "0008": {"title": "Invalid PIN, please try again"}, "0009": {"title": "The PIN does not match, you have {{remainingAttempts}} attempts left"}, "0010": {"title": "Entered wrong PIN {{remainingAttempts}} times", "description": "Please set a new PIN to continue"}}}, "WA4": {"CA": {"0002": {"title": "{{feature}} failed", "description": "An interruption occurred while {{feature}}. Please try again or contact hotline for support."}, "0005": {"title": "{{feature}} failed", "description": "An interruption occurred while {{feature}}. Please try again or contact hotline for support."}, "0006": {"title": "{{feature}} failed", "description": "An interruption occurred while {{feature}}. Please try again or contact hotline for support."}, "0007": {"title": "Temporary interruption", "description": "Please try again or contact hotline ******** for support, we apologize for the inconvenience"}, "0017": {"title": "Failed to set up automatic card payment", "description": "An interruption occurred while setting up automatic card payment. Please try again or contact hotline for support."}, "0023": {"title": "An interruption occurred", "description": "Please try again or contact hotline ******** for support, we apologize for the inconvenience"}, "0029": {"title": "{{feature}} failed", "description": "An interruption occurred while {{feature}}. Please try again or contact hotline for support."}, "0011": {"title": "Failed to create new PIN", "description": "An interruption occurred while creating new PIN. Please try again or contact hotline for support."}, "0012": {"title": "Failed to change linked account", "description": "An interruption occurred while changing the linked card account. Please try again later or contact hotline for support."}, "0026": {"title": "An interruption occurred", "description": "Please try again or contact hotline ******** for support, we apologize for the inconvenience"}, "0027": {"title": "An interruption occurred", "description": "Please try again or contact hotline ******** for support, we apologize for the inconvenience"}, "0028": {"title": "An interruption occurred", "description": "Please try again or contact hotline ******** for support, we apologize for the inconvenience"}}}, "open_card": {"update_identification": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>t gi<PERSON>y tờ tùy thân", "messages": "Gi<PERSON>y tờ tùy thân của quý khách đã hết hạn, vui lòng kiểm tra và cập nhật lại giấy tờ tùy thân để tiếp tục sử dụng dịch vụ"}, "not_eligible_open_card": {"title": "<PERSON><PERSON><PERSON> kh<PERSON>ch chưa đủ điều kiện mở thẻ", "messages": "<PERSON><PERSON> lòng liên hệ tổng đài để được hỗ trợ"}, "invalid_payment_account": {"title": "<PERSON><PERSON><PERSON>h to<PERSON> không hợp lệ", "messages": "<PERSON><PERSON> lòng thực hiện Mở tài khoản thanh toán hoặc liên hệ tổng đài ******** để được hỗ trợ", "message_bold": "Mở tài k<PERSON>n thanh toán"}, "insufficient_balance": {"title": "<PERSON><PERSON><PERSON>h toán không đủ số dư", "messages": "<PERSON><PERSON><PERSON> khách vui lòng nạp thêm tiền vào tài khoản thanh toán để sử dụng tính năng này"}, "close": "Đ<PERSON><PERSON>", "call_hotline": "<PERSON><PERSON><PERSON> hệ tổng đài", "update": "<PERSON><PERSON><PERSON>", "open_payment_account": "Mở tài k<PERSON>n thanh toán"}}, "create_pin": {"create_new_pin": "Create new PIN", "enter_new_pin": "Enter new PIN", "pin_note": "The PIN is used for transactions at ATM and POS. Note when creating new PIN:", "pin_4_warning": "Do not set a PIN starting with 0 or consecutive numbers (e.g., 1234 or 4321)", "pin_6_warning": "Do not set a PIN starting with 0 or consecutive numbers (e.g., 123456 or 654321)", "pin_error": "Invalid PIN, please try again"}, "re_input_pin": {"title": "Create new PIN", "re_pin_error": "The PIN does not match, you have {{remainingAttempts}} attempts left", "re_pin_max_error_title": "Entered wrong PIN {{remainingAttempts}} times", "re_pin_max_error": "Please set a new PIN to continue", "please_setup_pin": "Please re-enter PIN", "yes": "Yes", "close": "Close"}, "automatic_debit_payment": {"header_title": "Auto card payment", "select_payment_level": "Choose payment level", "select_auto_payment_level_on_statement_due": "Choose automatic payment level for statement due date", "minimum_statement_balance": "Minimum statement balance", "full_statement_balance": "Full statement balance", "select_payment_account": "Select payment account", "confirm": "Confirm", "selected": "Selected", "selecting": "Selecting", "no_data": "No payment account yet", "no_data_desc": "Linked account information will be displayed here"}, "update_debit_payment_account": {"header_title": "Change linked account", "select_link_account": "Choose linked account", "linking": "Linking", "confirm": "Confirm", "default": "<PERSON><PERSON><PERSON>", "no_data": "No payment account yet", "no_data_desc": "Linked account information will be displayed here"}, "select_card_to_open": {"title": "Mở thẻ", "select_card": "<PERSON><PERSON><PERSON> thẻ", "to_love": "<PERSON><PERSON><PERSON><PERSON> y<PERSON> th<PERSON>ch", "register": "<PERSON><PERSON><PERSON> ký", "view_all": "<PERSON><PERSON> t<PERSON>t cả", "select_type": "<PERSON><PERSON>n loại thẻ phát hành", "type_digital": "Thẻ số", "type_physical": "Thẻ vật lý", "fee": "Phí", "issuing_fee": "<PERSON><PERSON> p<PERSON> h<PERSON>nh", "annual_fee": "<PERSON><PERSON> thườ<PERSON>n", "free": "<PERSON><PERSON><PERSON> phí", "highlight_offer": "Ưu đãi nổi bật", "see_more": "<PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "issuing_fee_notice": "<PERSON><PERSON> hàng sẽ thu phí phát hành thẻ. <PERSON>uý khách vui lòng đảm bảo số dư tài khoản tối thiểu bằng mức phí này.", "collapse": "<PERSON><PERSON>", "delivery_fee": "<PERSON><PERSON> giao nhận thẻ", "delivery_location_note": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đi<PERSON>", "usable_after_registration": "Sử dụng ngay sau khi đăng ký", "online_payment": "<PERSON><PERSON> to<PERSON> trực tuyến", "mobile_wallets": "Apple Pay / Google Pay / Samsung Pay", "swipe_payment": "Quẹt thẻ thanh toán trực tiếp", "atm_withdraw_other_banks": "<PERSON><PERSON><PERSON> tiền tại các ATM ngân hàng khác", "compare_cards": "So sánh thẻ", "benefit_1": "<PERSON><PERSON><PERSON> phí ph<PERSON>, phí thường niên khi mở thẻ số", "benefit_2": "Ưu đãi tới 50% tại hơn 300 cửa hàng trong Thế giới ưu đãi JOY", "benefit_3": "Chạm & thanh toán siêu nhanh khi tích hợp Ví Apple Pay, Google Pay", "button_exit": "<PERSON><PERSON><PERSON><PERSON>", "button_continue": "<PERSON><PERSON><PERSON><PERSON>", "dont_miss_deal_title": "Đừng bỏ lỡ ưu đãi hấp dẫn!", "registration_info": "Thông tin đăng ký", "linked_payment_account": "<PERSON><PERSON><PERSON>h to<PERSON> liên kết", "security_question_school": "Tên trườ<PERSON> học đầu tiên của quý khách?", "input_alphanumeric_only": "<PERSON><PERSON><PERSON><PERSON> ký tự chữ hoặc số, không dùng ký tự đặc biệt", "referral_code": "<PERSON>ã giới thiệu", "optional": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "referrer": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "branch_or_transaction_office": "Chi nhánh/PGD", "card_delivery_address": "Địa chỉ nhận thẻ", "referral_code_invalid": "<PERSON>ã giới thiệu không tồn tại trên hệ thống", "insufficient_balance": "<PERSON>hông đủ số dư tối thiểu 50,000 VND. <PERSON><PERSON> lòng chọn tài khoản khác hoặc nạp thêm tiền", "enter_security_answer": "<PERSON><PERSON><PERSON> lời câu hỏi bảo mật", "security_answer_reminder": "<PERSON><PERSON><PERSON> khách lưu ý ghi nhớ câu trả lời để có thể sử dụng trong các trường hợp khẩn cấp cần liên hệ tổng đài hỗ trợ", "security_question": "<PERSON><PERSON><PERSON> hỏi bảo mật", "insufficient_minimum_balance": "<PERSON>hông đủ số dư tối thiểu {{amount}} VND. <PERSON>ui lòng chọn tài khoản khác hoặc nạp thêm tiền"}, "card_open_note_card_usage": {"toast_title": "<PERSON><PERSON> lòng đọ<PERSON> hết để tiếp tục", "screen_title": "Mở thẻ", "note_title": "<PERSON><PERSON><PERSON> ý sử dụng thẻ", "confirmation_text": "<PERSON><PERSON><PERSON> đã đọc, hiểu và đồng ý các L<PERSON> ý sử dụng thẻ và Điều khoản & Điều kiện của sản phẩm", "terms_and_conditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n", "continue_button": "<PERSON><PERSON><PERSON><PERSON>"}, "card_open_confirm_info": {"screen_title": "Mở thẻ", "step_title": "<PERSON><PERSON><PERSON> nhận thông tin", "section_card_info": "Thông tin thẻ", "section_fee": "Phí", "info_linked_account": "<PERSON><PERSON><PERSON>h to<PERSON> liên kết", "info_name_on_card": "Họ tên in trên thẻ", "info_first_school_question": "Tên trường tiểu học đầu tiên của quý khách?", "info_delivery_address": "Địa chỉ nhận thẻ", "info_referral_code": "<PERSON>ã giới thiệu", "masked_value": "************", "agreement_product_contract": "<PERSON><PERSON>i đã đọc, hiểu và đồng ý với <PERSON>ợp đồng của sản phẩm", "agreement_product_contract_bold": "<PERSON><PERSON><PERSON>", "agreement_note_and_terms": "<PERSON><PERSON><PERSON> đã đọc, hiểu và đồng ý các L<PERSON> ý sử dụng thẻ và Điều khoản & Điều kiện của sản phẩm", "agreement_note_and_terms_bold1": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n", "agreement_note_and_terms_bold2": "<PERSON><PERSON><PERSON> ý sử dụng thẻ", "fee_issue": "<PERSON><PERSON> p<PERSON> h<PERSON>nh", "fee_annual": "<PERSON><PERSON> thườ<PERSON>n", "fee_delivery": "<PERSON><PERSON> giao nhận thẻ", "fee_free": "<PERSON><PERSON><PERSON> phí", "fee_location_based": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đi<PERSON>", "currency_vnd": "VND", "button_continue": "<PERSON><PERSON><PERSON><PERSON>"}, "card_open_issuance_contract": {"screen_title": "Mở thẻ", "contract_title": "<PERSON><PERSON><PERSON> đồng ph<PERSON>t hành", "toast_read_prompt": "<PERSON><PERSON> lòng đọ<PERSON> hết để tiếp tục", "checkbox_label": "<PERSON><PERSON><PERSON> đã đọc, hiểu và đồng ý với hợp đồng của sản phẩm", "button_continue": "<PERSON><PERSON><PERSON><PERSON>"}, "card_open_notice_drop_off": {"screen_title": "Mở thẻ", "incomplete_flow_warning": "<PERSON>ang có luồng mở thẻ chưa hoàn tất, quý khách có muốn tiếp tục ?", "final_step_description": "Chỉ còn 1 bước cuối cùng, quý khách có thể sở hữu ngay thẻ MSB với vô vàn ưu đãi hấp dẫn", "timeline_step_1": "<PERSON><PERSON><PERSON> thẻ", "timeline_step_2": "<PERSON><PERSON><PERSON><PERSON> thông tin đăng ký", "timeline_step_3": "<PERSON><PERSON><PERSON> n<PERSON>n thông tin đăng ký", "button_exit": "<PERSON><PERSON><PERSON><PERSON>", "button_continue": "<PERSON><PERSON><PERSON><PERSON>"}, "additionalInfo": {"province_or_city": "Tỉnh/Thành phố", "province_city_name": "Tên Tỉnh/Thành phố", "district": "Quận/Huyện", "select_province": "Chọn Tỉnh/Thành phố", "district_name": "<PERSON><PERSON><PERSON> quận huy<PERSON>n", "select_district": "<PERSON><PERSON><PERSON>/Huyện", "ward": "Phường/Xã", "ward_name": "<PERSON><PERSON>n ph<PERSON><PERSON>ng xã", "select_ward": "<PERSON><PERSON>n <PERSON> xã", "address": "Địa chỉ", "empty_result": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "sub_empty_result": "<PERSON>ui lòng tìm với từ khoá khác", "street_address_detail": "<PERSON><PERSON> nhà, tên tòa nhà, tên đường..."}, "transaction": {"search_title": "Search transaction history", "content_placeholder": "Enter transaction details", "search_minimum": "Please enter at least 2 characters", "type": "Transaction type", "type_all": "All", "type_in": "Cash in", "type_out": "Cash out", "time": "Time", "time_n_days": "{{n}} days", "other_time": "Other time", "from_date": "From day", "to_date": "To day", "date_format": "dd/mm/yyyy", "amount_from": "From amount", "amount_placeholder": "Enter amount", "amount_to": "To amount", "clear_filter": "Clear filter", "search": "Search", "amount_range_invalid": "\"To amount\" must be greater than or equal to \"From amount\"", "search_results_found": "{{count}} result(s) found", "empty_result": "No result found", "no_search_results_suggestion": "Please search with another keyword or adjust the filter", "enter_amount": "Enter amount"}, "history_detail": {"day": "day", "card_number": "Card number", "transaction_content": "Description", "transaction_channel": "Transaction channel", "today": "Today", "yesterday": "Yesterday"}, "asset_credit_card": {"credit_card": "Credit card", "credit_card_with_count": "Credit card ({{count}})", "no_credit_card_message": "You currently don't own any Credit card products", "open_now": "Open now", "temporary_disruption": "Temporary interruption", "reload": "Reload"}}