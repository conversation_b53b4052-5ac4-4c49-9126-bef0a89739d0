{"common": {"app_version": "<PERSON><PERSON><PERSON> bản mô-đun: {{version}}", "done": "<PERSON><PERSON>"}, "error_title": "<PERSON><PERSON> gián đoạn tạm thời", "error_desc": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này", "list_card": {"credit": "<PERSON><PERSON> d<PERSON>", "debit": "<PERSON><PERSON> n<PERSON>"}, "history_card": {"title": "<PERSON><PERSON><PERSON> sử giao dịch", "search": "<PERSON><PERSON> ti<PERSON>, <PERSON><PERSON><PERSON> dung, t<PERSON><PERSON> thiểu 2 ký tự", "no_data": "<PERSON><PERSON><PERSON> có giao dịch đ<PERSON><PERSON><PERSON> thực hi<PERSON>n", "no_data_desc": "Th<PERSON>ng tin về giao dịch sẽ hiển thị ở đây", "search_no_data": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "search_no_data_desc": "<PERSON><PERSON><PERSON> khách vui lòng điều chỉnh lại điều kiện tìm kiếm"}, "card": {"active_sub_card_title": "Thẻ phụ chưa đư<PERSON><PERSON> k<PERSON>ch ho<PERSON>t", "active_sub_card_desc": "Để sử dụng đầy đủ tính năng thẻ, quý khách vui lòng kích hoạt thẻ", "lock_card_title": "<PERSON><PERSON><PERSON> nh<PERSON>n khóa thẻ", "lock_card_desc": "<PERSON><PERSON><PERSON> khách sẽ không thể thực hiện được các giao dịch thanh toán trên: kênh trực tuyến, quẹt thẻ tại POS, giao dịch ATM...", "unlock_card_title": "<PERSON><PERSON><PERSON> nh<PERSON>n mở khóa thẻ", "unlock_card_desc": "<PERSON><PERSON><PERSON> khách sẽ được trải nghiệm các giao dịch thanh toán linh hoạt và tiện lợi, bao gồm: kênh trự<PERSON> tuyến, quẹt thẻ tại POS, giao dịch ATM...", "unlock_card_need_call_hotline_title": "Thẻ của quý khách đang bị khóa", "unlock_card_need_call_hotline_desc": "<PERSON><PERSON> lòng liên hệ tổng đài hoặc ra chi nhánh gần nhất của MSB để được hỗ trợ", "confirm": "<PERSON><PERSON><PERSON>", "call_hotline": "<PERSON><PERSON><PERSON> hệ tổng đài", "close": "Đ<PERSON><PERSON>", "list_card_empty_title": "<PERSON><PERSON><PERSON> kh<PERSON>ch chưa sở hữu thẻ", "list_card_empty_desc": "<PERSON><PERSON> lòng mở thẻ để tận hưởng vô vàn ưu đãi của MSB.", "discover_card_desc": "<PERSON><PERSON>m hiểu trọn bộ thẻ MSB cùng những ưu đãi siêu hấp dẫn dành cho quý khách", "discover_card_desc_bold": "trọn bộ thẻ MSB", "bottom_sheet": {"card_info": "Thông tin thẻ", "cardholder_name": "Họ và tên chủ thẻ", "linked_account_number": "Số tài k<PERSON>n gắn thẻ", "debit_account_number": "Số tài k<PERSON>n trích nợ", "card_name": "<PERSON><PERSON>n thẻ", "card_number": "Số thẻ", "issue_date": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> h<PERSON>nh", "expiry_date": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "cvv_code": "Mã CVV"}}, "card_detail": {"card_info": {"title": "Thông tin thẻ", "warning": "<PERSON><PERSON> tr<PERSON>h rủi ro lừa đảo hoặc gian lận, quý kh<PERSON>ch lưu ý hãy luôn bảo mật thông tin thẻ, tuyệt đối không chia sẻ thông tin thẻ cho bất kỳ ai", "copy_card_number_success": "<PERSON>o chép số thẻ thành công"}}, "detail_history_card": {"transaction_id": "Mã giao d<PERSON>ch", "transaction_channel": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "money_manager": "<PERSON><PERSON><PERSON><PERSON> lý chi tiêu", "money_audit": "<PERSON><PERSON>"}, "slide_card": {"available": "<PERSON><PERSON><PERSON> d<PERSON>", "card_limit": "<PERSON><PERSON><PERSON> mức thẻ", "top_bar_title": "Thẻ", "top_bar_action": "Mở thẻ", "content_active": "<PERSON><PERSON> lòng kích hoạt để sử dụng đầy đủ tính năng thẻ", "active_button": "<PERSON><PERSON><PERSON> ho<PERSON> thẻ"}, "detail_card": {"availableBalance": "Số dư", "service": "<PERSON><PERSON><PERSON> v<PERSON> thẻ", "detail_title": "<PERSON> tiết thẻ", "view_more": "<PERSON><PERSON>", "instrument_title": "<PERSON> tiêu trong kỳ", "remaining_installment": "<PERSON><PERSON>ả góp còn lại", "instrument_description": "Tổng số tiền chi tiêu của thẻ từ ngày chốt sao kê gần nhất đến thời điểm hiện tại", "linked_account_title": "<PERSON><PERSON><PERSON> k<PERSON>n liên kết", "virtual_type": "Thẻ số", "physical_type": "Thẻ vật lý", "sub_card_bottom_sheet_title": "<PERSON>h sách thẻ phụ", "owner_card_name_title": "Tên chủ thẻ", "sub_card_title": "Thẻ phụ"}, "status_card": {"active": "<PERSON><PERSON> ho<PERSON>t động", "inactive": "Chờ kích ho<PERSON>t", "block_temp": "<PERSON><PERSON> tạm k<PERSON>", "expired": "Thẻ hết hạn", "waiting_close": "Thẻ chờ huỷ", "close": "Thẻ bị khoá"}, "features": {"lock": "Khóa thẻ", "payment": "Thanh toán thẻ", "loyalty": "Loyalty", "view_info": "<PERSON>em thông tin thẻ"}, "announcement_results": {"active_card_success": "<PERSON><PERSON><PERSON> ho<PERSON>t thẻ thành công", "active_card_fail": "<PERSON><PERSON><PERSON> ho<PERSON>t thẻ thất bại", "active_card_success_content": "Thẻ của quý khách đã được kích hoạt thành công. Chúc quý khách có những trải nghiệm chi tiêu tuyệt vời cùng MSB.", "active_card_success_content_bold_text": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch ho<PERSON>t thành công", "active_card_error_content": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình kích hoạt thẻ. Quý khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ.", "create_new_pin_error": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình tạo mới PIN thẻ. Quý khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ.", "create_new_pin_success": "Tạo mới PIN thẻ thành công", "create_new_pin_fail": "Tạo mới PIN thẻ thất bại", "lock_card_success": "<PERSON><PERSON><PERSON> thẻ thành công", "lock_card_fail": "<PERSON><PERSON><PERSON> thẻ thất bại", "unlock_card_success": "Mở khoá thẻ thành công", "unlock_card_fail": "Mở khoá thẻ thất bại", "update_debit_payment_account_success": "<PERSON><PERSON>i tài khoản liên kết thành công", "update_debit_payment_account_error": "<PERSON><PERSON>i tài khoản liên kết thất bại", "update_debit_payment_account_error_content": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình đổi tài khoản liên kết. Qu<PERSON> khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ.", "auto_debit_payment_success": "Cài đặt trích nợ tự động thẻ thành công", "auto_debit_payment_error": "Cài đặt trích nợ tự động thẻ thất bại", "auto_debit_payment_error_content": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ, xin lỗi vì sự bất tiện này", "defaultMessage": "<PERSON>ã có lỗi không mong muốn xảy ra. Quý khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ.", "day": "ng<PERSON>y", "active_card": "<PERSON><PERSON><PERSON> ho<PERSON> thẻ", "open_card_success": "<PERSON><PERSON><PERSON> hành thẻ thành công", "open_card_success_content": "<PERSON><PERSON> lòng <PERSON>ch hoạt thẻ để bắt đầu sử dụng và tận hưởng vô vàn ưu đãi hấp dẫn cùng MSB", "open_card_success_content_bold": "<PERSON><PERSON><PERSON> ho<PERSON> thẻ", "open_card_error": "<PERSON><PERSON><PERSON> hành thẻ thất bại", "open_card_error_content": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ.", "experience_now": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> ngay"}, "feature_card": {"lock": "Khoá thẻ", "unlock": "Mở khoá", "payment": "Thanh toán thẻ", "loyalty": "Loyalty", "installment": "Trả góp", "reissue": "<PERSON><PERSON><PERSON> lại thẻ", "limit": "<PERSON>ài đặt hạn mức", "pfm": "<PERSON><PERSON><PERSON><PERSON> lý chi tiêu", "statement": "Sao kê", "new_pin": "Tạo mới PIN", "auto_debit": "<PERSON>r<PERSON><PERSON> nợ tự động", "sms": "<PERSON><PERSON><PERSON> k<PERSON>", "security_info": "<PERSON>em thông tin thẻ", "active": "<PERSON><PERSON><PERSON> ho<PERSON> thẻ", "renewal": "<PERSON><PERSON> hạn thẻ", "linked_account": "<PERSON><PERSON><PERSON> tài k<PERSON>n liên kết"}, "errors": {"errorOccurred": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại sau, xin lỗi vì sự bất tiện này", "close": "Đ<PERSON><PERSON>", "messages": {"unexpected_error_with_support_info": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc gọi tổng đài ******** để được hỗ trợ, xin lỗi vì sự bất tiện này", "temporary_disruption": "<PERSON><PERSON> gián đoạn tạm thời", "retry_apology": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "viewSecretInfo": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "defaultMessage": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này", "notFoundCardNumberMessage": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc gọi tổng đài ******** để được hỗ trợ, xin lỗi vì sự bất tiện này", "errorCode": "Mã lỗi", "closeButton": "Đ<PERSON><PERSON>"}, "CJ-0000": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "CI-0000": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "W4-2000": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "W4-2024": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "W4-2025": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "W4-2008": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "W4-3005": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-3004": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-3003": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2036": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2022": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2039": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2014": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2001": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2034": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2006": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2002": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2003": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2033": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2005": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2037": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2009": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2012": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-400": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "W4-1996": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "W4-2007": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "W4-500": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "W4-1002": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "W4-1005": {"description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "W4-203": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình tạo PIN thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-1001": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình tạo PIN thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-1003": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình tạo PIN thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "W4-2013": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "W4--1": {"description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình phát hành thẻ. Quý khách vui lòng thử lại hoặc liên hệ tổng đài để được hỗ trợ."}, "RDB": {"CA": {"0000": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "0001": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "0018": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "0019": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "0020": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "0021": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại, xin lỗi vì sự bất tiện này"}, "0022": {"title": "<PERSON>ện tại quý khách không thể sử dụng tính năng này"}, "0008": {"title": "Mã PIN không hợp lệ. <PERSON><PERSON> lòng nhập lại"}, "0009": {"title": "<PERSON>ã PIN không trùng khớp, quý khách còn {{remainingAttempts}} lần thử lại"}, "0010": {"title": "<PERSON><PERSON><PERSON><PERSON> sai mã PIN {{remainingAttempts}} lần liên tiếp", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thiết lập lại mã PIN mới"}}}, "WA4": {"CA": {"0002": {"title": "{{feature}} thất b<PERSON>i", "description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình {{feature}}. Qu<PERSON> khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ."}, "0005": {"title": "{{feature}} thất b<PERSON>i", "description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình {{feature}}. Qu<PERSON> khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ."}, "0006": {"title": "{{feature}} thất b<PERSON>i", "description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình {{feature}}. Qu<PERSON> khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ."}, "0007": {"title": "<PERSON><PERSON> gián đoạn tạm thời", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc gọi tổng đài ******** để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "0017": {"title": "Cài đặt trích nợ tự động thẻ thất bại", "description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình cài đặt trích nợ tự động thẻ. Quý khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ."}, "0023": {"title": "<PERSON><PERSON> có lỗi không mong muốn xảy ra", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc gọi tổng đài ******** để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "0029": {"title": "{{feature}} thất b<PERSON>i", "description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình {{feature}}. Qu<PERSON> khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ."}, "0011": {"title": "Tạo mới PIN thẻ thất bại", "description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình tạo mới PIN thẻ. Quý khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ."}, "0012": {"title": "Thay đổi tài khoản liên kết thẻ thất bại", "description": "<PERSON><PERSON> gián đoạn xảy ra trong quá trình thay đổi tài khoản liên kết thẻ. Qu<PERSON> khách vui lòng thử lại sau hoặc liên hệ tổng đài để được hỗ trợ."}, "0026": {"title": "<PERSON><PERSON> có lỗi không mong muốn xảy ra", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc gọi tổng đài ******** để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "0027": {"title": "<PERSON><PERSON> có lỗi không mong muốn xảy ra", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc gọi tổng đài ******** để được hỗ trợ, xin lỗi vì sự bất tiện này"}, "0028": {"title": "<PERSON><PERSON> có lỗi không mong muốn xảy ra", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thử lại hoặc gọi tổng đài ******** để được hỗ trợ, xin lỗi vì sự bất tiện này"}}}, "open_card": {"update_identification": {"title": "<PERSON><PERSON><PERSON> nh<PERSON>t gi<PERSON>y tờ tùy thân", "messages": "Gi<PERSON>y tờ tùy thân của quý khách đã hết hạn, vui lòng kiểm tra và cập nhật lại giấy tờ tùy thân để tiếp tục sử dụng dịch vụ"}, "not_eligible_open_card": {"title": "<PERSON><PERSON><PERSON> kh<PERSON>ch chưa đủ điều kiện mở thẻ", "messages": "<PERSON><PERSON> lòng liên hệ tổng đài để được hỗ trợ"}, "invalid_payment_account": {"title": "<PERSON><PERSON><PERSON>h to<PERSON> không hợp lệ", "messages": "<PERSON><PERSON> lòng thực hiện Mở tài khoản thanh toán hoặc liên hệ tổng đài ******** để được hỗ trợ", "message_bold": "Mở tài k<PERSON>n thanh toán"}, "insufficient_balance": {"title": "<PERSON><PERSON><PERSON>h toán không đủ số dư", "messages": "<PERSON><PERSON><PERSON> khách vui lòng nạp thêm tiền vào tài khoản thanh toán để sử dụng tính năng này"}, "close": "Đ<PERSON><PERSON>", "call_hotline": "<PERSON><PERSON><PERSON> hệ tổng đài", "update": "<PERSON><PERSON><PERSON>", "open_payment_account": "Mở tài k<PERSON>n thanh toán"}}, "create_pin": {"create_new_pin": "Tạo mới PIN", "enter_new_pin": "Nhập mã PIN mới", "pin_note": "Mã PIN được sử dụng để thực hiện các giao dịch trực tiếp tại ATM, POS. L<PERSON>u ý khi tạo mã PIN mới:", "pin_4_warning": "<PERSON><PERSON><PERSON><PERSON> thiết lập mã PIN bắt đầu bằng số 0 hoặc các số có thứ tự liên tiếp (VD: 1234 hoặc 4321)", "pin_6_warning": "<PERSON><PERSON><PERSON><PERSON> thiết lập mã PIN bắt đầu bằng số 0 hoặc các số có thứ tự liên tiếp (VD: 123456 hoặc 654321)", "pin_error": "Mã PIN không hợp lệ, vui lòng nhập lại"}, "re_input_pin": {"title": "Tạo mới PIN", "re_pin_error": "<PERSON>ã PIN không trùng khớp, quý khách còn {{remainingAttempts}} lần thử lại", "re_pin_max_error_title": "<PERSON><PERSON><PERSON><PERSON> sai mã PIN {{remainingAttempts}} lần liên tiếp", "re_pin_max_error": "<PERSON><PERSON><PERSON> kh<PERSON>ch vui lòng thiết lập lại mã PIN mới", "please_setup_pin": "<PERSON><PERSON> lòng nhập lại mã PIN", "yes": "Đồng ý", "close": "Đ<PERSON><PERSON>"}, "automatic_debit_payment": {"header_title": "<PERSON>r<PERSON><PERSON> nợ tự động", "select_payment_level": "<PERSON><PERSON><PERSON> mức trích nợ tự động", "select_auto_payment_level_on_statement_due": "<PERSON><PERSON> thống sẽ tự động thu nợ thẻ theo mức trích nợ mà bạn chọn khi đến kỳ sao kê", "minimum_statement_balance": "<PERSON><PERSON><PERSON> thiểu dư nợ sao kê", "full_statement_balance": "Toàn bộ dư nợ sao kê", "select_payment_account": "<PERSON><PERSON><PERSON> tài kho<PERSON>n trích nợ", "confirm": "<PERSON><PERSON><PERSON>", "selected": "<PERSON><PERSON> ch<PERSON>n", "selecting": "<PERSON><PERSON>", "no_data": "<PERSON>ưa có tài khoản trích nợ", "no_data_desc": "Thông tin về tài khoản trích nợ sẽ hiển thị ở đây"}, "update_debit_payment_account": {"header_title": "<PERSON><PERSON><PERSON> tài k<PERSON>n liên kết", "select_link_account": "<PERSON><PERSON><PERSON> tài kho<PERSON>n liên kết", "linking": "<PERSON><PERSON> li<PERSON> k<PERSON>t", "confirm": "<PERSON><PERSON><PERSON>", "default": "Mặc định", "no_data": "<PERSON><PERSON><PERSON> có tài k<PERSON>n <PERSON>h toán", "no_data_desc": "Thông tin về tài khoản liên kết sẽ hiển thị ở đây"}, "select_card_to_open": {"title": "Mở thẻ", "select_card": "<PERSON><PERSON><PERSON> thẻ", "to_love": "<PERSON><PERSON><PERSON><PERSON> y<PERSON> th<PERSON>ch", "register": "<PERSON><PERSON><PERSON> ký", "view_all": "<PERSON><PERSON> t<PERSON>t cả", "select_type": "<PERSON><PERSON>n loại thẻ phát hành", "type_digital": "Thẻ số", "type_physical": "Thẻ vật lý", "fee": "Phí", "issuing_fee": "<PERSON><PERSON> p<PERSON> h<PERSON>nh", "annual_fee": "<PERSON><PERSON> thườ<PERSON>n", "free": "<PERSON><PERSON><PERSON> phí", "highlight_offer": "Ưu đãi nổi bật", "see_more": "<PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "issuing_fee_notice": "<PERSON><PERSON> hàng sẽ thu phí phát hành thẻ. <PERSON>uý khách vui lòng đảm bảo số dư tài khoản tối thiểu bằng mức phí này.", "collapse": "<PERSON><PERSON>", "delivery_fee": "<PERSON><PERSON> giao nhận thẻ", "delivery_location_note": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đi<PERSON>", "usable_after_registration": "Sử dụng ngay sau khi đăng ký", "online_payment": "<PERSON><PERSON> to<PERSON> trực tuyến", "mobile_wallets": "Apple Pay / Google Pay / Samsung Pay", "swipe_payment": "Quẹt thẻ thanh toán trực tiếp", "atm_withdraw_other_banks": "<PERSON><PERSON><PERSON> tiền tại các ATM ngân hàng khác", "compare_cards": "So sánh thẻ", "benefit_1": "<PERSON><PERSON><PERSON> phí ph<PERSON>, phí thường niên khi mở thẻ số", "benefit_2": "Ưu đãi tới 50% tại hơn 300 cửa hàng trong Thế giới ưu đãi JOY", "benefit_3": "Chạm & thanh toán siêu nhanh khi tích hợp Ví Apple Pay, Google Pay", "button_exit": "<PERSON><PERSON><PERSON><PERSON>", "button_continue": "<PERSON><PERSON><PERSON><PERSON>", "dont_miss_deal_title": "Đừng bỏ lỡ ưu đãi hấp dẫn!", "registration_info": "Thông tin đăng ký", "linked_payment_account": "<PERSON><PERSON><PERSON>h to<PERSON> liên kết", "security_question_school": "Tên trườ<PERSON> học đầu tiên của quý khách?", "input_alphanumeric_only": "<PERSON><PERSON><PERSON><PERSON> ký tự chữ hoặc số, không dùng ký tự đặc biệt", "referral_code": "<PERSON>ã giới thiệu", "optional": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "referrer": "<PERSON><PERSON><PERSON><PERSON> giới thiệu", "branch_or_transaction_office": "Chi nhánh/PGD", "card_delivery_address": "Địa chỉ nhận thẻ", "referral_code_invalid": "<PERSON>ã giới thiệu không tồn tại trên hệ thống", "insufficient_balance": "<PERSON>hông đủ số dư tối thiểu 50,000 VND. <PERSON><PERSON> lòng chọn tài khoản khác hoặc nạp thêm tiền", "enter_security_answer": "<PERSON><PERSON><PERSON> lời câu hỏi bảo mật", "security_answer_reminder": "<PERSON><PERSON><PERSON> khách lưu ý ghi nhớ câu trả lời để có thể sử dụng trong các trường hợp khẩn cấp cần liên hệ tổng đài hỗ trợ", "security_question": "<PERSON><PERSON><PERSON> hỏi bảo mật", "insufficient_minimum_balance": "<PERSON>hông đủ số dư tối thiểu {{amount}} VND. <PERSON>ui lòng chọn tài khoản khác hoặc nạp thêm tiền"}, "card_open_note_card_usage": {"toast_title": "<PERSON><PERSON> lòng đọ<PERSON> hết để tiếp tục", "screen_title": "Mở thẻ", "note_title": "<PERSON><PERSON><PERSON> ý sử dụng thẻ", "confirmation_text": "<PERSON><PERSON><PERSON> đã đọc, hiểu và đồng ý các L<PERSON> ý sử dụng thẻ và Điều khoản & Điều kiện của sản phẩm", "terms_and_conditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n", "continue_button": "<PERSON><PERSON><PERSON><PERSON>"}, "card_open_confirm_info": {"screen_title": "Mở thẻ", "step_title": "<PERSON><PERSON><PERSON> nhận thông tin", "section_card_info": "Thông tin thẻ", "section_fee": "Phí", "info_linked_account": "<PERSON><PERSON><PERSON>h to<PERSON> liên kết", "info_name_on_card": "Họ tên in trên thẻ", "info_first_school_question": "Tên trường tiểu học đầu tiên của quý khách?", "info_delivery_address": "Địa chỉ nhận thẻ", "info_referral_code": "<PERSON>ã giới thiệu", "masked_value": "************", "agreement_product_contract": "<PERSON><PERSON>i đã đọc, hiểu và đồng ý với <PERSON>ợp đồng của sản phẩm", "agreement_product_contract_bold": "<PERSON><PERSON><PERSON>", "agreement_note_and_terms": "<PERSON><PERSON><PERSON> đã đọc, hiểu và đồng ý các L<PERSON> ý sử dụng thẻ và Điều khoản & Điều kiện của sản phẩm", "agreement_note_and_terms_bold1": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n", "agreement_note_and_terms_bold2": "<PERSON><PERSON><PERSON> ý sử dụng thẻ", "fee_issue": "<PERSON><PERSON> p<PERSON> h<PERSON>nh", "fee_annual": "<PERSON><PERSON> thườ<PERSON>n", "fee_delivery": "<PERSON><PERSON> giao nhận thẻ", "fee_free": "<PERSON><PERSON><PERSON> phí", "fee_location_based": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đi<PERSON>", "currency_vnd": "VND", "button_continue": "<PERSON><PERSON><PERSON><PERSON>"}, "card_open_issuance_contract": {"screen_title": "Mở thẻ", "contract_title": "<PERSON><PERSON><PERSON> đồng ph<PERSON>t hành", "toast_read_prompt": "<PERSON><PERSON> lòng đọ<PERSON> hết để tiếp tục", "checkbox_label": "<PERSON><PERSON><PERSON> đã đọc, hiểu và đồng ý với hợp đồng của sản phẩm", "button_continue": "<PERSON><PERSON><PERSON><PERSON>"}, "card_open_notice_drop_off": {"screen_title": "Mở thẻ", "incomplete_flow_warning": "<PERSON>ang có luồng mở thẻ chưa hoàn tất, quý khách có muốn tiếp tục ?", "final_step_description": "Chỉ còn 1 bước cuối cùng, quý khách có thể sở hữu ngay thẻ MSB với vô vàn ưu đãi hấp dẫn", "timeline_step_1": "<PERSON><PERSON><PERSON> thẻ", "timeline_step_2": "<PERSON><PERSON><PERSON><PERSON> thông tin đăng ký", "timeline_step_3": "<PERSON><PERSON><PERSON> n<PERSON>n thông tin đăng ký", "button_exit": "<PERSON><PERSON><PERSON><PERSON>", "button_continue": "<PERSON><PERSON><PERSON><PERSON>"}, "additionalInfo": {"province_or_city": "Tỉnh/Thành phố", "province_city_name": "Tên Tỉnh/Thành phố", "district": "Quận/Huyện", "select_province": "Chọn Tỉnh/Thành phố", "district_name": "<PERSON><PERSON><PERSON> quận huy<PERSON>n", "select_district": "<PERSON><PERSON><PERSON>/Huyện", "ward": "Phường/Xã", "ward_name": "<PERSON><PERSON>n ph<PERSON><PERSON>ng xã", "select_ward": "<PERSON><PERSON>n <PERSON> xã", "address": "Địa chỉ", "empty_result": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "sub_empty_result": "<PERSON>ui lòng tìm với từ khoá khác", "street_address_detail": "<PERSON><PERSON> nhà, tên tòa nhà, tên đường..."}, "transaction": {"search_title": "<PERSON><PERSON><PERSON> ki<PERSON>m lịch sử giao dịch", "content_placeholder": "<PERSON><PERSON><PERSON><PERSON> nội dung giao d<PERSON>ch", "search_minimum": "<PERSON><PERSON> lòng tìm kiếm tối thiểu 2 ký tự", "type": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "type_all": "<PERSON><PERSON><PERSON> c<PERSON>", "type_in": "Tiền vào", "type_out": "<PERSON><PERSON><PERSON><PERSON> ra", "time": "<PERSON><PERSON><PERSON><PERSON> gian", "time_n_days": "{{n}} ngày", "other_time": "<PERSON>hờ<PERSON> gian kh<PERSON>c", "from_date": "<PERSON><PERSON> ngày", "to_date": "<PERSON><PERSON><PERSON>", "date_format": "dd/mm/yyyy", "amount_from": "<PERSON><PERSON> tiền từ", "amount_placeholder": "<PERSON><PERSON><PERSON><PERSON> số tiền", "amount_to": "<PERSON><PERSON> tiền đến", "clear_filter": "Xóa bộ lọc", "search": "<PERSON><PERSON><PERSON>", "amount_range_invalid": "Số tiền đến phải lớn hơn hoặc bằng số tiền từ", "search_results_found": "C<PERSON> {{count}} kết quả đư<PERSON>c tìm thấy", "empty_result": "<PERSON><PERSON><PERSON><PERSON> có kết quả", "no_search_results_suggestion": "<PERSON>ui lòng tìm với từ khóa khác hoặc điều chỉnh lại bộ lọc", "enter_amount": "<PERSON><PERSON><PERSON><PERSON> số tiền"}, "history_detail": {"day": "ng<PERSON>y", "card_number": "Số thẻ", "transaction_content": "<PERSON><PERSON>i dung", "transaction_channel": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "today": "<PERSON><PERSON><PERSON> nay", "yesterday": "<PERSON><PERSON><PERSON> qua"}, "asset_credit_card": {"credit_card": "Thẻ tín dụng", "credit_card_with_count": "Thẻ tín dụng ({{count}})", "no_credit_card_message": "<PERSON><PERSON><PERSON> kh<PERSON>ch chưa sở hữu sản phẩm Thẻ tín dụng", "open_now": "Mở ngay", "temporary_disruption": "<PERSON><PERSON> gián đoạn tạm thời", "reload": "<PERSON><PERSON><PERSON> l<PERSON>i"}}