import RootStack from '@app-navigation/index';
import {translations} from '@locales';
import {LocaleProvider} from 'msb-communication-lib';
import {useHostInjection} from 'msb-host-shared-module';
import React from 'react';

interface CardModuleMainProps {
  flow?: CardFlow;
}

const CardModuleMain: React.FC<CardModuleMainProps> = ({flow} = {flow: 'DEFAULT'}) => {
  const {locale} = useHostInjection();
  console.log('[Card Entry Point]:', flow);

  return (
    <LocaleProvider translations={translations} defaultLocale={locale}>
      <RootStack />
    </LocaleProvider>
  );
};

export default CardModuleMain;
