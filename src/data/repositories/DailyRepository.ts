import {LINK_ACCOUNT_PAGE_SIZE} from '@constants';
import {ResultState} from '@core/ResultState';
import {mapToListLinkAccount} from '@data/mappers/daily/ListLinkAccountMapper';
import {IDailyRepository} from '@domain/repositories/IDailyRepository';
import {ListLinkAccount} from '@entities/daily/ListLinkAccount';
import {GetListLinkAccountInput} from '@use-cases/daily/GetListLinkAccountUseCase';
import {handleData} from '@utils/HandleData';
import {IDailyService} from '../datasources/IDailyService';

export class DailyRepository implements IDailyRepository {
  private readonly dailyService: IDailyService;

  constructor(dailyService: IDailyService) {
    this.dailyService = dailyService;
  }

  async getListLinkAccount(input: GetListLinkAccountInput): Promise<ResultState<ListLinkAccount>> {
    const response = this.dailyService.getListLinkAccount({
      externalProductKindIds: ['kind1'],
      externalStateIds: ['ACTIVE'],
      size: LINK_ACCOUNT_PAGE_SIZE,
      from: input.from,
      pageable: true,
      currencies: ['VND'],
    });

    return handleData(response, mapToListLinkAccount);
  }
}
