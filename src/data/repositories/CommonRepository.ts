import {ResultState} from '@core/ResultState';
import {mapToBranches} from '@data/mappers/common/BranchMapper';
import {mapToDistricts} from '@data/mappers/common/DistrictMapper';
import {mapToProvinces} from '@data/mappers/common/ProvinceMapper';
import {mapToWards} from '@data/mappers/common/WardMapper';
import {ICommonRepository} from '@domain/repositories/ICommonRepository';
import {GetDistrictsInput} from '@domain/use-cases/common/GetDistrictsUseCase';
import {GetWardsInput} from '@domain/use-cases/common/GetWardsUseCase';
import {Branch} from '@entities/common/Branch';
import {District} from '@entities/common/District';
import {Province} from '@entities/common/Province';
import {Ward} from '@entities/common/Ward';
import {handleData} from '@utils/HandleData';
import {ICommonService} from '../datasources/ICommonService';

export class CommonRepository implements ICommonRepository {
  private readonly commonService: ICommonService;

  constructor(commonService: ICommonService) {
    this.commonService = commonService;
  }

  async getProvinces(): Promise<ResultState<Province[]>> {
    return handleData(this.commonService.getProvinces(), mapToProvinces);
  }

  async getDistricts(input: GetDistrictsInput): Promise<ResultState<District[]>> {
    return handleData(this.commonService.getDistricts(input), mapToDistricts);
  }

  async getWards(input: GetWardsInput): Promise<ResultState<Ward[]>> {
    return handleData(this.commonService.getWards(input), mapToWards);
  }

  async getBranches(): Promise<ResultState<Branch[]>> {
    return handleData(this.commonService.getBranches(), mapToBranches);
  }
}
