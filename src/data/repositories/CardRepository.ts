import {isValidResponse} from '@core/BaseResponse';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {isLeft, makeLeft, makeRight, ResultState} from '@core/ResultState';
import {mapToCard, mapToCardProducts, mapToCards} from '@data/mappers/card/CardMapper';

import {mapToCardSecretInfo} from '@data/mappers/card/CardSecretInfoMapper';
import {mapToHistory, mapToListHistory} from '@data/mappers/card/HistoryMapper';
import {mapToInitOpenCard} from '@data/mappers/card/InitOpenCardMapper';
import {autoDebitPaymentErrorMapperInTransactionSigning} from '@data/mappers/transaction-signing/AutoDebitErrorMapper';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {CommonState, CommonStateStatus} from '@entities/base/CommonState';
import {Card} from '@entities/card/Card';

import {mapToReferralInformation} from '@data/mappers/card/ReferralInformationMapper';
import {ReferralInformation} from '@domain/entities/card/ReferralInformation';
import {GetDetailHistoryInput} from '@domain/use-cases/card/GetDetailHistoryUseCase';
import {GetFilterTransactionHistoryInput} from '@domain/use-cases/card/GetFilterTransactionHistoryUseCase';
import {GetReferralInformationInput} from '@domain/use-cases/common/GetReferralInformationUseCase';
import {CardSecretInfo} from '@entities/card/CardSecretInfo';
import {History, ListHistory} from '@entities/card/History';
import {InitOpenCard} from '@entities/card/InitOpenCard';
import {AutoDebitPaymentRequest} from '@models/card/AutoDebitPaymentRequest';
import {ChangeCardStatusRequest} from '@models/card/ChangeCardStatusRequest';
import {CreateNewPinRequest} from '@models/card/CreateNewPinRequest';
import {UpdateDebitAccountPaymentRequest} from '@models/card/UpdateDebitAccountPaymentRequest';
import {ConfirmationStatus, TransactionStatus} from '@models/transaction-signing/ConfirmationStatus';
import {SecretInfoRequest} from '@models/transaction-signing/SecretInfoRequest';
import {SecretPayload} from '@models/transaction-signing/SecretPayload';
import {TransactionSigning} from '@models/transaction-signing/TransactionSigning';
import {TractionSigningConfirmationStatus} from '@models/transaction-signing/TransactionSigningState';
import {AutoDebitPaymentInput} from '@use-cases/card/AutoDebitPaymentUseCase';
import {ChangeCardStatusInput} from '@use-cases/card/ChangeCardStatusUseCase';
import {CreateNewPinInput} from '@use-cases/card/CreateNewPinUseCase';
import {GetCardSecretInfoInput} from '@use-cases/card/GetCardSecretInfoUseCase';
import {GetListHistoryInput} from '@use-cases/card/GetListHistoryUseCase';
import {UpdateDebitAccountPaymentInput} from '@use-cases/card/UpdateDebitAccountPaymentUseCase';
import {handleData} from '@utils/HandleData';
import {safeJSONParse} from '@utils/Parser';
import {baseErrorMapper, parseErrorResponse} from '@utils/ResponseHandler';
import {ICardService} from '../datasources/ICardService';
import {ITransactionService} from '../datasources/ITransactionService';

export class CardRepository implements ICardRepository {
  private readonly cardService: ICardService;
  private readonly transactionService: ITransactionService;

  constructor(cardService: ICardService, transactionService: ITransactionService) {
    this.cardService = cardService;
    this.transactionService = transactionService;
  }

  async getListCard(): Promise<ResultState<Card[]>> {
    return handleData(this.cardService.getListCard(), mapToCards);
  }

  async getDetailCard(cardId: string): Promise<ResultState<Card>> {
    return handleData(this.cardService.getDetailCard({id: cardId}), mapToCard);
  }

  async changeCardStatus(input: ChangeCardStatusInput): Promise<ResultState<CommonState>> {
    let request: ChangeCardStatusRequest = new ChangeCardStatusRequest(input.cardId, input.newStatus);

    if (input.skipTransactionSigning) {
      const changeCardStatusResponse = await this.cardService.changeCardStatus(request);
      if (!isValidResponse(changeCardStatusResponse)) {
        return makeLeft(changeCardStatusResponse.error);
      }

      return makeRight({
        status: CommonStateStatus.SUCCESS,
        data: undefined,
      });
    }

    const transactionSigningEither = await this.startFlowWithTransactionSigning(request);
    if (isLeft(transactionSigningEither)) {
      return makeLeft(transactionSigningEither.error);
    }

    const secretPayload = transactionSigningEither.data;
    if (secretPayload?.status === CommonStateStatus.BACK) {
      return makeRight({
        status: CommonStateStatus.BACK,
      });
    }
    return makeRight({
      status: CommonStateStatus.SUCCESS,
      data: undefined,
    });
  }

  async getCardSecretInfo({cardId}: GetCardSecretInfoInput): Promise<ResultState<CommonState<CardSecretInfo>>> {
    let request: SecretInfoRequest = new SecretInfoRequest(cardId);
    const transactionSigningEither = await this.startFlowWithTransactionSigning(request);
    if (isLeft(transactionSigningEither)) {
      return makeLeft(transactionSigningEither.error);
    }

    const secretPayload = transactionSigningEither.data;
    if (secretPayload?.status === CommonStateStatus.BACK) {
      return makeRight({
        status: CommonStateStatus.BACK,
      });
    }

    if (!secretPayload?.data.secretKey) {
      return makeLeft({
        code: MSBErrorCode.Default,
        message: '',
        name: '',
      });
    }

    const response = await this.cardService.getCardSecretInfo({
      cardId,
      confirmationId: secretPayload.data.confirmationId,
      secretKey: secretPayload.data.secretKey,
    });

    if (!isValidResponse(response)) {
      return makeLeft(response.error);
    }

    return makeRight({
      status: CommonStateStatus.SUCCESS,
      data: mapToCardSecretInfo(response.data),
    });
  }

  async getDetailHistory(input: GetDetailHistoryInput): Promise<ResultState<History>> {
    return handleData(this.cardService.getDetailHistory(input), mapToHistory);
  }

  async getListHistory(input: GetListHistoryInput): Promise<ResultState<ListHistory>> {
    return handleData(
      this.cardService.getListHistory({
        cardId: input.cardId,
        page: input.page,
        size: input.size,
        from: input.from,
        to: input.to,
      }),
      mapToListHistory,
    );
  }

  async updateDebitAccountPayment({
    cardId,
    accountId,
  }: UpdateDebitAccountPaymentInput): Promise<ResultState<CommonState>> {
    const request = new UpdateDebitAccountPaymentRequest(cardId, accountId);

    const transactionSigningEither = await this.startFlowWithTransactionSigning(request);
    if (isLeft(transactionSigningEither)) {
      return makeLeft(transactionSigningEither.error);
    }

    const secretPayload = transactionSigningEither.data;
    if (secretPayload?.status === CommonStateStatus.BACK) {
      return makeRight({
        status: CommonStateStatus.BACK,
      });
    }
    return makeRight({
      status: CommonStateStatus.SUCCESS,
      data: undefined,
    });
  }

  async autoDebitPayment({cardId, accountId, paymentRate}: AutoDebitPaymentInput): Promise<ResultState<CommonState>> {
    const request = new AutoDebitPaymentRequest(cardId, accountId, paymentRate);

    const transactionSigningEither = await this.startFlowWithTransactionSigning(
      request,
      autoDebitPaymentErrorMapperInTransactionSigning,
    );
    if (isLeft(transactionSigningEither)) {
      return makeLeft(transactionSigningEither.error);
    }

    const secretPayload = transactionSigningEither.data;
    if (secretPayload?.status === CommonStateStatus.BACK) {
      return makeRight({
        status: CommonStateStatus.BACK,
      });
    }
    return makeRight({
      status: CommonStateStatus.SUCCESS,
      data: undefined,
    });
  }

  public async startFlowWithTransactionSigning(
    transactionSigning: TransactionSigning,
    /**
     * Mỗi flow sẽ định nghĩa một error mapper tuỳ vào URD
     * Mặc định trả về Default
     */
    errorMapper = baseErrorMapper,
  ): Promise<ResultState<CommonState<SecretPayload>>> {
    const transactionState = await this.transactionService.initTransactionSigning(transactionSigning);

    if (!isValidResponse(transactionState)) {
      return makeLeft(transactionState.error);
    }
    if (transactionState.data?.status === 'back') {
      return makeRight<CommonState<SecretPayload>>({
        status: CommonStateStatus.BACK,
      });
    }

    /**
     * Xử lý lỗi từ luồng transaction
     */
    if (transactionState.data?.status === 'error') {
      const {parsedCode, parsedMessage, parsedContext} = await parseErrorResponse(
        async () => transactionState.data?.data,
      );

      if (transactionState.data.code === MSBErrorCode.Unauthorized) {
        //When Unauthorized -> return Unauthen code to handle case
        return makeLeft({
          message: parsedMessage ?? transactionState.data.message ?? MSBErrorCode.Unauthorized,
          code: MSBErrorCode.Unauthorized,
          name: parsedContext ?? '',
        });
      }
      return makeLeft({
        message: parsedMessage ?? transactionState.data.message ?? MSBErrorCode.Default,
        code: parsedCode ?? (errorMapper ? errorMapper(transactionState.data.code) : MSBErrorCode.Default),
        name: parsedContext ?? '',
      });
    }

    const status = safeJSONParse<TractionSigningConfirmationStatus>(transactionState.data?.data);
    console.log('safeJSONParse', status);

    if (status?.confirmation_status !== ConfirmationStatus.CONFIRMED) {
      return makeRight({
        status: CommonStateStatus.BACK,
      });
    }

    const confirmationId = status?.data?.['confirmation-id'];

    if (!confirmationId) {
      return makeLeft({
        code: MSBErrorCode.Default,
        message: '',
        name: '',
      });
    }

    const confirmationStatus = await this.checkTransactionUntilCompleted(confirmationId);

    if (isLeft(confirmationStatus)) {
      return makeLeft(confirmationStatus.error);
    }

    return makeRight<CommonState<SecretPayload>>({
      status: CommonStateStatus.SUCCESS,
      data: {
        confirmationId,
        secretKey: confirmationStatus.data?.secretKey ?? undefined,
      },
    });
  }

  private async checkTransactionUntilCompleted(
    confirmationId: string,
    maxAttempts = 1,
    intervalMs = 1000,
  ): Promise<ResultState<ConfirmationStatus>> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      attempts++;

      const confirmationStatus = await this.transactionService.checkTransactionStatus({
        requestId: confirmationId,
      });

      // Check if response is valid
      if (!isValidResponse(confirmationStatus)) {
        return makeLeft(confirmationStatus.error);
      }

      console.log('[TransactionStatus]:', confirmationStatus.data);

      if (!confirmationStatus.data) {
        return makeLeft({
          code: MSBErrorCode.Default,
          message: 'Không có data trả về khi check transaction status',
          name: 'NOT_FOUND_DATA',
        });
      }

      const {transactionStatus, error} = confirmationStatus.data;

      // Case: Transaction completed successfully
      if (transactionStatus === TransactionStatus.SUCCESS) {
        return makeRight(confirmationStatus.data);
      }

      // Case: Transaction failed
      if (transactionStatus === TransactionStatus.FAIL) {
        return makeLeft({
          code: error?.errors?.[0]?.key ?? MSBErrorCode.Default,
          message: '',
          name: '',
        });
      }

      // Case: Still pending, wait before next attempt
      await new Promise(resolve => setTimeout(resolve, intervalMs));
    }

    // If exceeded max attempts, return timeout error
    return makeLeft({
      code: MSBErrorCode.Default,
      message: '',
      name: '',
    });
  }

  async createNewPin(input: CreateNewPinInput): Promise<ResultState<CommonState>> {
    let request: CreateNewPinRequest = new CreateNewPinRequest(input.cardId, input.newPin);

    const transactionSigningEither = await this.startFlowWithTransactionSigning(request);
    if (isLeft(transactionSigningEither)) {
      return makeLeft(transactionSigningEither.error);
    }

    const secretPayload = transactionSigningEither.data;
    if (secretPayload?.status === CommonStateStatus.BACK) {
      return makeRight({
        status: CommonStateStatus.BACK,
      });
    }
    return makeRight({
      status: CommonStateStatus.SUCCESS,
      data: undefined,
    });
  }

  async getFilterTransactionHistory(input: GetFilterTransactionHistoryInput): Promise<ResultState<ListHistory>> {
    return handleData(this.cardService.getFilterTransactionHistory(input), mapToListHistory);
  }

  async getAllowedCardProducts(): Promise<ResultState<Card[]>> {
    return handleData(this.cardService.getAllowedCardProducts(), mapToCardProducts);
  }

  async initializeCardOpeningFlow(): Promise<ResultState<InitOpenCard>> {
    return Promise.resolve({
      status: 'SUCCESS',
      data: mapToInitOpenCard({
        id: 'mock',
        isDropOff: false,
      }),
    });
    // return handleData(this.cardService.initializeCardOpeningFlow(), mapToInitOpenCard);
  }

  async getReferralInformation(request: GetReferralInformationInput): Promise<ResultState<ReferralInformation>> {
    return handleData(this.cardService.getReferralInformation(request), mapToReferralInformation);
  }
}
