import {MSBErrorCode} from '@core/MSBErrorCode';
import {Left, Right} from '@core/ResultState';
import {IDailyService} from '@data/datasources/IDailyService';
import {mapToListLinkAccount} from '@data/mappers/daily/ListLinkAccountMapper';
import {DIContainer} from '@di/DIContainer';
import {ListLinkAccount} from '@domain/entities/daily/ListLinkAccount';
import {jest} from '@jest/globals';
import {
  mockEmptyResponseForGetListLinkAccount,
  mockInvalidResponseForGetLinkAccount,
  mockListLinkAccountResponse,
  mockNotHaveAliasResponseForGetLinkAccount,
  mockResponseForGetLinkAccount,
  mockServerFailureForGetLinkAccount,
} from 'mocks/service-apis/get-list-link-account';
import {DailyRepository} from '../DailyRepository';

describe('DailyRepository', () => {
  let dailyService: IDailyService;
  let dailyRepository: DailyRepository;

  beforeEach(() => {
    dailyService = DIContainer.getInstance().getDailyService();
    dailyRepository = new DailyRepository(dailyService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('return error state when service has errors when getListLinkAccount', async () => {
    mockServerFailureForGetLinkAccount();

    const res = (await dailyRepository.getListLinkAccount({
      from: 0,
    })) as Left;

    expect(res.status).toBe('ERROR');
    expect(res.error.code).toBe('internal_server_error');
    expect(res.error.message).toBe('Internal Server Error');
    expect(res.error.name).toBe('context');
  });

  it('return empty state if not have response body', async () => {
    mockEmptyResponseForGetListLinkAccount();

    const res = (await dailyRepository.getListLinkAccount({
      from: 0,
    })) as Right<ListLinkAccount>;

    expect(res.status).toBe('SUCCESS');
    expect(res.data?.metadata.totalItems).toBe(0);
    expect(res.data?.data.length).toBe(0);
  });

  it('return success state when getListLinkAccount', async () => {
    mockResponseForGetLinkAccount();

    const res = (await dailyRepository.getListLinkAccount({
      from: 0,
    })) as Right<ListLinkAccount>;

    expect(res.status).toBe('SUCCESS');

    const transformData = mapToListLinkAccount(mockListLinkAccountResponse);
    expect(res.data).toEqual(transformData);
  });

  it('return success state & use bank alias as default if name does not exist', async () => {
    mockNotHaveAliasResponseForGetLinkAccount();

    const res = (await dailyRepository.getListLinkAccount({
      from: 0,
    })) as Right<ListLinkAccount>;

    expect(res.status).toBe('SUCCESS');
    expect(res.data?.metadata.totalItems).toBe(1);
    expect(res.data?.data.length).toBe(1);
    expect(res.data?.data[0].id).toBe('test-id');
    expect(res.data?.data[0].name).toBe('bankAlias');
  });

  it('return error when data is not valid', async () => {
    mockInvalidResponseForGetLinkAccount();

    const res = (await dailyRepository.getListLinkAccount({
      from: 0,
    })) as Left;

    expect(res.status).toBe('ERROR');
    expect(res.error?.code).toBe(MSBErrorCode.Default);
  });
});
