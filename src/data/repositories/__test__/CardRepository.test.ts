import {MSBErrorCode} from '@core/MSBErrorCode';
import {Left, Right} from '@core/ResultState';
import {ICardService} from '@data/datasources/ICardService';
import {ITransactionService} from '@data/datasources/ITransactionService';
import {mapToCardProducts} from '@data/mappers/card/CardMapper';
import {mapToCardSecretInfo} from '@data/mappers/card/CardSecretInfoMapper';
import {mapToListHistory} from '@data/mappers/card/HistoryMapper';
import {autoDebitPaymentErrorMapperInTransactionSigning} from '@data/mappers/transaction-signing/AutoDebitErrorMapper';
import {SecretPayload} from '@data/models/transaction-signing/SecretPayload';
import {DIContainer} from '@di/DIContainer';
import {CommonState, CommonStateStatus} from '@domain/entities/base/CommonState';
import {Card} from '@domain/entities/card/Card';
import {CardSecretInfo} from '@domain/entities/card/CardSecretInfo';
import {ListHistory} from '@domain/entities/card/History';
import {PaymentLevelSelector} from '@domain/entities/card/PaymentLevelSelector';
import {Way4CardStatusCode} from '@domain/entities/card/Way4CardStatusCode';
import {describe, jest} from '@jest/globals';
import {mockResponseForChangeStatus, mockServerFailureForChangeCardStatus} from 'mocks/service-apis/change-card-status';
import {
  mockRDBForCheckTransactionStatus,
  mockResponseForCheckTransactionStatus,
  mockResponseNotHaveSecretKeyForCheckTransactionStatus,
  mockTransactionStatusResponse,
} from 'mocks/service-apis/check-transaction-status';
import {
  mockGetAllowedCardProductsResponse,
  mockInvalidDataForGetAllowedCardProducts,
  mockResponseForGetAllowedCardProducts,
  mockServerFailureForGetAllowedCardProducts,
} from 'mocks/service-apis/get-allowed-card-products';
import {mockCardDetailResponse} from 'mocks/service-apis/get-card-details';
import {
  mockInvalidDataForGetListCard,
  mockListCardResponse,
  mockResponseForGetListCard,
  mockServerFailureForGetListCard,
} from 'mocks/service-apis/get-list-cards';
import {mockServerFailureForGetListHistory} from 'mocks/service-apis/get-list-filter-transaction';
import {
  mockEmptyResponseForGetListHistory,
  mockInvalidResponseForGetListHistory,
  mockListTransactionHistoriesResponse,
  mockPaginationResponseForGetListHistory,
} from 'mocks/service-apis/get-list-transaction';
import {
  mockResponseForGetSecretInfo,
  mockSecretInfoResponse,
  mockServerFailureForGetSecretInfo,
} from 'mocks/service-apis/get-secret-info';
import {
  mockBackTransactionSigning,
  mockErrorSigningResult,
  mockErrorTransactionSigning,
  mockTransactionSigningMissingData,
  mockTransactionSigningNotConfirmed,
  mockTransactionSigningSuccess,
  mockTransactionSigningThrowError,
} from 'mocks/transaction-signing';
import {CardRepository} from '../CardRepository';

describe('CardRepository', () => {
  let cardService: ICardService;
  let transactionService: ITransactionService;
  let cardRepository: CardRepository;

  beforeEach(() => {
    cardService = DIContainer.getInstance().getCardService();
    transactionService = DIContainer.getInstance().getTransactionService();
    cardRepository = new CardRepository(cardService, transactionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('get list card', () => {
    it('return error state if service has errors when get list card', async () => {
      mockServerFailureForGetListCard();

      const res = (await cardRepository.getListCard()) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe('internal_server_error');
      expect(res.error.message).toBe('Internal Server Error');
      expect(res.error.name).toBe('context');
    });

    it('return error state if parser has errors when get list card', async () => {
      mockInvalidDataForGetListCard();

      const res = (await cardRepository.getListCard()) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe(MSBErrorCode.Default);
      expect(res.error.message).toBe('i18n:errors.RDB.CA.0000.title');
    });

    it('return success state when get list card successfully', async () => {
      mockResponseForGetListCard();

      const res = (await cardRepository.getListCard()) as Right<Card[]>;

      expect(res.status).toBe('SUCCESS');

      // Only check length of array because we have unit test for card service & mapper
      expect(res.data?.length).toBe(mockListCardResponse.length);
    });

    it('return empty state when get list card return nullish', async () => {
      const res = (await cardRepository.getListCard()) as Right<Card[]>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.length).toBe(0);
    });
  });

  describe('get list transaction histories', () => {
    it('return error state if service returns error when getting transaction history', async () => {
      mockServerFailureForGetListHistory();

      const res = (await cardRepository.getListHistory({
        cardId: 'test-id',
        page: 1,
        size: 10,
        from: '0',
        to: '10',
      })) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe('internal_server_error');
      expect(res.error.message).toBe('Internal Server Error');
      expect(res.error.name).toBe('context');
    });

    it('return success status when getting transaction success history list (page 1)', async () => {
      mockPaginationResponseForGetListHistory();

      const res = (await cardRepository.getListHistory({
        cardId: 'test-card-id',
        page: 1,
        size: 2,
        from: '0',
        to: '10',
      })) as Right<ListHistory>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.metadata.totalItems).toBe(6);
      expect(res.data?.data.length).toBe(2);

      const transformData = mapToListHistory(mockListTransactionHistoriesResponse);
      expect(res.data?.data).toEqual(transformData.data.slice(0, 2));
    });

    it('return success state when getting transaction history list successfully (page 2)', async () => {
      mockPaginationResponseForGetListHistory();

      const res = (await cardRepository.getListHistory({
        cardId: 'test-card-id',
        page: 2,
        size: 2,
        from: '0',
        to: '10',
      })) as Right<ListHistory>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.metadata.totalItems).toBe(6);
      expect(res.data?.data.length).toBe(2);

      const transformData = mapToListHistory(mockListTransactionHistoriesResponse);
      expect(res.data?.data).toEqual(transformData.data.slice(2, 4));
    });

    it('return success state with empty list if there is no transaction history', async () => {
      mockEmptyResponseForGetListHistory();

      const res = (await cardRepository.getListHistory({
        cardId: 'test-card-id',
        page: 1,
        size: 10,
        from: '0',
        to: '10',
      })) as Right<ListHistory>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.metadata.totalItems).toBe(0);
      expect(res.data?.data.length).toBe(0);
    });

    it('return error state if parser has errors when get list card', async () => {
      mockInvalidResponseForGetListHistory();

      const res = (await cardRepository.getListHistory({
        cardId: 'test-card-id',
        page: 1,
        size: 10,
        from: '0',
        to: '10',
      })) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe(MSBErrorCode.Default);
      expect(res.error.message).toBe('i18n:errors.RDB.CA.0000.title');
    });
  });

  describe('startFlowWithTransactionSigning', () => {
    it('should return error state if transactionService.initTransactionSigning status "error"', async () => {
      mockErrorTransactionSigning();

      const res = await cardRepository.startFlowWithTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      } as any);
      expect(res.status).toBe('ERROR');
      expect((res as Left).error.code).toBe(MSBErrorCode.Default);
      expect((res as Left).error.message).toBe(mockErrorSigningResult.message);
    });

    it('should return BACK state if transactionService.initTransactionSigning returns status "back"', async () => {
      mockBackTransactionSigning();

      const res = await cardRepository.startFlowWithTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      } as any);

      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return BACK state if confirmation_status is not CONFIRMED', async () => {
      mockTransactionSigningNotConfirmed();

      const res = await cardRepository.startFlowWithTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      } as any);
      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return error state if confirmationId is missing', async () => {
      mockTransactionSigningMissingData();

      const res = await cardRepository.startFlowWithTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      } as any);
      expect(res.status).toBe('ERROR');

      const error = (res as Left).error;
      expect(error.code).toBe(MSBErrorCode.Default);
      expect(error.message).toBe('');
      expect(error.name).toBe('');
    });

    it('should return error state if checkTransactionUntilCompleted returns error', async () => {
      mockRDBForCheckTransactionStatus();
      mockTransactionSigningSuccess();

      const res = await cardRepository.startFlowWithTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      } as any);
      expect(res.status).toBe('ERROR');

      const error = (res as Left).error;
      expect(error.code).toBe(MSBErrorCode['RDB.CA.0018']);
      expect(error.message).toBe('Error from RDB');
      expect(error.name).toBe('second context');
    });

    it('has WA4 errors when check transaction status & error mapper exits', async () => {
      mockErrorTransactionSigning();

      const res = await cardRepository.startFlowWithTransactionSigning(
        {
          toRequestOrder: jest.fn().mockReturnValue(''),
        } as any,
        autoDebitPaymentErrorMapperInTransactionSigning,
      );
      expect(res.status).toBe('ERROR');

      const error = (res as Left).error;
      expect(error.code).toBe(MSBErrorCode['RDB.CA.0020']);
    });

    it('should return default error if transaction throw error', async () => {
      mockTransactionSigningThrowError();

      const res = await cardRepository.startFlowWithTransactionSigning(
        {
          toRequestOrder: jest.fn().mockReturnValue(''),
        } as any,
        autoDebitPaymentErrorMapperInTransactionSigning,
      );
      expect(res.status).toBe('ERROR');

      const error = (res as Left).error;
      expect(error.code).toBe(MSBErrorCode.Default);
      expect(error.message).toBe('Something went wrong');
    });

    it('should return SUCCESS state if everything is correct', async () => {
      mockResponseForCheckTransactionStatus();
      mockTransactionSigningSuccess();

      const res = await cardRepository.startFlowWithTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      } as any);
      expect(res.status).toBe('SUCCESS');

      const data = (res as Right<CommonState<SecretPayload>>).data;
      expect(data?.status).toBe(CommonStateStatus.SUCCESS);

      const transactionData = (data as CommonState<SecretPayload, CommonStateStatus.SUCCESS> | undefined)?.data;
      expect(transactionData?.confirmationId).toBe(mockTransactionStatusResponse.confirmationId);
      expect(transactionData?.secretKey).toBe(mockTransactionStatusResponse.secretKey);
    });
  });

  describe('get secret information', () => {
    it('should return CVV if everything is correct', async () => {
      mockResponseForGetSecretInfo();
      mockResponseForCheckTransactionStatus();
      mockTransactionSigningSuccess();

      const res = await cardRepository.getCardSecretInfo({cardId: mockCardDetailResponse['246245020'].id});
      expect(res.status).toBe('SUCCESS');

      const data = (res as Right<CommonState<CardSecretInfo>>).data;
      const transactionData = (data as CommonState<CardSecretInfo, CommonStateStatus.SUCCESS> | undefined)?.data;

      const transformData = mapToCardSecretInfo(mockSecretInfoResponse);

      expect(transactionData).toEqual(transformData);
    });

    it('should return BACK if user cancelled follow', async () => {
      mockBackTransactionSigning();

      const res = await cardRepository.getCardSecretInfo({cardId: mockCardDetailResponse['246245020'].id});
      expect(res.status).toBe('SUCCESS');

      const data = (res as Right<CommonState<CardSecretInfo, CommonStateStatus.BACK>>).data;
      expect(data?.status).toEqual(CommonStateStatus.BACK);
    });

    it('should return error if transaction signing failed', async () => {
      mockTransactionSigningSuccess();
      mockRDBForCheckTransactionStatus();

      const res = await cardRepository.getCardSecretInfo({cardId: mockCardDetailResponse['246245020'].id});
      expect(res.status).toBe('ERROR');

      const error = (res as Left).error;
      expect(error.code).toEqual(MSBErrorCode['RDB.CA.0018']);
      expect(error.message).toEqual('Error from RDB');
      expect(error.name).toEqual('second context');
    });

    it('should return default error if not return secret key', async () => {
      mockTransactionSigningSuccess();
      mockResponseNotHaveSecretKeyForCheckTransactionStatus();

      const res = await cardRepository.getCardSecretInfo({cardId: mockCardDetailResponse['246245020'].id});
      expect(res.status).toBe('ERROR');

      const error = (res as Left).error;
      expect(error.code).toEqual(MSBErrorCode.Default);
    });

    it('should return ERROR if get information failed', async () => {
      mockServerFailureForGetSecretInfo();
      mockResponseForCheckTransactionStatus();
      mockTransactionSigningSuccess();

      const res = await cardRepository.getCardSecretInfo({cardId: mockCardDetailResponse['246245020'].id});
      expect(res.status).toBe('ERROR');

      expect(res.status).toBe('ERROR');
      const error = (res as Left).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });
  });

  describe('change card status', () => {
    it('pass step transaction signing and change status successfully if no need', async () => {
      mockResponseForChangeStatus();

      const res = await cardRepository.changeCardStatus({
        cardId: 'test-id',
        newStatus: Way4CardStatusCode.CardOK,
        skipTransactionSigning: true,
      });

      expect(res.status).toBe('SUCCESS');
    });

    it('pass step transaction signing and change status failed if no need', async () => {
      mockServerFailureForChangeCardStatus();

      const res = await cardRepository.changeCardStatus({
        cardId: 'test-id',
        newStatus: Way4CardStatusCode.CardOK,
        skipTransactionSigning: true,
      });

      expect(res.status).toBe('ERROR');

      const error = (res as Left).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });

    it('should return BACK if user cancelled follow', async () => {
      mockBackTransactionSigning();
      mockResponseForChangeStatus();

      const res = await cardRepository.changeCardStatus({
        cardId: 'test-id',
        newStatus: Way4CardStatusCode.CardOK,
        skipTransactionSigning: false,
      });

      expect(res.status).toBe('SUCCESS');

      const data = (res as Right<CommonState<CardSecretInfo, CommonStateStatus.BACK>>).data;
      expect(data?.status).toEqual(CommonStateStatus.BACK);
    });
  });

  describe('update debit account payment', () => {
    it('should return error state if transactionService.initTransactionSigning status "error"', async () => {
      mockErrorTransactionSigning();

      const res = await cardRepository.updateDebitAccountPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
      });
      expect(res.status).toBe('ERROR');
      expect((res as Left).error.code).toBe(MSBErrorCode.Default);
      expect((res as Left).error.message).toBe(mockErrorSigningResult.message);
    });

    it('should return BACK state if transactionService.initTransactionSigning returns status "back"', async () => {
      mockBackTransactionSigning();

      const res = await cardRepository.updateDebitAccountPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
      });

      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return BACK state if confirmation_status is not CONFIRMED', async () => {
      mockTransactionSigningNotConfirmed();

      const res = await cardRepository.updateDebitAccountPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
      });
      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return SUCCESS state if everything is correct', async () => {
      mockResponseForCheckTransactionStatus();
      mockTransactionSigningSuccess();

      const res = await cardRepository.updateDebitAccountPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
      });
      expect(res.status).toBe('SUCCESS');

      const data = (res as Right<CommonState<SecretPayload>>).data;
      expect(data?.status).toBe(CommonStateStatus.SUCCESS);
    });
  });

  describe('set auto debit payment', () => {
    it('should return error state if transactionService.initTransactionSigning status "error"', async () => {
      mockErrorTransactionSigning();

      const res = await cardRepository.autoDebitPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
        paymentRate: PaymentLevelSelector.Fullpayment,
      });
      expect(res.status).toBe('ERROR');
      expect((res as Left).error.code).toBe(MSBErrorCode['RDB.CA.0020']);
      expect((res as Left).error.message).toBe(mockErrorSigningResult.message);
    });

    it('should return BACK state if transactionService.initTransactionSigning returns status "back"', async () => {
      mockBackTransactionSigning();

      const res = await cardRepository.autoDebitPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
        paymentRate: PaymentLevelSelector.Fullpayment,
      });

      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return BACK state if confirmation_status is not CONFIRMED', async () => {
      mockTransactionSigningNotConfirmed();

      const res = await cardRepository.autoDebitPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
        paymentRate: PaymentLevelSelector.Fullpayment,
      });
      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return SUCCESS state if everything is correct', async () => {
      mockResponseForCheckTransactionStatus();
      mockTransactionSigningSuccess();

      const res = await cardRepository.autoDebitPayment({
        cardId: 'test-id',
        accountId: 'account-test-id',
        paymentRate: PaymentLevelSelector.Fullpayment,
      });
      expect(res.status).toBe('SUCCESS');

      const data = (res as Right<CommonState<SecretPayload>>).data;
      expect(data?.status).toBe(CommonStateStatus.SUCCESS);
    });
  });

  describe('create a new pin', () => {
    it('should return error state if transactionService.initTransactionSigning status "error"', async () => {
      mockErrorTransactionSigning();

      const res = await cardRepository.createNewPin({
        cardId: 'test-id',
        newPin: '122112',
      });
      expect(res.status).toBe('ERROR');
      expect((res as Left).error.code).toBe(MSBErrorCode.Default);
      expect((res as Left).error.message).toBe(mockErrorSigningResult.message);
    });

    it('should return BACK state if transactionService.initTransactionSigning returns status "back"', async () => {
      mockBackTransactionSigning();

      const res = await cardRepository.createNewPin({
        cardId: 'test-id',
        newPin: '122112',
      });

      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return BACK state if confirmation_status is not CONFIRMED', async () => {
      mockTransactionSigningNotConfirmed();

      const res = await cardRepository.createNewPin({
        cardId: 'test-id',
        newPin: '122112',
      });
      expect(res.status).toBe('SUCCESS');
      expect((res as Right<CommonState<SecretPayload>>).data?.status).toBe(CommonStateStatus.BACK);
    });

    it('should return SUCCESS state if everything is correct', async () => {
      mockResponseForCheckTransactionStatus();
      mockTransactionSigningSuccess();

      const res = await cardRepository.createNewPin({
        cardId: 'test-id',
        newPin: '122112',
      });
      expect(res.status).toBe('SUCCESS');

      const data = (res as Right<CommonState<SecretPayload>>).data;
      expect(data?.status).toBe(CommonStateStatus.SUCCESS);
    });
  });

  describe('get allowed card products', () => {
    it('return error state if service has errors', async () => {
      mockServerFailureForGetAllowedCardProducts();

      const res = (await cardRepository.getAllowedCardProducts()) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe('internal_server_error');
      expect(res.error.message).toBe('Internal Server Error');
      expect(res.error.name).toBe('context');
    });

    it('return error state if parser has errors', async () => {
      mockInvalidDataForGetAllowedCardProducts();

      const res = (await cardRepository.getAllowedCardProducts()) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe(MSBErrorCode.Default);
      expect(res.error.message).toBe('i18n:errors.RDB.CA.0000.title');
    });

    it('return success state successfully', async () => {
      mockResponseForGetAllowedCardProducts();

      const res = (await cardRepository.getAllowedCardProducts()) as Right<Card[]>;

      expect(res.status).toBe('SUCCESS');

      expect(
        res.data?.map(card => ({
          ...card,
          id: undefined,
        })),
      ).toEqual(
        mapToCardProducts(mockGetAllowedCardProductsResponse).map(card => ({
          ...card,
          id: undefined,
        })),
      );
    });

    it('return empty state when get list card return nullish', async () => {
      const res = (await cardRepository.getAllowedCardProducts()) as Right<Card[]>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.length).toBe(0);
    });
  });
});
