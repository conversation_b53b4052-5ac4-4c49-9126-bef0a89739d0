import {MSBErrorCode} from '@core/MSBErrorCode';
import {Left, Right} from '@core/ResultState';
import {ICommonService} from '@data/datasources/ICommonService';
import {mapToDistricts} from '@data/mappers/common/DistrictMapper';
import {mapToProvinces} from '@data/mappers/common/ProvinceMapper';
import {mapToWards} from '@data/mappers/common/WardMapper';
import {DIContainer} from '@di/DIContainer';
import {District} from '@domain/entities/common/District';
import {Province} from '@domain/entities/common/Province';
import {Ward} from '@domain/entities/common/Ward';
import {jest} from '@jest/globals';
import {
  mockGetDistrictsResponse,
  mockInvalidDataForGetDistricts,
  mockResponseForGetDistricts,
  mockServerFailureForGetDistricts,
} from 'mocks/service-apis/get-districts';
import {
  mockGetProvincesResponse,
  mockInvalidDataForGetProvinces,
  mockResponseForGetProvinces,
  mockServerFailureForGetProvinces,
} from 'mocks/service-apis/get-provinces';
import {
  mockGetWardsResponse,
  mockInvalidDataForGetWards,
  mockResponseForGetWards,
  mockServerFailureForGetWards,
} from 'mocks/service-apis/get-wards';
import {CommonRepository} from '../CommonRepository';

describe('CommonRepository', () => {
  let commonService: ICommonService;
  let commonRepository: CommonRepository;

  beforeEach(() => {
    commonService = DIContainer.getInstance().getCommonService();
    commonRepository = new CommonRepository(commonService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('get provinces', () => {
    it('return error state if service has errors', async () => {
      mockServerFailureForGetProvinces();

      const res = (await commonRepository.getProvinces()) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe('internal_server_error');
      expect(res.error.message).toBe('Internal Server Error');
      expect(res.error.name).toBe('context');
    });

    it('return error state if parser has errors', async () => {
      mockInvalidDataForGetProvinces();

      const res = (await commonRepository.getProvinces()) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe(MSBErrorCode.Default);
      expect(res.error.message).toBe('i18n:errors.RDB.CA.0000.title');
    });

    it('return success state successfully', async () => {
      mockResponseForGetProvinces();

      const res = (await commonRepository.getProvinces()) as Right<Province[]>;

      expect(res.status).toBe('SUCCESS');

      expect(res.data).toEqual(mapToProvinces(mockGetProvincesResponse));
    });

    it('return empty state when get list return nullish', async () => {
      const res = (await commonRepository.getProvinces()) as Right<Province[]>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.length).toBe(0);
    });
  });

  describe('get districts', () => {
    it('return error state if service has errors', async () => {
      mockServerFailureForGetDistricts();

      const res = (await commonRepository.getDistricts({
        provinceId: '1',
      })) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe('internal_server_error');
      expect(res.error.message).toBe('Internal Server Error');
      expect(res.error.name).toBe('context');
    });

    it('return error state if parser has errors', async () => {
      mockInvalidDataForGetDistricts();

      const res = (await commonRepository.getDistricts({
        provinceId: '1',
      })) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe(MSBErrorCode.Default);
      expect(res.error.message).toBe('i18n:errors.RDB.CA.0000.title');
    });

    it('return success state successfully xx', async () => {
      mockResponseForGetDistricts();

      const res = (await commonRepository.getDistricts({
        provinceId: '1',
      })) as Right<District[]>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data).toEqual(mapToDistricts(mockGetDistrictsResponse));
    });

    it('return empty state when get list return nullish', async () => {
      const res = (await commonRepository.getDistricts({
        provinceId: '1',
      })) as Right<District[]>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.length).toBe(0);
    });
  });

  describe('get wards', () => {
    it('return error state if service has errors', async () => {
      mockServerFailureForGetWards();

      const res = (await commonRepository.getWards({
        provinceId: '1',
        districtId: '268',
      })) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe('internal_server_error');
      expect(res.error.message).toBe('Internal Server Error');
      expect(res.error.name).toBe('context');
    });

    it('return error state if parser has errors', async () => {
      mockInvalidDataForGetWards();

      const res = (await commonRepository.getWards({
        provinceId: '1',
        districtId: '268',
      })) as Left;

      expect(res.status).toBe('ERROR');
      expect(res.error.code).toBe(MSBErrorCode.Default);
      expect(res.error.message).toBe('i18n:errors.RDB.CA.0000.title');
    });

    it('return success state successfully', async () => {
      mockResponseForGetWards();

      const res = (await commonRepository.getWards({
        provinceId: '1',
        districtId: '268',
      })) as Right<Ward[]>;

      expect(res.status).toBe('SUCCESS');

      expect(res.data).toEqual(mapToWards(mockGetWardsResponse));
    });

    it('return empty state when get list return nullish', async () => {
      const res = (await commonRepository.getWards({
        provinceId: '1',
        districtId: '268',
      })) as Right<Ward[]>;

      expect(res.status).toBe('SUCCESS');
      expect(res.data?.length).toBe(0);
    });
  });
});
