import {PathResolver} from '@utils/PathResolver';
import {formatUrl} from '@utils/StringFormat';
import {RequestOrder} from 'msb-host-shared-module';
import {TransactionSigning} from '../transaction-signing/TransactionSigning';

export class SecretInfoRequest extends TransactionSigning {
  cardId: string;

  constructor(cardId: string) {
    super();
    this.cardId = cardId;
  }

  toRequestOrder(): RequestOrder {
    return {
      url: `${formatUrl(PathResolver.card.requestCardSecretInfo(), this.cardId)}`,
      method: 'POST',
    };
  }
}
