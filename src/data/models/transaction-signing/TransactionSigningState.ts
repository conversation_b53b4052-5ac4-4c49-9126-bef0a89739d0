export interface ConfirmationData {
  'confirmation-id'?: string | null;
  'confirmation-type'?: string | null;
  'confirmation-flow-external-id'?: string | null;
}

export interface TractionSigningConfirmationStatus {
  confirmation_status?: string | null;
  data?: ConfirmationData | null;
}

export interface TransactionSigningState {
  status?: 'success' | 'error' | 'back' | null;
  data?: string | null;
  code?: string | null;
  message?: string | null;
}
