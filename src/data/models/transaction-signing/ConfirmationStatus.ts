import {ResponseErrors} from '@core/ResponseErrors';

export const TransactionStatus = {
  INIT: 'INIT',
  PENDING: 'PENDING',
  PROCESS: 'PROCESS',
  SUCCESS: 'SUCCESS',
  FAIL: 'FAIL',
};

export const ConfirmationStatus = {
  CONFIRMED: 'confirmed',
};

export interface ConfirmationStatus {
  secretKey?: string | null;
  transactionStatus?: string | null;
  confirmationStatus?: string | null;
  confirmationId?: string | null;
  error?: ResponseErrors | null;
}
