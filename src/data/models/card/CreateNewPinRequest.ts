import {PathResolver} from '@utils/PathResolver';
import {formatUrl} from '@utils/StringFormat';
import {RequestOrder} from 'msb-host-shared-module';
import {TransactionSigning} from '../transaction-signing/TransactionSigning';

export class CreateNewPinRequest extends TransactionSigning {
  cardId: string;
  newPin: string;

  constructor(cardId: string, newPin: string) {
    super();
    this.cardId = cardId;
    this.newPin = newPin;
  }

  toRequestOrder(): RequestOrder {
    return {
      url: `${formatUrl(PathResolver.card.createNewPin(), this.cardId)}`,
      method: 'PUT',
      body: JSON.stringify({newPin: this.newPin}),
    };
  }
}
