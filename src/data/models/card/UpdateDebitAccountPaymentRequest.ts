import {PathResolver} from '@utils/PathResolver';
import {formatUrl} from '@utils/StringFormat';
import {RequestOrder} from 'msb-host-shared-module';
import {TransactionSigning} from '../transaction-signing/TransactionSigning';

export class UpdateDebitAccountPaymentRequest extends TransactionSigning {
  cardId: string;
  accountId: string;

  constructor(cardId: string, accountId: string) {
    super();
    this.cardId = cardId;
    this.accountId = accountId;
  }

  toRequestOrder(): RequestOrder {
    return {
      url: formatUrl(PathResolver.card.updateDebitAccountPayment(), this.cardId),
      method: 'PATCH',
      body: JSON.stringify({accountId: this.accountId}),
    };
  }
}
