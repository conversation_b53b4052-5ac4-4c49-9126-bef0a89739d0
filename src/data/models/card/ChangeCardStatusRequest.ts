import {Way4CardStatusCode} from '@entities/card/Way4CardStatusCode';
import {PathResolver} from '@utils/PathResolver';
import {formatUrl} from '@utils/StringFormat';
import {RequestOrder} from 'msb-host-shared-module';
import {TransactionSigning} from '../transaction-signing/TransactionSigning';

export class ChangeCardStatusRequest extends TransactionSigning {
  cardId: string;
  newStatus: Way4CardStatusCode;
  skipTransactionSigning?: boolean;

  constructor(cardId: string, newStatus: Way4CardStatusCode, skipTransactionSigning = false) {
    super();
    this.cardId = cardId;
    this.newStatus = newStatus;
    this.skipTransactionSigning = skipTransactionSigning;
  }

  toRequestOrder(): RequestOrder {
    return {
      url: formatUrl(PathResolver.card.changeCardStatus(), this.cardId),
      method: 'PATCH',
      body: JSON.stringify({newStatus: this.newStatus}),
    };
  }
}
