import {AdditionDTO} from './AdditionDTO';
import {BenefitDTO} from './BenefitDTO';
import {CardFeatureDTO} from './CardFeatureDTO';
import {CardHolderDTO} from './CardHolderDTO';
import {CardVisualDTO} from './CardVisualDTO';
import {SubCardDTO} from './SubCardDTO';

export type CardDTO = {
  id?: string | null;
  brand?: string | null;
  type?: string | null;
  name?: string | null;
  status?: string | null;
  ownership?: string | null;
  holder?: CardHolderDTO | null;
  currency?: string | null;
  maskedNumber?: string | null;
  cardVisual?: CardVisualDTO | null;
  instrument?: string | null;
  productCode?: string | null;
  productId?: string | null;
  availableBalance?: number | null;
  creditLimit?: number | null;
  rbsNumber?: string | null;
  features?: CardFeatureDTO[];
  spentAmount?: number | null;
  remainingInstallments?: number | null;
  additions?: AdditionDTO | null;
  subCards?: SubCardDTO[] | null;
  paymentRate?: string | null;
  physicalAvailable?: number | null;
  virtualAvailable?: number | null;
  benefits?: BenefitDTO[] | null;
};

export type ListCardProductDTO = {
  totalRecord?: number | null;
  cards?: CardDTO[] | null;
};
