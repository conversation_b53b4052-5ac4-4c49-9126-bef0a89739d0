import {PaymentLevelSelector} from '@entities/card/PaymentLevelSelector';
import {PathResolver} from '@utils/PathResolver';
import {formatUrl} from '@utils/StringFormat';
import {RequestOrder} from 'msb-host-shared-module';
import {TransactionSigning} from '../transaction-signing/TransactionSigning';

export class AutoDebitPaymentRequest extends TransactionSigning {
  cardId: string;
  accountId: string;
  paymentRate: PaymentLevelSelector;

  constructor(cardId: string, accountId: string, paymentRate: PaymentLevelSelector) {
    super();
    this.cardId = cardId;
    this.accountId = accountId;
    this.paymentRate = paymentRate;
  }

  toRequestOrder(): RequestOrder {
    return {
      url: `${formatUrl(PathResolver.card.autoDebitPayment(), this.cardId)}`,
      method: 'PATCH',
      body: JSON.stringify({accountId: this.accountId, paymentRate: this.paymentRate}),
    };
  }
}
