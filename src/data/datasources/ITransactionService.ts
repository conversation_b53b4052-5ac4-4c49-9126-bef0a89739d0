import {BaseResponse} from '@core/BaseResponse';
import {CheckTransactionStatusRequest} from '@models/transaction-signing/CheckTransactionStatusRequest';
import {ConfirmationStatus} from '@models/transaction-signing/ConfirmationStatus';
import {TransactionSigning} from '@models/transaction-signing/TransactionSigning';
import {TransactionSigningState} from '@models/transaction-signing/TransactionSigningState';

export interface ITransactionService {
  initTransactionSigning: (request: TransactionSigning) => Promise<BaseResponse<TransactionSigningState>>;
  checkTransactionStatus: (request: CheckTransactionStatusRequest) => Promise<BaseResponse<ConfirmationStatus>>;
}
