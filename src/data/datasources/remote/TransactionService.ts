import {BaseResponse} from '@core/BaseResponse';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {CheckTransactionStatusRequest} from '@models/transaction-signing/CheckTransactionStatusRequest';
import {ConfirmationStatus} from '@models/transaction-signing/ConfirmationStatus';
import {TransactionSigning} from '@models/transaction-signing/TransactionSigning';
import {TransactionSigningState} from '@models/transaction-signing/TransactionSigningState';
import {PathResolver} from '@utils/PathResolver';
import {handleResponse, ResponseCodes} from '@utils/ResponseHandler';
import {formatUrl} from '@utils/StringFormat';
import {hostSharedModule, IHttpClient} from 'msb-host-shared-module';
import {ITransactionService} from '../ITransactionService';

export class TransactionService implements ITransactionService {
  constructor(private readonly httpClient: IHttpClient) {}

  initTransactionSigning = async (request: TransactionSigning): Promise<BaseResponse<TransactionSigningState>> => {
    try {
      const data = request.toRequestOrder();

      const state = await (hostSharedModule.d.domainService?.onTransfer(data) as Promise<TransactionSigningState>);
      console.log('🚀 ~ TransactionServices ~ initTransactionSigning= ~ state:', state);

      return {
        success: true,
        data: state,
        status: ResponseCodes.Success,
      };
    } catch (error) {
      console.log('initTransactionSigning', error);

      return {
        success: false,
        error: {
          code: MSBErrorCode.Default,
          message: error instanceof Error ? error.message : '',
          name: '',
        },
        status: ResponseCodes.BadRequest,
      };
    }
  };

  checkTransactionStatus = async (
    request: CheckTransactionStatusRequest,
  ): Promise<BaseResponse<ConfirmationStatus>> => {
    const url = PathResolver.card.getTransactionStatus();
    const response = await this.httpClient.get(formatUrl(url, request?.requestId));
    return handleResponse(response);
  };
}
