import {BaseResponse} from '@core/BaseResponse';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {ICardService} from '@data/datasources/ICardService';
import {CardDTO, ListCardProductDTO} from '@data/models/card/CardDTO';
import {CardSecretInfoDTO} from '@data/models/card/CardSecretInfoDTO';
import {ChangeCardStatusRequest} from '@data/models/card/ChangeCardStatusRequest';
import {CreateNewPinRequest} from '@data/models/card/CreateNewPinRequest';
import {GetCardSecretInfoRequest} from '@data/models/card/GetCardSecretInfoRequest';
import {GetListHistoryRequest} from '@data/models/card/GetListHistoryRequest';
import {GetListHistoryResponse} from '@data/models/card/GetListHistoryResponse';
import {DIContainer} from '@di/DIContainer';
import {Way4CardStatusCode} from '@domain/entities/card/Way4CardStatusCode';
import {mockResponseForChangeStatus, mockServerFailureForChangeCardStatus} from 'mocks/service-apis/change-card-status';
import {mockResponseForCreateNewPin, mockServerFailureForCreateNewPin} from 'mocks/service-apis/create-new-pin';
import {
  mockGetAllowedCardProductsResponse,
  mockResponseForGetAllowedCardProducts,
  mockServerFailureForGetAllowedCardProducts,
} from 'mocks/service-apis/get-allowed-card-products';
import {
  mockCardDetailResponse,
  mockResponseForGetCardDetail,
  mockServerFailureForGetDetailCard,
} from 'mocks/service-apis/get-card-details';
import {
  mockResponseForGetListCard,
  mockServerFailureForGetListCard,
  mocResponseOnlyDebitForGetListCard,
} from 'mocks/service-apis/get-list-cards';
import {mockServerFailureForGetListHistory} from 'mocks/service-apis/get-list-filter-transaction';
import {
  mockListTransactionHistoriesResponse,
  mockPaginationResponseForGetListHistory,
} from 'mocks/service-apis/get-list-transaction';
import {
  mockResponseForGetSecretInfo,
  mockSecretInfoResponse,
  mockWA4ErrorsForGetCardSecretInfo,
} from 'mocks/service-apis/get-secret-info';
import {mockServerFailureForGetDetailHistory} from 'mocks/service-apis/get-transaction-details';

describe('CardService', () => {
  let cardService: ICardService;

  beforeEach(() => {
    cardService = DIContainer.getInstance().getCardService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getListCard', () => {
    it('should return error when API call fails', async () => {
      mockServerFailureForGetListCard();
      const res = await cardService.getListCard();
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<CardDTO[], false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });

    it('should get list of cards successfully', async () => {
      mockResponseForGetListCard();
      const res = await cardService.getListCard();
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<CardDTO[], true>).data;
      expect(Array.isArray(data)).toBe(true);
      expect(data!.length).toBeGreaterThan(0);
      expect(data![0]).toHaveProperty('id');
      expect(data![0]).toHaveProperty('name');
    });

    it('should get only debit cards successfully', async () => {
      mocResponseOnlyDebitForGetListCard();
      const res = await cardService.getListCard();
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<CardDTO[], true>).data;
      expect(Array.isArray(data)).toBe(true);
      expect(data!.every(card => card.type === 'DEBIT')).toBe(true);
    });
  });

  describe('getDetailCard', () => {
    it('should return error when API call fails', async () => {
      mockServerFailureForGetDetailCard();
      const res = await cardService.getDetailCard({id: 'test-id'});
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<CardDTO, false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });

    it('should get card detail successfully', async () => {
      mockResponseForGetCardDetail();
      const cardId = '246245020';
      const res = await cardService.getDetailCard({id: cardId});
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<CardDTO, true>).data;
      expect(data).toEqual({
        ...mockCardDetailResponse[cardId],
        id: cardId,
      });
    });
  });

  describe('changeCardStatus when skip transaction signing', () => {
    it('should get card detail successfully', async () => {
      mockResponseForChangeStatus();
      const res = await cardService.changeCardStatus(
        new ChangeCardStatusRequest('test-id', Way4CardStatusCode.AccountClosed),
      );
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);
    });

    it('should return error when API call fails', async () => {
      mockServerFailureForChangeCardStatus();
      const res = await cardService.changeCardStatus(
        new ChangeCardStatusRequest('test-id', Way4CardStatusCode.AccountClosed),
      );

      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<any, false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });
  });

  describe('get secret info', () => {
    it('should create successfully', async () => {
      mockResponseForGetSecretInfo();
      const request: GetCardSecretInfoRequest = {
        cardId: 'test-id',
        confirmationId: 'confirmationId',
        secretKey: 'secretKey',
      };
      const res = await cardService.getCardSecretInfo(request);
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<CardSecretInfoDTO, true>).data;
      expect(data).toEqual(mockSecretInfoResponse);
    });

    it('has WA4 errors when getCardSecretInfo', async () => {
      mockWA4ErrorsForGetCardSecretInfo();

      const request: GetCardSecretInfoRequest = {
        cardId: 'test-id',
        confirmationId: 'confirmationId',
        secretKey: 'secretKey',
      };

      const res = await cardService.getCardSecretInfo(request);
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<any, false>).error;
      expect(error.code).toBe(MSBErrorCode['WA4.CA.0017']);
      expect(error.message).toBe('Error from WA4');
      expect(error.name).toBe('first context');
    });
  });

  describe('create new pin', () => {
    it('should create successfully', async () => {
      mockResponseForCreateNewPin();
      const res = await cardService.createNewPin(new CreateNewPinRequest('test-id', '123456'));
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);
    });

    it('has errors when createNewPin', async () => {
      mockServerFailureForCreateNewPin();

      const res = await cardService.createNewPin(new CreateNewPinRequest('test-id', '123456'));
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);
    });
  });

  it('has errors when getDetailHistory', async () => {
    mockServerFailureForGetDetailHistory();
    const res = await cardService.getDetailHistory({cardId: 'test-id'});
    expect(res.success).toBe(false);
    expect(res.status).toBe(500);

    const error = (res as BaseResponse<any, false>).error;
    expect(error.code).toBe('internal_server_error');
    expect(error.message).toBe('Internal Server Error');
    expect(error.name).toBe('context');
  });

  describe('getListHistory', () => {
    it('should get list history successfully if page 1', async () => {
      mockPaginationResponseForGetListHistory();

      const request: GetListHistoryRequest = {cardId: 'test-card-id', page: 1, size: 2, from: '0', to: '10'};
      const res = await cardService.getListHistory(request);

      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<GetListHistoryResponse, true>).data;
      expect(data).toBeDefined();
      expect(data?.totalRecord).toBe(6);
      expect(Array.isArray(data?.transactionHistories)).toBe(true);
      expect(data?.transactionHistories?.length).toBe(2);
      expect(data?.transactionHistories?.[1]).toEqual(mockListTransactionHistoriesResponse.transactionHistories[1]);
    });

    it('should get list history successfully if page 2', async () => {
      mockPaginationResponseForGetListHistory();

      const request: GetListHistoryRequest = {cardId: 'test-card-id', page: 2, size: 2, from: '0', to: '10'};
      const res = await cardService.getListHistory(request);

      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<GetListHistoryResponse, true>).data;
      expect(data).toBeDefined();
      expect(data?.totalRecord).toBe(6);
      expect(Array.isArray(data?.transactionHistories)).toBe(true);
      expect(data?.transactionHistories?.length).toBe(2);
      expect(data?.transactionHistories?.[0]).toEqual(mockListTransactionHistoriesResponse.transactionHistories[2]);
      expect(data?.transactionHistories?.[1]).toEqual(mockListTransactionHistoriesResponse.transactionHistories[3]);
    });

    it('should return error when API call fails', async () => {
      const request: GetListHistoryRequest = {cardId: 'test-id', page: 1, size: 10, from: '0', to: '10'};
      mockServerFailureForGetListHistory();
      const res = await cardService.getListHistory(request);
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<any, false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });
  });

  describe('getAllowedCardProducts', () => {
    it('should return card product list when API succeeds', async () => {
      mockResponseForGetAllowedCardProducts();

      const res = await cardService.getAllowedCardProducts();

      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<ListCardProductDTO, true>).data;
      expect(data).toBeDefined();
      expect(data?.totalRecord).toBe(mockGetAllowedCardProductsResponse.totalRecord);
      expect(Array.isArray(data?.cards)).toBe(true);
      expect(data?.cards?.length).toBe(mockGetAllowedCardProductsResponse.totalRecord);
      expect(data?.cards?.[1]).toEqual(mockGetAllowedCardProductsResponse.cards[1]);
    });

    it('should return error when API call fails', async () => {
      mockServerFailureForGetAllowedCardProducts();
      const res = await cardService.getAllowedCardProducts();
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<any, false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });
  });
});
