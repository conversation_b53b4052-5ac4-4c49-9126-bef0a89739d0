import {BaseResponse} from '@core/BaseResponse';
import {ICommonService} from '@data/datasources/ICommonService';
import {ProvinceDTO} from '@data/models/common/ProvinceDTO';
import {DIContainer} from '@di/DIContainer';
import {
  mockGetDistrictsResponse,
  mockResponseForGetDistricts,
  mockServerFailureForGetDistricts,
} from 'mocks/service-apis/get-districts';
import {
  mockGetProvincesResponse,
  mockResponseForGetProvinces,
  mockServerFailureForGetProvinces,
} from 'mocks/service-apis/get-provinces';
import {
  mockGetWardsResponse,
  mockResponseForGetWards,
  mockServerFailureForGetWards,
} from 'mocks/service-apis/get-wards';

describe('CommonService', () => {
  let commonService: ICommonService;

  beforeEach(() => {
    commonService = DIContainer.getInstance().getCommonService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getProvinces', () => {
    it('should return error when API call fails', async () => {
      mockServerFailureForGetProvinces();
      const res = await commonService.getProvinces();
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<ProvinceDTO[], false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });

    it('should get list of province successfully', async () => {
      mockResponseForGetProvinces();
      const res = await commonService.getProvinces();
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<ProvinceDTO[], true>).data;
      expect(Array.isArray(data)).toBe(true);
      expect(data!.length).toBe(mockGetProvincesResponse.length);
      expect(data![0]).toHaveProperty('id');
      expect(data![0]).toHaveProperty('name');
    });
  });

  describe('getDistricts', () => {
    it('should return error when API call fails', async () => {
      mockServerFailureForGetDistricts();
      const res = await commonService.getDistricts({
        provinceId: '1',
      });
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<ProvinceDTO[], false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });

    it('should get list of province successfully', async () => {
      mockResponseForGetDistricts();
      const res = await commonService.getDistricts({
        provinceId: '1',
      });
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<ProvinceDTO[], true>).data;
      expect(Array.isArray(data)).toBe(true);
      expect(data!.length).toBe(mockGetDistrictsResponse.length);
      expect(data![0]).toHaveProperty('id');
      expect(data![0]).toHaveProperty('name');
    });
  });

  describe('getWards', () => {
    it('should return error when API call fails', async () => {
      mockServerFailureForGetWards();
      const res = await commonService.getWards({
        provinceId: '1',
        districtId: '268',
      });
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<ProvinceDTO[], false>).error;
      expect(error.code).toBe('internal_server_error');
      expect(error.message).toBe('Internal Server Error');
      expect(error.name).toBe('context');
    });

    it('should get list of province successfully', async () => {
      mockResponseForGetWards();
      const res = await commonService.getWards({
        provinceId: '1',
        districtId: '268',
      });
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<ProvinceDTO[], true>).data;
      expect(Array.isArray(data)).toBe(true);
      expect(data!.length).toBe(mockGetWardsResponse.length);
      expect(data![0]).toHaveProperty('id');
      expect(data![0]).toHaveProperty('name');
    });
  });
});
