import {BaseResponse} from '@core/BaseResponse';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {ITransactionService} from '@data/datasources/ITransactionService';
import {ConfirmationStatus} from '@data/models/transaction-signing/ConfirmationStatus';
import {TransactionSigningState} from '@data/models/transaction-signing/TransactionSigningState';
import {DIContainer} from '@di/DIContainer';
import {ResponseCodes} from '@utils/ResponseHandler';
import {
  mockRDBForCheckTransactionStatus,
  mockResponseForCheckTransactionStatus,
  mockTransactionStatusResponse,
} from 'mocks/service-apis/check-transaction-status';
import {
  mockBackTransactionSigning,
  mockErrorTransactionSigning,
  mockTransactionSigningSuccess,
  signingDataText,
} from 'mocks/transaction-signing';

describe('TransactionService', () => {
  let transactionService: ITransactionService;

  beforeEach(() => {
    transactionService = DIContainer.getInstance().getTransactionService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initTransactionSigning', () => {
    it('return confirmation data after signing successfully', async () => {
      mockTransactionSigningSuccess();

      const res = await transactionService.initTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      });
      const confirmationData = (res as BaseResponse<TransactionSigningState, true>).data;
      expect(res.success).toBe(true);
      expect(res.status).toBe(ResponseCodes.Success);
      expect(confirmationData?.data?.trim()).toEqual(JSON.stringify(signingDataText));
    });

    it('return back when user cancelled', async () => {
      mockBackTransactionSigning();

      const res = await transactionService.initTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      });
      const confirmationData = (res as BaseResponse<TransactionSigningState, true>).data;
      expect(res.success).toBe(true);
      expect(confirmationData?.status).toBe('back');
    });

    it('has error while in active transaction signing flow', async () => {
      mockErrorTransactionSigning();

      const res = await transactionService.initTransactionSigning({
        toRequestOrder: jest.fn().mockReturnValue(''),
      });
      expect(res.success).toBe(true);
      expect(res.status).toBe(ResponseCodes.Success);

      const confirmationData = (res as BaseResponse<TransactionSigningState, true>).data;
      expect(confirmationData?.status).toBe('error');
      expect(confirmationData?.code).toBe('500');
      expect(confirmationData?.message).toBe('Internal Server Error');
    });
  });

  describe('checkTransactionStatus', () => {
    it('has transaction status successfully when check transaction status', async () => {
      mockResponseForCheckTransactionStatus();

      const res = await transactionService.checkTransactionStatus({requestId: 'request-id'});
      expect(res.success).toBe(true);
      expect(res.status).toBe(200);

      const data = (res as BaseResponse<ConfirmationStatus, true>).data;
      expect(data!.confirmationId).toBe(mockTransactionStatusResponse.confirmationId);
      expect(data!.confirmationStatus).toBe(mockTransactionStatusResponse.confirmationStatus);
      expect(data!.transactionStatus).toBe(mockTransactionStatusResponse.transactionStatus);
    });

    it('has WA4 errors when check transaction status', async () => {
      mockRDBForCheckTransactionStatus();

      const res = await transactionService.checkTransactionStatus({requestId: 'request-id'});
      expect(res.success).toBe(false);
      expect(res.status).toBe(500);

      const error = (res as BaseResponse<any, false>).error;
      expect(error.code).toBe(MSBErrorCode['RDB.CA.0018']);
      expect(error.message).toBe('Error from RDB');
      expect(error.name).toBe('second context');
    });
  });
});
