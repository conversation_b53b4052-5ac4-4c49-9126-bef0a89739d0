import {BaseResponse} from '@core/BaseResponse';
import {IDailyService} from '@data/datasources/IDailyService';
import {DIContainer} from '@di/DIContainer';
import {LinkAccountDTO} from '@models/daily/LinkAccountDTO';
import {PaginationMetadataDTO} from '@models/pagination/PaginationMetadataDTO';
import {
  mockListLinkAccountResponse,
  mockResponseForGetLinkAccount,
  mockServerFailureForGetLinkAccount,
} from 'mocks/service-apis/get-list-link-account';

describe('DailyService', () => {
  let dailyService: IDailyService;

  beforeEach(() => {
    dailyService = DIContainer.getInstance().getDailyService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('has data when getListLinkAccount successfully', async () => {
    mockResponseForGetLinkAccount();
    const res = await dailyService.getListLinkAccount({} as any);
    expect(res.success).toBe(true);
    expect(res.status).toBe(200);

    const data = (res as BaseResponse<PaginationMetadataDTO<LinkAccountDTO>, true>).data;
    expect(data).toEqual(mockListLinkAccountResponse);
  });

  it('has errors when getListLinkAccount', async () => {
    mockServerFailureForGetLinkAccount();
    const res = await dailyService.getListLinkAccount({} as any);
    expect(res.success).toBe(false);
    expect(res.status).toBe(500);

    const error = (res as BaseResponse<PaginationMetadataDTO<LinkAccountDTO>, false>).error;
    expect(error.code).toBe('internal_server_error');
    expect(error.message).toBe('Internal Server Error');
    expect(error.name).toBe('context');
  });
});
