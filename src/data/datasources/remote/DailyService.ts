import {BaseResponse} from '@core/BaseResponse';
import {GetListLinkAccountRequest} from '@models/daily/GetListLinkAccountRequest';
import {LinkAccountDTO} from '@models/daily/LinkAccountDTO';
import {PaginationMetadataDTO} from '@models/pagination/PaginationMetadataDTO';
import {PathResolver} from '@utils/PathResolver';
import {handleResponse} from '@utils/ResponseHandler';
import {IHttpClient} from 'msb-host-shared-module';
import {IDailyService} from '../IDailyService';

export class DailyService implements IDailyService {
  constructor(private readonly httpClient: IHttpClient) {}

  async getListLinkAccount(
    request: GetListLinkAccountRequest,
  ): Promise<BaseResponse<PaginationMetadataDTO<LinkAccountDTO>>> {
    const url = PathResolver.daily.getListLinkAccount();

    const response = await this.httpClient.post(url, request);
    return handleResponse(response);
  }
}
