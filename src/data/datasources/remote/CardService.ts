import {BaseResponse} from '@core/BaseResponse';
import {initOpenCardErrorMapper} from '@data/mappers/card/InitOpenCardErrorMapper';
import {GetReferralInformationRequest} from '@data/models/card/GetReferralInformationRequest';
import {ReferralInformationDTO} from '@data/models/card/ReferralInformationDTO';
import {CardDTO, ListCardProductDTO} from '@models/card/CardDTO';
import {CardSecretInfoDTO} from '@models/card/CardSecretInfoDTO';
import {ChangeCardStatusRequest} from '@models/card/ChangeCardStatusRequest';
import {CreateNewPinRequest} from '@models/card/CreateNewPinRequest';
import {GetCardSecretInfoRequest} from '@models/card/GetCardSecretInfoRequest';
import {GetDetailCardRequest} from '@models/card/GetDetailCardRequest';
import {GetDetailHistoryRequest} from '@models/card/GetDetailHistoryRequest';
import {GetFilterTransactionHistoryRequest} from '@models/card/GetFilterTransactionHistoryRequest';
import {GetListHistoryRequest} from '@models/card/GetListHistoryRequest';
import {GetListHistoryResponse} from '@models/card/GetListHistoryResponse';
import {HistoryDTO} from '@models/card/HistoryDTO';
import {InitOpenCardDTO} from '@models/card/InitOpenCardDTO';
import {filterNullishValues} from '@utils/Parser';
import {injectParams, PathResolver} from '@utils/PathResolver';
import {handleResponse} from '@utils/ResponseHandler';
import {formatUrl} from '@utils/StringFormat';
import {IHttpClient} from 'msb-host-shared-module';
import {ICardService} from '../ICardService';

export class CardService implements ICardService {
  constructor(private readonly httpClient: IHttpClient) {}

  async getListCard(): Promise<BaseResponse<CardDTO[]>> {
    const url = PathResolver.card.getListCard();
    const response = await this.httpClient.get(
      injectParams(url, {includeCardFeature: 'true', excludedStatuses: '109'}),
    );
    return handleResponse(response);
  }

  async getDetailCard(cardRequest: GetDetailCardRequest): Promise<BaseResponse<CardDTO>> {
    const url = PathResolver.card.getDetailCard();
    const response = await this.httpClient.get(
      injectParams(formatUrl(url, cardRequest.id), {includeCardFeature: 'true', includeSubCards: 'true'}),
    );
    return handleResponse(response);
  }

  async changeCardStatus({cardId, newStatus}: ChangeCardStatusRequest): Promise<BaseResponse<any>> {
    const url = PathResolver.card.changeCardStatus();
    const response = await this.httpClient.patch(formatUrl(url, cardId), {newStatus});
    return handleResponse(response);
  }

  async getCardSecretInfo({cardId, ...params}: GetCardSecretInfoRequest): Promise<BaseResponse<CardSecretInfoDTO>> {
    const url = PathResolver.card.getCardSecretInfo();
    const response = await this.httpClient.get(injectParams(formatUrl(url, cardId), params));
    return handleResponse<CardSecretInfoDTO>(response);
  }

  async createNewPin({cardId, newPin}: CreateNewPinRequest): Promise<BaseResponse<any>> {
    const url = PathResolver.card.createNewPin();
    const response = await this.httpClient.patch(formatUrl(url, cardId), {newPin});
    return handleResponse(response);
  }

  async getDetailHistory(request: GetDetailHistoryRequest): Promise<BaseResponse<HistoryDTO>> {
    const url = PathResolver.card.getDetailHistory();
    const response = await this.httpClient.post(url, request);
    return handleResponse(response);
  }

  async getListHistory({cardId, ...params}: GetListHistoryRequest): Promise<BaseResponse<GetListHistoryResponse>> {
    const url = PathResolver.card.getListHistory();
    const response = await this.httpClient.get(injectParams(formatUrl(url, cardId), params as Record<string, any>));
    return handleResponse(response);
  }

  async getFilterTransactionHistory({
    cardId,
    ...body
  }: GetFilterTransactionHistoryRequest): Promise<BaseResponse<GetListHistoryResponse>> {
    const url = PathResolver.card.getFilterTransactionHistory();
    const response = await this.httpClient.post(formatUrl(url, cardId), filterNullishValues(body));
    return handleResponse(response);
  }

  async getAllowedCardProducts(): Promise<BaseResponse<ListCardProductDTO>> {
    const url = PathResolver.card.getAllowedCardProducts();
    const response = await this.httpClient.get(url);
    return handleResponse(response);
  }

  async initializeCardOpeningFlow(): Promise<BaseResponse<InitOpenCardDTO>> {
    const url = PathResolver.card.initializeCardOpeningFlow();
    const response = await this.httpClient.post(url, {});
    return handleResponse(response, initOpenCardErrorMapper);
  }

  async getReferralInformation({
    referralCode,
  }: GetReferralInformationRequest): Promise<BaseResponse<ReferralInformationDTO>> {
    const url = PathResolver.card.getReferralInformation();
    const response = await this.httpClient.get(formatUrl(url, referralCode));
    return handleResponse(response);
  }
}
