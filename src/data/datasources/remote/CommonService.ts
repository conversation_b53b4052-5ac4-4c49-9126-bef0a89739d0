import {BaseResponse} from '@core/BaseResponse';
import {BranchDTO} from '@models/common/BranchDTO';
import {DistrictDTO} from '@models/common/DistrictDTO';
import {GetDistrictsRequest} from '@models/common/GetDistrictsRequest';
import {GetWardsRequest} from '@models/common/GetWardsRequest';
import {ProvinceDTO} from '@models/common/ProvinceDTO';
import {WardDTO} from '@models/common/WardDTO';
import {PathResolver} from '@utils/PathResolver';
import {handleResponse} from '@utils/ResponseHandler';
import {formatUrl} from '@utils/StringFormat';
import {IHttpClient} from 'msb-host-shared-module';
import {ICommonService} from '../ICommonService';

export class CommonService implements ICommonService {
  constructor(private readonly httpClient: IHttpClient) {}

  async getProvinces(): Promise<BaseResponse<ProvinceDTO[]>> {
    const url = PathResolver.common.getProvinces();
    const response = await this.httpClient.get(url);
    return handleResponse(response);
  }

  async getDistricts({provinceId}: GetDistrictsRequest): Promise<BaseResponse<DistrictDTO[]>> {
    const url = PathResolver.common.getDistricts();
    const response = await this.httpClient.get(formatUrl(url, provinceId));
    return handleResponse(response);
  }

  async getWards({provinceId, districtId}: GetWardsRequest): Promise<BaseResponse<WardDTO[]>> {
    const url = PathResolver.common.getWards();
    const response = await this.httpClient.get(formatUrl(url, provinceId, districtId));
    return handleResponse(response);
  }

  async getBranches(): Promise<BaseResponse<BranchDTO[]>> {
    const url = PathResolver.common.getBranches();
    const response = await this.httpClient.get(url);
    return handleResponse(response);
  }
}
