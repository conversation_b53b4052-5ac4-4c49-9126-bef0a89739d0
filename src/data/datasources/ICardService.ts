import {BaseResponse} from '@core/BaseResponse';
import {GetReferralInformationRequest} from '@data/models/card/GetReferralInformationRequest';
import {ReferralInformationDTO} from '@data/models/card/ReferralInformationDTO';
import {CardDTO, ListCardProductDTO} from '@models/card/CardDTO';
import {CardSecretInfoDTO} from '@models/card/CardSecretInfoDTO';
import {ChangeCardStatusRequest} from '@models/card/ChangeCardStatusRequest';
import {CreateNewPinRequest} from '@models/card/CreateNewPinRequest';
import {GetCardSecretInfoRequest} from '@models/card/GetCardSecretInfoRequest';
import {GetDetailCardRequest} from '@models/card/GetDetailCardRequest';
import {GetDetailHistoryRequest} from '@models/card/GetDetailHistoryRequest';
import {GetFilterTransactionHistoryRequest} from '@models/card/GetFilterTransactionHistoryRequest';
import {GetListHistoryRequest} from '@models/card/GetListHistoryRequest';
import {GetListHistoryResponse} from '@models/card/GetListHistoryResponse';
import {HistoryDTO} from '@models/card/HistoryDTO';
import {InitOpenCardDTO} from '@models/card/InitOpenCardDTO';

export interface ICardService {
  getListCard(): Promise<BaseResponse<CardDTO[]>>;
  getDetailCard(cardRequest: GetDetailCardRequest): Promise<BaseResponse<CardDTO>>;
  changeCardStatus({cardId, newStatus}: ChangeCardStatusRequest): Promise<BaseResponse<any>>;
  getCardSecretInfo(request: GetCardSecretInfoRequest): Promise<BaseResponse<CardSecretInfoDTO>>;
  createNewPin(request: CreateNewPinRequest): Promise<BaseResponse<any>>;
  getDetailHistory(request: GetDetailHistoryRequest): Promise<BaseResponse<HistoryDTO>>;
  getListHistory(request: GetListHistoryRequest): Promise<BaseResponse<GetListHistoryResponse>>;
  getFilterTransactionHistory(
    request: GetFilterTransactionHistoryRequest,
  ): Promise<BaseResponse<GetListHistoryResponse>>;
  getAllowedCardProducts(): Promise<BaseResponse<ListCardProductDTO>>;
  initializeCardOpeningFlow(): Promise<BaseResponse<InitOpenCardDTO>>;
  getReferralInformation(request: GetReferralInformationRequest): Promise<BaseResponse<ReferralInformationDTO>>;
}
