import {BaseResponse} from '@core/BaseResponse';
import {GetListLinkAccountRequest} from '@models/daily/GetListLinkAccountRequest';
import {LinkAccountDTO} from '@models/daily/LinkAccountDTO';
import {PaginationMetadataDTO} from '@models/pagination/PaginationMetadataDTO';

export interface IDailyService {
  getListLinkAccount(request: GetListLinkAccountRequest): Promise<BaseResponse<PaginationMetadataDTO<LinkAccountDTO>>>;
}
