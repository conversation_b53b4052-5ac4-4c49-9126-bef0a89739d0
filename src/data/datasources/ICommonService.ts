import {BaseResponse} from '@core/BaseResponse';
import {BranchDTO} from '@models/common/BranchDTO';
import {DistrictDTO} from '@models/common/DistrictDTO';
import {GetDistrictsRequest} from '@models/common/GetDistrictsRequest';
import {GetWardsRequest} from '@models/common/GetWardsRequest';
import {ProvinceDTO} from '@models/common/ProvinceDTO';
import {WardDTO} from '@models/common/WardDTO';

export interface ICommonService {
  getProvinces(): Promise<BaseResponse<ProvinceDTO[]>>;
  getDistricts(request: GetDistrictsRequest): Promise<BaseResponse<DistrictDTO[]>>;
  getWards(request: GetWardsRequest): Promise<BaseResponse<WardDTO[]>>;
  getBranches(): Promise<BaseResponse<BranchDTO[]>>;
}
