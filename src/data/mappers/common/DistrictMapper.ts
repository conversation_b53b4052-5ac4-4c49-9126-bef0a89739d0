import {District} from '@entities/common/District';
import {DistrictDTO} from '@models/common/DistrictDTO';
import {toDefinedOrDefaultValue} from '@utils';

export function mapToDistrict(dto?: DistrictDTO): District {
  return {
    id: toDefinedOrDefaultValue(dto?.id, -1),
    name: toDefinedOrDefaultValue(dto?.name, ''),
    provinceCode: toDefinedOrDefaultValue(dto?.provinceCode, -1),
  };
}

export function mapToDistricts(dtos?: DistrictDTO[]): District[] {
  return dtos?.map(mapToDistrict) ?? [];
}
