import {Province} from '@entities/common/Province';
import {ProvinceDTO} from '@models/common/ProvinceDTO';
import {toDefinedOrDefaultValue} from '@utils';

export function mapToProvince(dto?: ProvinceDTO): Province {
  return {
    id: toDefinedOrDefaultValue(dto?.id, -1),
    name: toDefinedOrDefaultValue(dto?.name, ''),
    abbreviationCode: toDefinedOrDefaultValue(dto?.abbreviationCode, ''),
    upperName: toDefinedOrDefaultValue(dto?.upperName, ''),
    provinceCode: toDefinedOrDefaultValue(dto?.provinceCode, '-001'),
  };
}

export function mapToProvinces(dtos?: ProvinceDTO[]): Province[] {
  return dtos?.map(mapToProvince) ?? [];
}
