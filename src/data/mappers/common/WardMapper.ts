import {Ward} from '@entities/common/Ward';
import {WardDTO} from '@models/common/WardDTO';
import {toDefinedOrDefaultValue} from '@utils';

export function mapToWard(dto?: WardDTO): Ward {
  return {
    id: toDefinedOrDefaultValue(dto?.id, -1),
    name: toDefinedOrDefaultValue(dto?.name, ''),
    districtCode: toDefinedOrDefaultValue(dto?.districtCode, -1),
  };
}

export function mapToWards(dtos?: WardDTO[]): Ward[] {
  return dtos?.map(mapToWard) ?? [];
}
