import {Branch} from '@entities/common/Branch';
import {BranchDTO} from '@models/common/BranchDTO';
import {toDefinedOrDefaultValue} from '@utils';

export function mapToBranch(dto?: BranchDTO): Branch {
  return {
    id: toDefinedOrDefaultValue(dto?.id, -1),
    name: toDefinedOrDefaultValue(dto?.hubName, ''),
    branchNo: toDefinedOrDefaultValue(dto?.branchNo, ''),
    hubCode: toDefinedOrDefaultValue(dto?.hubCode, ''),
    hubAddress: toDefinedOrDefaultValue(dto?.hubAddress, ''),
  };
}

export function mapToBranches(dtos?: BranchDTO[]): Branch[] {
  return dtos?.map(mapToBranch) ?? [];
}
