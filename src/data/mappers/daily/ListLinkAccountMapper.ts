import {LinkAccount, ListLinkAccount} from '@entities/daily/ListLinkAccount';
import {LinkAccountDTO} from '@models/daily/LinkAccountDTO';
import {PaginationMetadataDTO} from '@models/pagination/PaginationMetadataDTO';
import {toDefinedOrDefaultValue} from '@utils';

export const mapToListLinkAccount = (paginationData?: PaginationMetadataDTO<LinkAccountDTO>): ListLinkAccount => {
  if (!paginationData) {
    return {
      metadata: {
        totalItems: 0,
      },
      data: [],
    };
  }

  return {
    metadata: {
      totalItems: toDefinedOrDefaultValue(paginationData.totalCount, 0),
    },
    data: (paginationData.data ?? []).map(mapToLinkAccount).filter(entity => entity.id.length !== 0),
  };
};

export const mapToLinkAccount = (dto: LinkAccountDTO): LinkAccount => {
  return {
    id: toDefinedOrDefaultValue(dto.externalArrangementId, ''),
    name: toDefinedOrDefaultValue(dto.userPreferences?.alias ?? dto.bankAlias, ''),
    availableBalance: toDefinedOrDefaultValue(dto.availableBalance, 0),
    currency: toDefinedOrDefaultValue(dto.currency, ''),
  };
};
