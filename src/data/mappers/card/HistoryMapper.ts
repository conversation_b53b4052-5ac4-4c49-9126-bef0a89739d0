import {DISPLAY_DATE_FORMAT, TIME_24H_FORMAT} from '@constants';
import {History, ListHistory} from '@entities/card/History';
import {GetListHistoryResponse} from '@models/card/GetListHistoryResponse';
import {HistoryDTO} from '@models/card/HistoryDTO';
import {convertDateTimeTo, getDisplayDate, toDefinedOrDefaultValue} from '@utils';

export function mapToHistory(dto?: HistoryDTO): History {
  if (!dto) {
    return {
      mCardNo: '',
      transDate: '',
      transDay: '',
      transTime: '',
      rrn: '',
      transAmount: '',
      transCurr: '',
      transDetails: '',
      typeTransaction: '',
      currencyLabel: '',
      codeCategory: '',
      drcr: '',
      textSearch: '',
      tranMsg: '',
    };
  }

  const date = getDisplayDate(
    convertDateTimeTo(toDefinedOrDefaultValue(dto.transDate, ''), DISPLAY_DATE_FORMAT),
    DISPLAY_DATE_FORMAT,
  );
  const time = convertDateTimeTo(toDefinedOrDefaultValue(dto.transDate, ''), TIME_24H_FORMAT);

  return {
    mCardNo: toDefinedOrDefaultValue(dto.maskedCardNo, ''),
    transDate: toDefinedOrDefaultValue(dto.transDate, ''),
    transDay: date,
    transTime: time,
    rrn: toDefinedOrDefaultValue(dto.rrn, ''),
    transAmount: toDefinedOrDefaultValue(dto.transAmount, ''),
    transCurr: toDefinedOrDefaultValue(dto.transCurr, ''),
    transDetails: toDefinedOrDefaultValue(dto.transDetails, ''),
    typeTransaction: toDefinedOrDefaultValue(dto.typeTransaction, ''),
    currencyLabel: toDefinedOrDefaultValue(dto.currencyLabel, ''),
    codeCategory: toDefinedOrDefaultValue(dto.codeCategory, ''),
    drcr: toDefinedOrDefaultValue(dto.drcr, ''),
    textSearch: toDefinedOrDefaultValue(dto.textSearch, ''),
    tranMsg: toDefinedOrDefaultValue(dto.tranMsg, ''),
  };
}

export const mapToListHistory = (paginationData?: GetListHistoryResponse): ListHistory => {
  if (!paginationData) {
    return {
      metadata: {
        totalItems: 0,
      },
      data: [],
    };
  }

  return {
    metadata: {
      totalItems: toDefinedOrDefaultValue(paginationData.totalRecord, 0),
    },
    data: (paginationData.transactionHistories ?? []).map(mapToHistory),
  };
};
