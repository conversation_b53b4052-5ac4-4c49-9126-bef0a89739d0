import {ReferralInformationDTO} from '@data/models/card/ReferralInformationDTO';
import {ReferralInformation} from '@entities/card/ReferralInformation';
import {toDefinedOrDefaultValue} from '@utils';

export function mapToReferralInformation(dto?: ReferralInformationDTO): ReferralInformation {
  return {
    referralCode: toDefinedOrDefaultValue(dto?.referralCode, ''),
    fullName: toDefinedOrDefaultValue(dto?.fullName, ''),
    departmentName: toDefinedOrDefaultValue(dto?.department?.name, ''),
  };
}
