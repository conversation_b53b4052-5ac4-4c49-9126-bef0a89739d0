import {BaseErrorMapper} from '@core/BaseErrorMapper';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {ResponseCodes} from '@utils/ResponseHandler';

export const initOpenCardErrorMapper: BaseErrorMapper = status => {
  if (!status) {
    return MSBErrorCode.Default;
  }

  const errorCodeMapping: Record<string, string> = {
    [ResponseCodes.GatewayTimeout.toString()]: MSBErrorCode.Timeout,
  };

  return errorCodeMapping[status.toString()] || MSBErrorCode.Default;
};
