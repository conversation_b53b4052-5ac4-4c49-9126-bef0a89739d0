import {CardInstrument} from '@domain/entities/card/CardInstrument';
import {Card} from '@entities/card/Card';
import {CardDomainStatus} from '@entities/card/CardDomainStatus';
import {CardFeature} from '@entities/card/CardFeature';
import {CardFeatureType} from '@entities/card/CardFeatureType';
import {CardHolder} from '@entities/card/CardHolder';
import {CardImage} from '@entities/card/CardImage';
import {CardType} from '@entities/card/CardType';
import {CardVisual} from '@entities/card/CardVisual';
import {FeatureData} from '@entities/card/FeatureData';
import {OwnerShip} from '@entities/card/OwnerShip';
import {PaymentLevelSelector} from '@entities/card/PaymentLevelSelector';
import {SubCard} from '@entities/card/SubCard';
import {translate} from '@locales';
import {CardDTO, ListCardProductDTO} from '@models/card/CardDTO';
import {CardFeatureDTO} from '@models/card/CardFeatureDTO';
import {CardHolderDTO} from '@models/card/CardHolderDTO';
import {CardImageDTO} from '@models/card/CardImageDTO';
import {CardVisualDTO} from '@models/card/CardVisualDTO';
import {SubCardDTO} from '@models/card/SubCardDTO';
import {toDefinedOrDefaultValue} from '@utils';
import {getEnumFromValue} from '@utils/Parser';
import {isNotNullOrUndefined, isNullOrUndefined} from '@utils/StringFormat';
import {uniqueId} from 'lodash';
import {mapToBenefit} from './BenefitMapper';

export const mapToCard = (data?: CardDTO): Card => {
  if (!data) {
    return {
      id: '',
      brand: '',
      name: '',
      ownership: OwnerShip.Main,
      holder: {
        name: '',
      },
      currency: '',
      maskedNumber: '',
      cardVisual: {
        images: [],
      },
      instrument: CardInstrument.VIRTUAL,
      instrumentI18n: '',
      productCode: '',
      productId: '',
      rbsNumber: '',
      features: [],
      subCards: [],
      creditLimit: undefined,
      availableBalance: undefined,
      benefits: [],
    };
  }

  return {
    id: toDefinedOrDefaultValue(data.id, uniqueId()),
    brand: toDefinedOrDefaultValue(data.brand, ''),
    type: getEnumFromValue(CardType, data.type),
    name: toDefinedOrDefaultValue(data?.name?.trim(), ''),
    status: getEnumFromValue(CardDomainStatus, data.status),
    ownership: getEnumFromValue(OwnerShip, data.ownership) ?? OwnerShip.Main,
    holder: mapToCardHolder(data.holder),
    currency: toDefinedOrDefaultValue(data.currency, ''),
    maskedNumber: toDefinedOrDefaultValue(data.maskedNumber, ''),
    cardVisual: mapToCardVisual(data.cardVisual),
    instrument: getEnumFromValue(CardInstrument, data.instrument) ?? CardInstrument.VIRTUAL,
    instrumentI18n:
      data.instrument?.toUpperCase() === 'Physical'.toUpperCase()
        ? translate('detail_card.physical_type')
        : translate('detail_card.virtual_type'),
    productCode: toDefinedOrDefaultValue(data.productCode, ''),
    productId: toDefinedOrDefaultValue(data.productId, ''),
    availableBalance: toDefinedOrDefaultValue(data.availableBalance),
    creditLimit: toDefinedOrDefaultValue(data.creditLimit),
    rbsNumber: toDefinedOrDefaultValue(data.rbsNumber, ''),
    features:
      data?.features
        ?.map(item => mapToCardFeature(item, getEnumFromValue(CardDomainStatus, data?.status)))
        .filter((item): item is CardFeature => item !== null && item?.status === 'ENABLED')
        .sort((a, b) => b.priority - a.priority) ?? [],
    spentAmount: toDefinedOrDefaultValue(data.spentAmount),
    remainingInstallments: toDefinedOrDefaultValue(data.remainingInstallments),
    subCards: data.subCards?.map(item => mapToSubCard(item)).filter((item): item is SubCard => item !== null) ?? [],
    paymentRate: getEnumFromValue(PaymentLevelSelector, data.paymentRate),
    benefits: data.benefits?.map(mapToBenefit) ?? [],
  };
};

export const mapToCards = (data?: CardDTO[]): Card[] => {
  if (!data) {
    return [];
  }

  return data.map(mapToCard).filter(item => item !== undefined);
};

export const mapToCardProducts = (dto?: ListCardProductDTO): Card[] => {
  return dto?.cards?.map(mapToCard).filter(item => item !== undefined) ?? [];
};

export function mapToSubCard(data: SubCardDTO | null): SubCard | undefined {
  if (data === null || isNullOrUndefined(data.id)) {
    return;
  }
  return {
    id: toDefinedOrDefaultValue(data.id, ''),
    status: getEnumFromValue(CardDomainStatus, data.status),
    holder: mapToCardHolder(data.holder ?? null),
    maskedNumber: toDefinedOrDefaultValue(data.maskedNumber, ''),
    cardVisual: mapToCardVisual(data.cardVisual ?? null),
  };
}

export function mapToCardVisual(data?: Nullish<CardVisualDTO>): CardVisual {
  return {
    images: data?.images?.map(mapToCardImage).filter((item): item is CardImage => item !== null) ?? [],
  };
}

export function mapToCardHolder(data?: CardHolderDTO | null): CardHolder {
  return {
    name: toDefinedOrDefaultValue(data?.name, ''),
  };
}

export function mapToCardImage(data: CardImageDTO | null): CardImage | null {
  if (isNullOrUndefined(data?.imageId) || isNullOrUndefined(data?.imageURL)) {
    return null;
  }
  return {
    imageId: toDefinedOrDefaultValue(data?.imageId, ''),
    imageURL: toDefinedOrDefaultValue(data?.imageURL, ''),
    type: toDefinedOrDefaultValue(data?.type, ''),
    version: toDefinedOrDefaultValue(data?.version, ''),
  };
}

export function mapToCardFeature(data: CardFeatureDTO, status: CardDomainStatus | undefined): CardFeature | undefined {
  if (isNullOrUndefined(data.id)) {
    return;
  }

  return {
    id: toDefinedOrDefaultValue(data.id, ''),
    status: toDefinedOrDefaultValue(data.status, ''),
    type: getEnumFromValue(CardFeatureType, toDefinedOrDefaultValue(data?.id, '')),
    priority: toDefinedOrDefaultValue(data.priority, 0),
    isEmpty: false,
    ...convertFeatureIdToUI(data, status),
  };
}

export function mapSubCardToCard(subCard: SubCard): Card {
  return {
    id: subCard.id,
    name: subCard.holder.name,
    ownership: OwnerShip.Sub,
    status: subCard.status,
    maskedNumber: subCard.maskedNumber,
    cardVisual: subCard.cardVisual,
    brand: '',
    holder: {
      name: '',
    },
    currency: '',
    instrument: CardInstrument.VIRTUAL,
    instrumentI18n: '',
    productCode: '',
    productId: '',
    rbsNumber: '',
    features: [],
    subCards: [],
    benefits: [],
  };
}

function convertFeatureIdToUI(feature?: CardFeatureDTO, status?: CardDomainStatus | null): FeatureData | null {
  switch (getEnumFromValue(CardFeatureType, feature?.id)) {
    case CardFeatureType.ChangeStatus:
      return getFeatureChangeStatus(status);
    case CardFeatureType.Payment:
      return {icon: 'tone-card-credit-payment', label: translate('feature_card.payment')};

    case CardFeatureType.Loyalty:
      // chua co tone-card-star-loyalty
      return {icon: 'tone-card-star-loyalty', label: translate('feature_card.loyalty')};

    case CardFeatureType.Installment:
      return {icon: 'tone-card-installment', label: translate('feature_card.installment')};

    case CardFeatureType.Reissue:
      return {icon: 'tone-card-refund', label: translate('feature_card.reissue')};

    case CardFeatureType.Limit:
      return {icon: 'tone-limit-coin', label: translate('feature_card.limit')};

    case CardFeatureType.Pfm:
      return {icon: 'tone-stock-transfer', label: translate('feature_card.pfm')};

    case CardFeatureType.Statement:
      return {icon: 'tone-file-money-statement', label: translate('feature_card.statement')};

    case CardFeatureType.NewPin:
      // @todo
      return {icon: 'tone-PIN', label: translate('feature_card.new_pin')};

    case CardFeatureType.AutoDebit:
      // Chưa có tone-card-edit
      return {icon: 'tone-card-edit', label: translate('feature_card.auto_debit')};

    case CardFeatureType.RegisterSms:
      // @todo
      return {icon: 'tone-message-box', label: translate('feature_card.sms')};

    case CardFeatureType.SecurityInfo:
      // Chua co tone-card-eye
      return {icon: 'tone-card-eye', label: translate('feature_card.security_info')};

    case CardFeatureType.LinkedAccount:
      // Chua co tone-card-pay-auto
      return {icon: 'tone-card-pay-auto', label: translate('feature_card.linked_account')};

    case CardFeatureType.Renewal:
      // @todo
      return {icon: 'tone-extend-card', label: translate('feature_card.renewal')};
    default:
      return null;
  }
}

function getFeatureChangeStatus(status?: CardDomainStatus | null): FeatureData | null {
  if (!isNotNullOrUndefined(status)) {
    return null;
  }
  switch (status) {
    case CardDomainStatus.Active:
      // @todo
      return {icon: 'tone-lock', label: translate('feature_card.lock')};
    case CardDomainStatus.Inactive:
    case CardDomainStatus.CallIssuer:
      // Chua co tone-card-disable-lock
      return {icon: 'tone-card-disable-lock', label: translate('feature_card.active')};
    default:
      // @todo
      return {icon: 'tone-unlock', label: translate('feature_card.unlock')};
  }
}
