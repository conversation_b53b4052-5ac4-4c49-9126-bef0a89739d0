import {CardSecretInfo} from '@entities/card/CardSecretInfo';
import {CardSecretInfoDTO} from '@models/card/CardSecretInfoDTO';
import {toDefinedOrDefaultValue} from '@utils';

export const mapToCardSecretInfo = (info?: CardSecretInfoDTO): CardSecretInfo => {
  return {
    clientName: toDefinedOrDefaultValue(info?.clientName, ''),
    rbsNumber: toDefinedOrDefaultValue(info?.rbsNumber, ''),
    productName: toDefinedOrDefaultValue(info?.productName, ''),
    cardNumber: toDefinedOrDefaultValue(info?.cardNumber, ''),
    cardDateOpen: toDefinedOrDefaultValue(info?.cardDateOpen, ''),
    expDate: toDefinedOrDefaultValue(info?.expDate, ''),
    cvv: toDefinedOrDefaultValue(info?.cvv, ''),
  };
};
