import {BaseErrorMapper} from '@core/BaseErrorMapper';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {ResponseCodes} from '@utils/ResponseHandler';

export const autoDebitPaymentErrorMapperInTransactionSigning: BaseErrorMapper = status => {
  if (!status) {
    return MSBErrorCode.Default;
  }

  const errorCodeMapping: Record<string, string> = {
    [ResponseCodes.BadRequest.toString()]: MSBErrorCode['WA4.CA.0017'],
    [ResponseCodes.TokenInvalid.toString()]: MSBErrorCode['RDB.CA.0019'],
    [ResponseCodes.ServerError.toString()]: MSBErrorCode['RDB.CA.0020'],
    [ResponseCodes.GatewayTimeout.toString()]: MSBErrorCode.Timeout,
  };

  return errorCodeMapping[status.toString()] || MSBErrorCode.Default;
};
