import {BranchDTO} from '@models/common/BranchDTO';
import {mapToBranch, mapToBranches} from '../common/BranchMapper';

describe('mapToBranch', () => {
  it('should return default values when dto is undefined', () => {
    const result = mapToBranch();

    expect(result).toEqual({
      id: -1,
      name: '',
      branchNo: '',
      hubCode: '',
      hubAddress: '',
    });
  });

  it('should map dto to Branch correctly', () => {
    const dto: BranchDTO = {
      id: 101,
      hubName: 'Main Hub',
      branchNo: 'B001',
      hubCode: 'HUB123',
      hubAddress: '123 Main Street',
    };

    const result = mapToBranch(dto);

    expect(result).toEqual({
      id: 101,
      name: 'Main Hub',
      branchNo: 'B001',
      hubCode: 'HUB123',
      hubAddress: '123 Main Street',
    });
  });
});

describe('mapToBranches', () => {
  it('should return an empty array if input is undefined', () => {
    const result = mapToBranches();
    expect(result).toEqual([]);
  });

  it('should map array of BranchDTOs correctly', () => {
    const dtos: BranchDTO[] = [
      {id: 1, hubName: 'Branch A', branchNo: '001', hubCode: 'HUB001', hubAddress: 'Address A'},
      {id: 2, hubName: 'Branch B', branchNo: '002', hubCode: 'HUB002', hubAddress: 'Address B'},
    ];

    const result = mapToBranches(dtos);

    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('Branch A');
    expect(result[1].hubCode).toBe('HUB002');
  });
});
