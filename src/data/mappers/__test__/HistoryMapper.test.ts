import {GetListHistoryResponse} from '@models/card/GetListHistoryResponse';
import {HistoryDTO} from '@models/card/HistoryDTO';
import {mapToHistory, mapToListHistory} from '../card/HistoryMapper';

describe('mapToHistory', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return default values when dto is undefined', () => {
    const result = mapToHistory();

    expect(result).toEqual({
      mCardNo: '',
      transDate: '',
      transDay: '',
      transTime: '',
      rrn: '',
      transAmount: '',
      transCurr: '',
      transDetails: '',
      typeTransaction: '',
      currencyLabel: '',
      codeCategory: '',
      drcr: '',
      textSearch: '',
      tranMsg: '',
    });
  });

  it('should map dto to History with formatted date and time', () => {
    const dto: HistoryDTO = {
      maskedCardNo: '****1234',
      transDate: '2024-06-01T12:30:00Z',
      rrn: '123456',
      transAmount: '100',
      transCurr: 'USD',
      transDetails: 'Purchase',
      typeTransaction: 'Online',
      currencyLabel: 'USD',
      codeCategory: '01',
      drcr: 'DR',
      textSearch: 'some text',
      tranMsg: 'Message here',
    };

    const result = mapToHistory(dto);

    expect(result.transDay).toBe('01/06/2024');
    expect(result.transTime).toBe('19:30');
    expect(result.mCardNo).toBe('****1234');
    expect(result.transDetails).toBe('Purchase');
  });
});

describe('mapToListHistory', () => {
  it('should return default structure if paginationData is undefined', () => {
    const result = mapToListHistory();

    expect(result).toEqual({
      metadata: {totalItems: 0},
      data: [],
    });
  });

  it('should map pagination data to list history correctly', () => {
    const paginationData: GetListHistoryResponse = {
      totalRecord: 2,
      transactionHistories: [
        {
          maskedCardNo: '****1111',
          transDate: '2024-06-01T10:00:00Z',
          rrn: 'rrn-1',
          transAmount: '100',
          transCurr: 'USD',
          transDetails: 'Detail 1',
          typeTransaction: 'Online',
          currencyLabel: 'USD',
          codeCategory: '01',
          drcr: 'DR',
          textSearch: '',
          tranMsg: 'Msg1',
        },
      ],
    };

    const result = mapToListHistory(paginationData);

    expect(result.metadata.totalItems).toBe(2);
    expect(result.data.length).toBe(1);
    expect(result.data[0].mCardNo).toBe('****1111');
  });

  it('should default totalItems to 0 if undefined', () => {
    const result = mapToListHistory({
      totalRecord: undefined,
      transactionHistories: [],
    });

    expect(result.metadata.totalItems).toBe(0);
  });
});
