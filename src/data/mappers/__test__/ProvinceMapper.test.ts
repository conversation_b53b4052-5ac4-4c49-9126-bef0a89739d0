import {ProvinceDTO} from '@models/common/ProvinceDTO';
import {mapToProvince, mapToProvinces} from '../common/ProvinceMapper';

describe('mapToProvince', () => {
  it('should return default values when dto is undefined', () => {
    const result = mapToProvince();

    expect(result).toEqual({
      id: -1,
      name: '',
      abbreviationCode: '',
      upperName: '',
      provinceCode: '-001',
    });
  });

  it('should map dto to Province correctly', () => {
    const dto: ProvinceDTO = {
      id: 99,
      name: 'Hà Nội',
      abbreviationCode: 'HN',
      upperName: 'MIỀN BẮC',
      provinceCode: 'HN-001',
    };

    const result = mapToProvince(dto);

    expect(result).toEqual({
      id: 99,
      name: 'Hà Nội',
      abbreviationCode: 'HN',
      upperName: 'MIỀN BẮC',
      provinceCode: 'HN-001',
    });
  });
});

describe('mapToProvinces', () => {
  it('should return empty array when input is undefined', () => {
    const result = mapToProvinces();
    expect(result).toEqual([]);
  });

  it('should map array of ProvinceDTO correctly', () => {
    const dtos: ProvinceDTO[] = [
      {
        id: 1,
        name: 'TP HCM',
        abbreviationCode: 'HCM',
        upperName: 'MIỀN NAM',
        provinceCode: 'HCM-002',
      },
      {
        id: 2,
        name: 'Đà Nẵng',
        abbreviationCode: 'DN',
        upperName: 'MIỀN TRUNG',
        provinceCode: 'DN-003',
      },
    ];

    const result = mapToProvinces(dtos);

    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('TP HCM');
    expect(result[1].provinceCode).toBe('DN-003');
  });
});
