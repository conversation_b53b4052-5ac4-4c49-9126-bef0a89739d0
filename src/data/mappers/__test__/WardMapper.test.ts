import {WardDTO} from '@models/common/WardDTO';
import {mapToWard, mapToWards} from '../common/WardMapper';

describe('mapToWard', () => {
  it('should return default values when dto is undefined', () => {
    const result = mapToWard();

    expect(result).toEqual({
      id: -1,
      name: '',
      districtCode: -1,
    });
  });

  it('should map WardDTO to Ward correctly', () => {
    const dto: WardDTO = {
      id: 10,
      name: 'Phường Bến Nghé',
      districtCode: 1,
    };

    const result = mapToWard(dto);

    expect(result).toEqual({
      id: 10,
      name: 'Phường Bến Nghé',
      districtCode: 1,
    });
  });
});

describe('mapToWards', () => {
  it('should return empty array when input is undefined', () => {
    const result = mapToWards();
    expect(result).toEqual([]);
  });

  it('should map list of WardDTO to Ward[] correctly', () => {
    const dtos: WardDTO[] = [
      {id: 1, name: 'Ph<PERSON>ờng 1', districtCode: 101},
      {id: 2, name: 'Ph<PERSON>ờng 2', districtCode: 102},
    ];

    const result = mapToWards(dtos);

    expect(result).toHaveLength(2);
    expect(result[0]).toEqual({id: 1, name: 'Phường 1', districtCode: 101});
    expect(result[1].name).toBe('Phường 2');
  });
});
