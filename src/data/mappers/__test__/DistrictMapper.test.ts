import {DistrictDTO} from '@models/common/DistrictDTO';
import {mapToDistrict, mapToDistricts} from '../common/DistrictMapper';

describe('mapToDistrict', () => {
  it('should return default values when dto is undefined', () => {
    const result = mapToDistrict();

    expect(result).toEqual({
      id: -1,
      name: '',
      provinceCode: -1,
    });
  });

  it('should map dto to District correctly', () => {
    const dto: DistrictDTO = {
      id: 1001,
      name: 'District A',
      provinceCode: 10,
    };

    const result = mapToDistrict(dto);

    expect(result).toEqual({
      id: 1001,
      name: 'District A',
      provinceCode: 10,
    });
  });
});

describe('mapToDistricts', () => {
  it('should return empty array if input is undefined', () => {
    const result = mapToDistricts();
    expect(result).toEqual([]);
  });

  it('should map array of DistrictDTO correctly', () => {
    const dtos: DistrictDTO[] = [
      {id: 1, name: 'D1', provinceCode: 101},
      {id: 2, name: 'D2', provinceCode: 102},
    ];

    const result = mapToDistricts(dtos);

    expect(result).toHaveLength(2);
    expect(result[0]).toEqual({id: 1, name: 'D1', provinceCode: 101});
    expect(result[1]).toEqual({id: 2, name: 'D2', provinceCode: 102});
  });
});
