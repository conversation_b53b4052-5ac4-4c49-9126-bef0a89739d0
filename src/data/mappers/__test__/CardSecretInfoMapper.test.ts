import {CardSecretInfoDTO} from '@models/card/CardSecretInfoDTO';
import {mockListCardResponse} from 'mocks/service-apis/get-list-cards';
import {mapToCardSecretInfo} from '../card/CardSecretInfoMapper';

describe('CardSecretInfoMapper', () => {
  it('should correctly map data from CardSecretInfoDTO', () => {
    // Create mock CardSecretInfoDTO data from mockListCardResponse
    const dto: CardSecretInfoDTO = {
      clientName: mockListCardResponse[0].holder.name,
      rbsNumber: mockListCardResponse[0].rbsNumber,
      productName: mockListCardResponse[0].name,
      cardNumber: mockListCardResponse[0].maskedNumber,
      cardDateOpen: '2023-01-01',
      expDate: '2028-01-01',
      cvv: '123',
    };
    const result = mapToCardSecretInfo(dto);
    expect(result.clientName).toBe(dto.clientName);
    expect(result.rbsNumber).toBe(dto.rbsNumber);
    expect(result.productName).toBe(dto.productName);
    expect(result.cardNumber).toBe(dto.cardNumber);
    expect(result.cardDateOpen).toBe(dto.cardDateOpen);
    expect(result.expDate).toBe(dto.expDate);
    expect(result.cvv).toBe(dto.cvv);
  });

  it('should return default values when input is undefined', () => {
    const result = mapToCardSecretInfo();
    expect(result.clientName).toBe('');
    expect(result.rbsNumber).toBe('');
    expect(result.productName).toBe('');
    expect(result.cardNumber).toBe('');
    expect(result.cardDateOpen).toBe('');
    expect(result.expDate).toBe('');
    expect(result.cvv).toBe('');
  });

  it('should return default values when fields are null', () => {
    const dto: CardSecretInfoDTO = {
      clientName: null,
      rbsNumber: null,
      productName: null,
      cardNumber: null,
      cardDateOpen: null,
      expDate: null,
      cvv: null,
    };
    const result = mapToCardSecretInfo(dto);
    expect(result.clientName).toBe('');
    expect(result.rbsNumber).toBe('');
    expect(result.productName).toBe('');
    expect(result.cardNumber).toBe('');
    expect(result.cardDateOpen).toBe('');
    expect(result.expDate).toBe('');
    expect(result.cvv).toBe('');
  });
});
