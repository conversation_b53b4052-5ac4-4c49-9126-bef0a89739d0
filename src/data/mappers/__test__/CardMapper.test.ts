import {CardHolderDTO} from '@data/models/card/CardHolderDTO';
import {SubCardDTO} from '@data/models/card/SubCardDTO';
import {mockGetAllowedCardProductsResponse} from 'mocks/service-apis/get-allowed-card-products';
import {mockListCardResponse} from 'mocks/service-apis/get-list-cards';
import {
  mapToCard,
  mapToCardFeature,
  mapToCardHolder,
  mapToCardImage,
  mapToCardProducts,
  mapToCards,
  mapToCardVisual,
  mapToSubCard,
} from '../card/CardMapper';

describe('CardMapper', () => {
  describe('mapToCard', () => {
    it('should map CardDTO to Card', () => {
      const dto = mockListCardResponse[0];
      const card = mapToCard(dto);
      expect(card).toBeDefined();
      expect(card.id).toBe(dto.id);
      expect(card.brand).toBe(dto.brand);
      expect(card.name).toBe(dto.name);
      expect(card.maskedNumber).toBe(dto.maskedNumber);
      expect(card.cardVisual.images[0].imageId).toBe(dto.cardVisual.images[0].imageId);
    });

    it('should return default Card when input is undefined', () => {
      const card = mapToCard();
      expect(card).toBeDefined();
      expect(card.id).toBe('');
      expect(card.brand).toBe('');
      expect(card.name).toBe('');
    });
  });

  describe('mapToCards', () => {
    it('should map array of CardDTO to array of Card', () => {
      const cards = mapToCards(mockListCardResponse);
      expect(Array.isArray(cards)).toBe(true);
      expect(cards.length).toBe(mockListCardResponse.length);
      expect(cards[0].id).toBe(mockListCardResponse[0].id);
    });

    it('should return empty array when input is undefined', () => {
      expect(mapToCards()).toEqual([]);
    });
  });

  describe('mapToCardProducts', () => {
    it('should map ListCardProductDTO to array of Card', () => {
      const cards = mapToCardProducts(mockGetAllowedCardProductsResponse);
      expect(Array.isArray(cards)).toBe(true);
      expect(cards.length).toBe(mockGetAllowedCardProductsResponse.cards.length);
      expect(cards[0].productCode).toBe(mockGetAllowedCardProductsResponse.cards[0].productCode);
    });

    it('should return empty array when input is undefined', () => {
      expect(mapToCardProducts()).toEqual([]);
    });
  });

  describe('mapToSubCard', () => {
    it('should map SubCardDTO to SubCard', () => {
      const holder: CardHolderDTO = {
        name: 'Holder Sub',
        additions: {},
      };
      const dto: SubCardDTO = {
        id: 'sub1',
        status: '14',
        holder,
        maskedNumber: '**** 1234',
        cardVisual: {
          images: [
            {
              imageId: 'img1',
              imageURL: 'url1',
              type: 'FRONT',
              version: '1',
            },
          ],
        },
      };
      const subCard = mapToSubCard(dto);
      expect(subCard).toBeDefined();
      expect(subCard?.id).toBe('sub1');
      expect(subCard?.holder.name).toBe('Holder Sub');
      expect(subCard?.maskedNumber).toBe('**** 1234');
      expect(subCard?.cardVisual.images[0].imageId).toBe('img1');
    });

    it('should return undefined if input is null', () => {
      expect(mapToSubCard(null)).toBeUndefined();
    });
  });

  describe('mapToCardVisual', () => {
    it('should map CardVisualDTO to CardVisual', () => {
      const dto = {
        images: [
          {
            imageId: 'img1',
            imageURL: 'url1',
            type: 'FRONT',
            version: '1',
          },
        ],
      };
      const visual = mapToCardVisual(dto);
      expect(visual.images.length).toBe(1);
      expect(visual.images[0].imageId).toBe('img1');
    });

    it('should return empty images array if input is undefined', () => {
      expect(mapToCardVisual().images).toEqual([]);
    });
  });

  describe('mapToCardHolder', () => {
    it('should map CardHolderDTO to CardHolder', () => {
      const dto: CardHolderDTO = {
        name: 'Holder Name',
        additions: {},
      };
      const holder = mapToCardHolder(dto);
      expect(holder.name).toBe('Holder Name');
    });

    it('should return empty name if input is undefined', () => {
      expect(mapToCardHolder().name).toBe('');
    });
  });

  describe('mapToCardImage', () => {
    it('should map CardImageDTO to CardImage', () => {
      const dto = {
        imageId: 'img1',
        imageURL: 'url1',
        type: 'FRONT',
        version: '1',
      };
      const image = mapToCardImage(dto);
      expect(image).toBeDefined();
      expect(image?.imageId).toBe('img1');
      expect(image?.imageURL).toBe('url1');
    });

    it('should return null if imageId or imageURL is missing', () => {
      expect(mapToCardImage({imageId: undefined, imageURL: 'url'})).toBeNull();
      expect(mapToCardImage({imageId: 'img', imageURL: undefined})).toBeNull();
    });
  });

  describe('mapToCardFeature', () => {
    it('should map CardFeatureDTO to CardFeature', () => {
      const dto = {
        id: 'CHANGE_STATUS',
        status: 'ENABLED',
        priority: 1000,
      };
      const feature = mapToCardFeature(dto, undefined);
      expect(feature).toBeDefined();
      expect(feature?.id).toBe('CHANGE_STATUS');
      expect(feature?.status).toBe('ENABLED');
      expect(feature?.isEmpty).toBe(false);
    });

    it('should return undefined if id is missing', () => {
      expect(mapToCardFeature({status: 'ENABLED'}, undefined)).toBeUndefined();
    });
  });
});
