import {MSBErrorCode} from '@core/MSBErrorCode';
import {ResponseCodes} from '@utils/ResponseHandler';
import {autoDebitPaymentErrorMapperInTransactionSigning} from '../transaction-signing/AutoDebitErrorMapper';

describe('autoDebitPaymentErrorMapperInTransactionSigning', () => {
  it('should return Default error code when status is undefined', () => {
    // Test description: Should return MSBErrorCode.Default if status is undefined
    expect(autoDebitPaymentErrorMapperInTransactionSigning()).toBe(MSBErrorCode.Default);
    expect(autoDebitPaymentErrorMapperInTransactionSigning(null)).toBe(MSBErrorCode.Default);
  });

  it('should return WA4.CA.0017 when status is BadRequest', () => {
    // Test description: Should map BadRequest to WA4.CA.0017
    expect(autoDebitPaymentErrorMapperInTransactionSigning(ResponseCodes.BadRequest)).toBe(MSBErrorCode['WA4.CA.0017']);
  });

  it('should return RDB.CA.0019 when status is TokenInvalid', () => {
    // Test description: Should map TokenInvalid to RDB.CA.0019
    expect(autoDebitPaymentErrorMapperInTransactionSigning(ResponseCodes.TokenInvalid)).toBe(
      MSBErrorCode['RDB.CA.0019'],
    );
  });

  it('should return RDB.CA.0020 when status is ServerError', () => {
    // Test description: Should map ServerError to RDB.CA.0020
    expect(autoDebitPaymentErrorMapperInTransactionSigning(ResponseCodes.ServerError)).toBe(
      MSBErrorCode['RDB.CA.0020'],
    );
  });

  it('should return RDB.CA.0021 when status is GatewayTimeout', () => {
    // Test description: Should map GatewayTimeout to RDB.CA.0001
    expect(autoDebitPaymentErrorMapperInTransactionSigning(ResponseCodes.GatewayTimeout)).toBe(MSBErrorCode.Timeout);
  });

  it('should return Default error code for unknown status', () => {
    // Test description: Should return MSBErrorCode.Default for unknown status
    expect(autoDebitPaymentErrorMapperInTransactionSigning('SOME_UNKNOWN_STATUS')).toBe(MSBErrorCode.Default);
    expect(autoDebitPaymentErrorMapperInTransactionSigning(999)).toBe(MSBErrorCode.Default);
  });
});
