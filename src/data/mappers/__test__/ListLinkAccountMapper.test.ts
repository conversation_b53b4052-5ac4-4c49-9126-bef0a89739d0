import {LinkAccountDTO} from '@models/daily/LinkAccountDTO';
import {PaginationMetadataDTO} from '@models/pagination/PaginationMetadataDTO';
import {mockListCardResponse} from 'mocks/service-apis/get-list-cards';
import {mapToLinkAccount, mapToListLinkAccount} from '../daily/ListLinkAccountMapper';

describe('ListLinkAccountMapper', () => {
  describe('mapToListLinkAccount', () => {
    it('should return default ListLinkAccount when input is undefined', () => {
      const result = mapToListLinkAccount();
      expect(result.metadata.totalItems).toBe(0);
      expect(result.data).toEqual([]);
    });

    it('should map PaginationMetadataDTO<LinkAccountDTO> to ListLinkAccount', () => {
      const mockPagination: PaginationMetadataDTO<LinkAccountDTO> = {
        totalCount: 2,
        data: [
          {
            externalArrangementId: '123',
            userPreferences: {alias: 'Account 1'},
            bankAlias: 'Bank 1',
            availableBalance: 1000,
            currency: 'VND',
          },
          {
            externalArrangementId: '456',
            userPreferences: {alias: 'Account 2'},
            bankAlias: 'Bank 2',
            availableBalance: 2000,
            currency: 'USD',
          },
        ],
      };
      const result = mapToListLinkAccount(mockPagination);
      expect(result.metadata.totalItems).toBe(2);
      expect(result.data.length).toBe(2);
      expect(result.data[0].id).toBe('123');
      expect(result.data[1].id).toBe('456');
    });

    it('should filter out entities with empty id', () => {
      const mockPagination: PaginationMetadataDTO<LinkAccountDTO> = {
        totalCount: 2,
        data: [
          {
            externalArrangementId: '',
            userPreferences: {alias: 'Account 1'},
            bankAlias: 'Bank 1',
            availableBalance: 1000,
            currency: 'VND',
          },
          {
            externalArrangementId: '789',
            userPreferences: {alias: 'Account 3'},
            bankAlias: 'Bank 3',
            availableBalance: 3000,
            currency: 'EUR',
          },
        ],
      };
      const result = mapToListLinkAccount(mockPagination);
      expect(result.data.length).toBe(1);
      expect(result.data[0].id).toBe('789');
    });

    it('should return totalItems as 0 if totalCount is undefined', () => {
      const mockPagination: PaginationMetadataDTO<LinkAccountDTO> = {
        totalCount: undefined,
        data: [],
      };
      const result = mapToListLinkAccount(mockPagination);
      expect(result.metadata.totalItems).toBe(0);
    });
  });

  describe('mapToLinkAccount', () => {
    it('should map LinkAccountDTO to LinkAccount with alias', () => {
      const dto: LinkAccountDTO = {
        externalArrangementId: 'abc',
        userPreferences: {alias: 'My Account'},
        bankAlias: 'Bank Alias',
        availableBalance: 500,
        currency: 'VND',
      };
      const result = mapToLinkAccount(dto);
      expect(result.id).toBe('abc');
      expect(result.name).toBe('My Account');
      expect(result.availableBalance).toBe(500);
      expect(result.currency).toBe('VND');
    });

    it('should use bankAlias if userPreferences.alias is undefined', () => {
      const dto: LinkAccountDTO = {
        externalArrangementId: 'def',
        userPreferences: {},
        bankAlias: 'Bank Alias 2',
        availableBalance: 100,
        currency: 'USD',
      };
      const result = mapToLinkAccount(dto);
      expect(result.name).toBe('Bank Alias 2');
    });

    it('should return default values if fields are missing', () => {
      const dto: LinkAccountDTO = {};
      const result = mapToLinkAccount(dto);
      expect(result.id).toBe('');
      expect(result.name).toBe('');
      expect(result.availableBalance).toBe(0);
      expect(result.currency).toBe('');
    });
  });

  describe('Integration with mockListCardResponse', () => {
    it('should map mockListCardResponse[0] to LinkAccount correctly', () => {
      const dto = {
        externalArrangementId: mockListCardResponse[0].id,
        userPreferences: {alias: mockListCardResponse[0].holder.name},
        bankAlias: mockListCardResponse[0].accountName,
        availableBalance: mockListCardResponse[0].availableBalance,
        currency: mockListCardResponse[0].currency,
      };
      const result = mapToLinkAccount(dto);
      expect(result.id).toBe(mockListCardResponse[0].id);
      expect(result.name).toBe(mockListCardResponse[0].holder.name);
      expect(result.availableBalance).toBe(mockListCardResponse[0].availableBalance);
      expect(result.currency).toBe(mockListCardResponse[0].currency);
    });
  });
});
