import {ReferralInformationDTO} from '@data/models/card/ReferralInformationDTO';
import {mapToReferralInformation} from '../card/ReferralInformationMapper';

describe('mapToReferralInformation', () => {
  it('should return default values when dto is undefined', () => {
    const result = mapToReferralInformation();

    expect(result).toEqual({
      referralCode: '',
      fullName: '',
      departmentName: '',
    });
  });

  it('should return default values when dto.data is null', () => {
    const dto: ReferralInformationDTO = {};

    const result = mapToReferralInformation(dto);

    expect(result).toEqual({
      referralCode: '',
      fullName: '',
      departmentName: '',
    });
  });

  it('should return default values when nested fields are null', () => {
    const dto: ReferralInformationDTO = {
      referralCode: null,
      fullName: null,
      department: {
        name: null,
      },
    };

    const result = mapToReferralInformation(dto);

    expect(result).toEqual({
      referralCode: '',
      fullName: '',
      departmentName: '',
    });
  });

  it('should map data correctly when all fields are provided', () => {
    const dto: ReferralInformationDTO = {
      referralCode: 'REF123',
      fullName: 'Nguyen Van A',
      department: {
        name: 'Phòng Kinh doanh',
      },
    };

    const result = mapToReferralInformation(dto);

    expect(result).toEqual({
      referralCode: 'REF123',
      fullName: 'Nguyen Van A',
      departmentName: 'Phòng Kinh doanh',
    });
  });
});
