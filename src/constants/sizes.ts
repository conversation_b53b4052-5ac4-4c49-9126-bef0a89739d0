import {Dimensions, ScaledSize} from 'react-native';

export const screenDimensions: ScaledSize = Dimensions.get('screen');

export const screenWidthToDesignRatio = screenDimensions.width / 390;
/**
 * <PERSON> tiêu chuẩn ISO 7810
 * <PERSON><PERSON><PERSON> thước tiêu chuẩn của thẻ ATM là chiều dài 85,60 mm (3,37 inch) và chiều rộng 53,98 mm (2,13 inch).
 * <PERSON><PERSON> kính bo góc (2.88–3.48 mm).
 *
 * Mặc định: 3mm để tính corner
 */
export const cardAspectRatio = 8560 / 5398;

// Làm tròn để tránh crash trên các thiết bị Android
export const getCardRoundedCorner = (cardWidthInPx: number) => {
  'worklet';
  return Math.round((cardWidthInPx * 300) / 8560);
};
