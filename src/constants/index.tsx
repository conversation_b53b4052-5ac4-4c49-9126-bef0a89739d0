import images from '@assets/images';
import {isJest} from '@utils/PlatformChecker';

export const HOTLINE = '19006083';
export const TIME_24H_FORMAT = 'HH:mm';
export const DISPLAY_DATE_FORMAT = 'DD/MM/YYYY';
export const DISPLAY_CARD_DATE_FORMAT = 'MM/YY';
export const DISPLAY_DATETIME_FORMAT = 'HH:mm DD/MM/YYYY';
export const SERVER_DATE_FORMAT = 'YYYY-MM-DD';
export const SERVER_DATE_FORMAT_WITH_TIMEZONE = 'YYYY-MM-DDTHH:mm:ss[Z]';
export const MAX_ENTER_PIN = 5;

/**
 * Do dữ liệu mẫu chỉ được 6 phần tử nên đang split ra 2 page và cần giả lập mỗi page là 3
 * Khi nào có dữ liệu mẫu lớn sẽ loại bỏ phần check dữ liệu ở đây
 */
export const HISTORY_PAGE_SIZE = isJest() ? 3 : 30;
export const FILTER_HISTORY_PAGE_SIZE = isJest() ? 3 : 20;
export const LINK_ACCOUNT_PAGE_SIZE = 200;

export const listBannerCard = [images.banner_applePay, images.banner_googlePay];
