/**
 * Các status ở bên domain CARD
 */
export enum CardDomainStatus {
  Active = '14',
  BlockCardTempByCustomer = '176',
  Expired = '690',
  BlockCardByCustomer = '170',
  BlockByPin = '287',
  BlockPickUp = '97',
  BlockCardAnnualFee = '174',
  BlockCardNoRenewal = '183',
  BlockHonourWithIdentification = '184',
  BlockByCardRisk = '472',
  Block = '98',
  Inactive = '473',
  BlockCard = '63',
  BlockCardLost = '74',
  BlockCardDoNotHonor = '171',
  BlockCardNoRenewalCredit = '173',
  BadDebit = '1540',
  WaitingClose = '175',
  BlockAfter2Cycles = '471',
  DeviceClosed = '102',
  DeviceOK = '85',
  CardClosed = '109',
  KHOATHE = '94',
  CallIssuer = '64',
  AccountClosed = '86',
  AccountDecline = '166',
  AccountOK = '51',
  WaitingForOpsActivation = '177',
  zCODE66 = '178',
  zCODE67 = '179',
  zCODE75 = '180',
  zCODE76 = '181',
  zDECLINE57 = '182',
  BlockAfter1Cycle = '890',
}

export const isNeedActiveCard = (cardStatus: Nullish<CardDomainStatus>): boolean => {
  return (
    cardStatus !== undefined &&
    cardStatus !== null &&
    [CardDomainStatus.Inactive, CardDomainStatus.CallIssuer].includes(cardStatus)
  );
};

export const STATUS_GROUPS = {
  INACTIVE: [CardDomainStatus.CallIssuer, CardDomainStatus.Inactive],
  TEMP_BLOCK: [CardDomainStatus.BlockCardTempByCustomer, CardDomainStatus.BlockCardByCustomer],
  BLOCKED: [
    CardDomainStatus.BlockAfter1Cycle,
    CardDomainStatus.BlockByPin,
    CardDomainStatus.BlockAfter2Cycles,
    CardDomainStatus.BlockPickUp,
    CardDomainStatus.BlockCardAnnualFee,
    CardDomainStatus.BlockCardNoRenewal,
    CardDomainStatus.BlockHonourWithIdentification,
    CardDomainStatus.BlockByCardRisk,
    CardDomainStatus.Block,
    CardDomainStatus.BlockCard,
    CardDomainStatus.BlockCardLost,
    CardDomainStatus.BlockCardDoNotHonor,
    CardDomainStatus.BlockCardNoRenewalCredit,
    CardDomainStatus.WaitingForOpsActivation,
    CardDomainStatus.BadDebit,
  ],
};
