export enum Way4CardStatusCode {
  ExpiredCard = '54',
  //   Khóa do chưa đổi PIN lần đầu
  PINBlocked = '79',
  BlockAfter2Cycles = '02',
  //   Thẻ bị khóa do nghi ngờ rủi ro/gian lận
  BlockByCardRisk = '52',
  //   30 ngày chưa kích hoạt
  InactiveCard = '39',
  //   Thẻ bị khóa do nợ nhóm 3 trở lên tại MSB hoặc các TCTD khác
  BadDebt = '89',
  DeviceClosed = '12',
  //   Đóng hoàn toàn
  CardClosed = '14',
  //Thẻ hoạt động
  CardOK = '00',
  //   Thẻ mới được phát hành, chưa kích hoạt
  CallIssuer = '57',
  AccountClosed = '13',
  //   Khóa thẻ
  AccountDecline = '05',
  PickUp04 = '04',
  //   Thẻ bị khóa khi chủ thẻ báo mất cắp/thất lạc, không thể giao dịch
  BlockCard = '43',
  //   Thẻ bị khóa khi chủ thẻ báo mất cắp/thất lạc, không thể giao dịch
  BlockCardLost = '41',
  //   Khóa thẻ do KH khóa
  BlockCardByCustomer = '08',
  //   Khóa tạm thời do chậm thanh toán 4 kỳ liên tiếp
  CardDoNotHonorCreditCard = '36',
  //   Thẻ bị khóa vĩnh viễn do không thanh toán đầy đủ và đúng hạn dư nợ thẻ sau 30 ngày kể từ ngày khóa tạm thời
  CardNoRenewalCreditCard = '37',
  //   Khóa do chưa thanh toán phí thường niên
  BlockCardAnnualFee = '38',
  //   Khóa thẻ chờ hủy
  WAITING_CLOSE = '58',
  // KH chủ động khóa thẻ qua MBA/ tổng đài tự động IVR
  CustomerBlock = '62',
  //   Thẻ tạm khóa chờ xử lý của bộ phận tác nghiệp
  WaitingForOpsActivation = '65',
  zCODE66 = '66',
  zCODE67 = '67',
  zCODE75 = '75',
  zCODE76 = '76',
  zDECLINE57 = '92',
  //   Khóa giao dịch vào thời điểm 12h đêm
  HonourWithIdentification = '15',
  //   Trước kia dùng vì lý do gì k biết
  CardNoRenewal = '50',
  BlockAfter1Cycle = '18',
}
