import {PaginationMetadata} from '../base/PaginationMetadata';

export interface ListHistory {
  metadata: PaginationMetadata;
  data: History[];
}

export interface History {
  mCardNo: string;
  transDate: string;
  transDay: string;
  transTime: string;
  rrn: string;
  transAmount: string;
  transCurr: string;
  transDetails: string;
  typeTransaction: string;
  currencyLabel: string;
  codeCategory: string;
  drcr: string;
  textSearch: string;
  tranMsg: string;
}

export enum HistoryItemType {
  HEADER_SECTION,
  ITEM,
}

export type HistoryItem<Type = HistoryItemType> = Type extends HistoryItemType.HEADER_SECTION
  ? {
      type: HistoryItemType.HEADER_SECTION;
      headerTitle: string;
      data?: History;
    }
  : {
      type: HistoryItemType.ITEM;
      data: History;
      isFirstSection: boolean;
    };
