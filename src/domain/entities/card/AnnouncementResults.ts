import {I18nKeys, translate} from '@locales';
import {MSBIcons} from 'msb-shared-component';
import {Card} from './Card';
import {EAnnouncementResultsStatus, EAnnouncementResultsType} from './EAnnouncementResults';

interface CardDataNoticeScreen {
  cardId: string;
  imageUrl: string;
  instrumentI18n: string;
  name: string;
  markedNumber: string;
}

export interface IAnnouncementExtendData {
  status: EAnnouncementResultsStatus;
  type: EAnnouncementResultsType;
  message?: string;
  errorCode?: string;
  date: string;
  fileUrl?: string;
}
export interface AnnouncementResultsParams {
  extendData: IAnnouncementExtendData;
  cardData: CardDataNoticeScreen;
}

export interface IGetAnnouncementResultUI {
  title: string;
  boldText?: string;
  icon: string | undefined;
  message: string;
  file?: {
    iconName: MSBIcons;
    title: string;
    onPress: () => void;
  };
}

/**
 * Get error description from translation
 * @returns translate(`errors.${errorCode}.description`)
 *
 * If want to add new translation for error code
 * -> Add it to errors object in translation file(vi.json, en.json)
 */
export const getAnnouncementResultConfig = (
  errorCode?: string,
): {
  [type in EAnnouncementResultsType]: {
    [status in EAnnouncementResultsStatus]: IGetAnnouncementResultUI;
  };
} => ({
  [EAnnouncementResultsType.createNewPin]: {
    [EAnnouncementResultsStatus.success]: {
      title: translate('announcement_results.create_new_pin_success'),
      boldText: translate('card.discover_card_desc_bold'),
      icon: 'result-success',
      message: '',
    },
    [EAnnouncementResultsStatus.fail]: {
      title: translate('announcement_results.create_new_pin_fail'),
      icon: 'result-error',
      message: translate(`errors.${errorCode}.description` as I18nKeys, {
        defaultValue: translate('announcement_results.create_new_pin_error'),
      }),
    },
  },
  [EAnnouncementResultsType.activeCard]: {
    [EAnnouncementResultsStatus.success]: {
      title: translate('announcement_results.active_card_success'),
      icon: 'result-success',
      message: translate('announcement_results.active_card_success_content'),
      boldText: translate('announcement_results.active_card_success_content_bold_text'),
    },
    [EAnnouncementResultsStatus.fail]: {
      title: translate('announcement_results.active_card_fail'),
      icon: 'result-error',
      message: translate(`errors.${errorCode}.description` as I18nKeys, {
        defaultValue: translate('announcement_results.active_card_error_content'),
      }),
    },
  },
  [EAnnouncementResultsType.unlockCard]: {
    [EAnnouncementResultsStatus.success]: {
      title: translate('announcement_results.unlock_card_success'),
      icon: 'result-success',
      message: translate('announcement_results.defaultMessage'),
    },
    [EAnnouncementResultsStatus.fail]: {
      title: translate('announcement_results.unlock_card_fail'),
      icon: 'result-error',
      message: translate(`errors.${errorCode}.description` as I18nKeys, {
        defaultValue: translate('announcement_results.defaultMessage'),
      }),
    },
  },
  [EAnnouncementResultsType.customerLockCard]: {
    [EAnnouncementResultsStatus.success]: {
      title: translate('announcement_results.lock_card_success'),
      icon: 'result-success',
      message: translate('announcement_results.defaultMessage'),
    },
    [EAnnouncementResultsStatus.fail]: {
      title: translate('announcement_results.lock_card_fail'),
      icon: 'result-error',
      message: translate(`errors.${errorCode}.description` as I18nKeys, {
        defaultValue: translate('announcement_results.defaultMessage'),
      }),
    },
  },
  [EAnnouncementResultsType.updateDebitPaymentAccount]: {
    [EAnnouncementResultsStatus.success]: {
      title: translate('announcement_results.update_debit_payment_account_success'),
      icon: 'result-success',
      message: translate('announcement_results.defaultMessage'),
    },
    [EAnnouncementResultsStatus.fail]: {
      title: translate('announcement_results.update_debit_payment_account_error'),
      icon: 'result-error',
      message: translate(`errors.${errorCode}.description` as I18nKeys, {
        defaultValue: translate('announcement_results.update_debit_payment_account_error_content'),
      }),
    },
  },
  [EAnnouncementResultsType.autoDebitPayment]: {
    [EAnnouncementResultsStatus.success]: {
      title: translate('announcement_results.auto_debit_payment_success'),
      icon: 'result-success',
      message: translate('announcement_results.defaultMessage'),
    },
    [EAnnouncementResultsStatus.fail]: {
      title: translate('announcement_results.auto_debit_payment_error'),
      icon: 'result-error',
      message: translate(`errors.${errorCode}.description` as I18nKeys, {
        defaultValue: translate('announcement_results.auto_debit_payment_error_content'),
      }),
    },
  },
  [EAnnouncementResultsType.openCard]: {
    [EAnnouncementResultsStatus.success]: {
      title: translate('announcement_results.open_card_success'),
      icon: 'result-success',
      message: translate('announcement_results.open_card_success_content'),
      boldText: translate('announcement_results.open_card_success_content_bold'),
      file: {
        iconName: MSBIcons.IconFile,
        title: 'Lưu hợp đồng',
        onPress: () => console.log('xxxx'),
      },
    },
    [EAnnouncementResultsStatus.fail]: {
      title: translate('announcement_results.open_card_error'),
      icon: 'result-error',
      message: translate(`errors.${errorCode}.description` as I18nKeys, {
        defaultValue: translate('announcement_results.open_card_error_content'),
      }),
    },
  },
});

export const getAnnouncementResultUI = (
  type: EAnnouncementResultsType,
  status: EAnnouncementResultsStatus,
  errorCode?: string,
): IGetAnnouncementResultUI | null => {
  const typeConfig = getAnnouncementResultConfig(errorCode)[type];
  if (!typeConfig) {
    return null;
  }

  return typeConfig[status] ?? null;
};

export const mapCardToCardDataNoticeScreen = (card: Card): CardDataNoticeScreen => ({
  cardId: card?.id || '',
  imageUrl: card?.cardVisual?.images?.[0]?.imageURL ?? '',
  instrumentI18n: card?.instrumentI18n ?? '',
  name: card?.name ?? '',
  markedNumber: card?.maskedNumber ?? '',
});
