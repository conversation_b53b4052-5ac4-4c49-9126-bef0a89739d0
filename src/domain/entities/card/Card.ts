import {Benefit} from './Benefit';
import {CardDomainStatus} from './CardDomainStatus';
import {CardFeature} from './CardFeature';
import {CardGeneralInfo} from './CardGeneralInfo';
import {CardHolder} from './CardHolder';
import {CardInstrument} from './CardInstrument';
import {CardType} from './CardType';
import {CardVisual} from './CardVisual';
import {OwnerShip} from './OwnerShip';
import {PaymentLevelSelector} from './PaymentLevelSelector';
import {SubCard} from './SubCard';

export type Card = {
  id: string;
  brand: string;
  type?: CardType;
  name: string;
  status?: CardDomainStatus;
  ownership: OwnerShip;
  holder: CardHolder;
  currency: string;
  maskedNumber: string;
  cardVisual: CardVisual;
  instrument: CardInstrument;
  instrumentI18n: string;
  productCode: string;
  productId: string;
  availableBalance?: number;
  creditLimit?: number;
  rbsNumber: string;
  features: CardFeature[];
  spentAmount?: number;
  remainingInstallments?: number;
  subCards: SubCard[];
  paymentRate?: PaymentLevelSelector;
  benefits: Benefit[];
};

export const mapFromCardToGeneralInfo = (card: Card): CardGeneralInfo => {
  return {
    imageUrl: card?.cardVisual?.images?.[0]?.imageURL ?? '',
    instrumentI18n: card?.instrumentI18n ?? '',
    name: card?.name ?? '',
    status: card?.status ?? undefined,
    markedNumber: card?.maskedNumber ?? '',
    ownership: card?.ownership ?? OwnerShip.Main,
  };
};
