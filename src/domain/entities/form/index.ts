import {CustomError} from '@core/MSBCustomError';
import {FormAdditionalInformation} from './FormAdditionalInformation';
import {FormCardProduct} from './FormCardProduct';
import {FormChooseCardType} from './FormChooseCardType';

export type FormCardOpen = FormChooseCardType & FormAdditionalInformation & FormCardProduct;

export interface CardOpenFormData extends FormCardOpen {
  loading: boolean;
  error?: CustomError;
}
