import {SelectionItem} from '../base/SelectionItem';
import {ReferralInformation} from '../card/ReferralInformation';
import {FormChooseCardType} from './FormChooseCardType';

export interface FormAdditionalInformation extends Pick<FormChooseCardType, 'instrument'> {
  province: SelectionItem | null;
  district: SelectionItem | null;
  ward: SelectionItem | null;
  referralInformation: ReferralInformation | null;
  sourceAccount: SourceAccountModel | null;
  defaultSourceAccount: string;
  address: string;
  hasSecurityQuestion: boolean;
  secretQuestion: string;
  referralCode: string;
}
