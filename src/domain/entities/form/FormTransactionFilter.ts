import {SelectionItem} from '../base/SelectionItem';

export enum TransactionType {
  ALL,
  INCOME,
  OUTCOME,
}

export enum TransactionDateRange {
  OTHER = 0,
  '7_DAYS' = 7,
  '15_DAYS' = 15,
}

export interface FormTransactionFilter {
  searchTerm: string;
  transactionType: SelectionItem;
  transactionDateRange: SelectionItem | null;
  fromDate: Date | null;
  toDate: Date | null;
  fromCurrency: string;
  toCurrency: string;
}
