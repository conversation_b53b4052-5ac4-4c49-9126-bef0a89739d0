import {PaginationMetadata} from '../base/PaginationMetadata';

export interface ListLinkAccount {
  metadata: PaginationMetadata;
  data: LinkAccount[];
}

export interface LinkAccount {
  id: string;
  name: string;
  availableBalance: number;
  currency: string;
}

export enum LinkAccountItemType {
  HEADER_SECTION,
  DEFAULT,
  ITEM,
}

export type LinkAccountItem<Type = LinkAccountItemType> = Type extends LinkAccountItemType.HEADER_SECTION
  ? {
      type: LinkAccountItemType.HEADER_SECTION;
    }
  : Type extends LinkAccountItemType.DEFAULT
  ? {
      type: LinkAccountItemType.DEFAULT;
      data: LinkAccount;
    }
  : {
      type: LinkAccountItemType.ITEM;
      data: LinkAccount;
    };
