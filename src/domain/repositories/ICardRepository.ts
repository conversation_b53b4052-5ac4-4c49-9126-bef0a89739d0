import {ResultState} from '@core/ResultState';
import {ReferralInformation} from '@domain/entities/card/ReferralInformation';
import {GetReferralInformationInput} from '@domain/use-cases/common/GetReferralInformationUseCase';
import {InitOpenCard} from '@entities/card/InitOpenCard';
import {AutoDebitPaymentInput} from '@use-cases/card/AutoDebitPaymentUseCase';
import {GetFilterTransactionHistoryInput} from '@use-cases/card/GetFilterTransactionHistoryUseCase';
import {UpdateDebitAccountPaymentInput} from '@use-cases/card/UpdateDebitAccountPaymentUseCase';
import {CommonState} from '../entities/base/CommonState';
import {Card} from '../entities/card/Card';
import {CardSecretInfo} from '../entities/card/CardSecretInfo';
import {History, ListHistory} from '../entities/card/History';
import {ChangeCardStatusInput} from '../use-cases/card/ChangeCardStatusUseCase';
import {CreateNewPinInput} from '../use-cases/card/CreateNewPinUseCase';
import {GetCardSecretInfoInput} from '../use-cases/card/GetCardSecretInfoUseCase';
import {GetDetailHistoryInput} from '../use-cases/card/GetDetailHistoryUseCase';
import {GetListHistoryInput} from '../use-cases/card/GetListHistoryUseCase';

export interface ICardRepository {
  getListCard(): Promise<ResultState<Card[]>>;
  getDetailCard(cardId: string): Promise<ResultState<Card>>;
  changeCardStatus(input: ChangeCardStatusInput): Promise<ResultState<CommonState>>;
  getCardSecretInfo(input: GetCardSecretInfoInput): Promise<ResultState<CommonState<CardSecretInfo>>>;
  createNewPin(input: CreateNewPinInput): Promise<ResultState<CommonState>>;
  getListHistory(input: GetListHistoryInput): Promise<ResultState<ListHistory>>;
  getDetailHistory(input: GetDetailHistoryInput): Promise<ResultState<History>>;
  updateDebitAccountPayment(input: UpdateDebitAccountPaymentInput): Promise<ResultState<CommonState>>;
  autoDebitPayment(input: AutoDebitPaymentInput): Promise<ResultState<CommonState>>;
  getFilterTransactionHistory(input: GetFilterTransactionHistoryInput): Promise<ResultState<ListHistory>>;
  getAllowedCardProducts(): Promise<ResultState<Card[]>>;
  initializeCardOpeningFlow(): Promise<ResultState<InitOpenCard>>;
  getReferralInformation(input: GetReferralInformationInput): Promise<ResultState<ReferralInformation>>;
}
