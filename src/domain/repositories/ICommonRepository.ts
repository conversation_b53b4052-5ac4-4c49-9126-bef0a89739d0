import {ResultState} from '@core/ResultState';
import {Branch} from '@entities/common/Branch';
import {District} from '@entities/common/District';
import {Province} from '@entities/common/Province';
import {Ward} from '@entities/common/Ward';
import {GetDistrictsInput} from '@use-cases/common/GetDistrictsUseCase';
import {GetWardsInput} from '@use-cases/common/GetWardsUseCase';

export interface ICommonRepository {
  getProvinces(): Promise<ResultState<Province[]>>;
  getDistricts(input: GetDistrictsInput): Promise<ResultState<District[]>>;
  getWards(input: GetWardsInput): Promise<ResultState<Ward[]>>;
  getBranches(): Promise<ResultState<Branch[]>>;
}
