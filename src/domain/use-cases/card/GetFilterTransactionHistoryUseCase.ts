import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {BaseInput} from '@entities/base/BaseInput';
import {ListHistory} from '@entities/card/History';

export class GetFilterTransactionHistoryUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: GetFilterTransactionHistoryInput): Promise<ResultState<ListHistory>> {
    // call this.repository.getFilterTransactionHistory(...)
    return this.repository.getFilterTransactionHistory(input);
  }
}

export interface GetFilterTransactionHistoryInput extends BaseInput {
  cardId: string;
  page: number;
  size: number;
  from: string;
  to: string;
  keySearch: string | null;
  amountMin: number | null;
  amountMax: number | null;
  transType: string | null;
  categoryCode: string[] | null;
}
