import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {InitOpenCard} from '@entities/card/InitOpenCard';

export class InitializeCardOpeningFlowUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<InitOpenCard>> {
    // call this.repository.initializeCardOpeningFlow(...)
    return this.repository.initializeCardOpeningFlow();
  }
}
