import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {CommonState} from '@entities/base/CommonState';

export class CreateNewPinUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: CreateNewPinInput): Promise<ResultState<CommonState>> {
    // call this.repository.createNewPin(...)
    return this.repository.createNewPin(input);
  }
}

export interface CreateNewPinInput {
  // add more input
  cardId: string;
  newPin: string;
}
