import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {CommonState} from '@entities/base/CommonState';
import {Way4CardStatusCode} from '@entities/card/Way4CardStatusCode';

export class ChangeCardStatusUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: ChangeCardStatusInput): Promise<ResultState<CommonState>> {
    // call this.repository.changeCardStatus(...)
    return this.repository.changeCardStatus(input);
  }
}

export interface ChangeCardStatusInput {
  cardId: string;
  newStatus: Way4CardStatusCode;
  skipTransactionSigning: boolean;
}
