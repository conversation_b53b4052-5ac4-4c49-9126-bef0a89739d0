import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {CommonState} from '@entities/base/CommonState';

export class UpdateDebitAccountPaymentUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: UpdateDebitAccountPaymentInput): Promise<ResultState<CommonState>> {
    // call this.repository.updateDebitAccountPayment(...)
    return this.repository.updateDebitAccountPayment(input);
  }
}

export interface UpdateDebitAccountPaymentInput {
  cardId: string;
  accountId: string;
}
