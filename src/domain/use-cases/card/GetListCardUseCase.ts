import {ResultState} from '@core/ResultState';
import {Card} from '@entities/card/Card';

import {ICardRepository} from '@domain/repositories/ICardRepository';
export class GetListCardUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<Card[]>> {
    // call this.repository.getListCard(...)
    return this.repository.getListCard();
  }
}
