import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {Card} from '@entities/card/Card';
export class GetDetailCardUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(cardId: string): Promise<ResultState<Card>> {
    // call this.repository.getDetailCard(...)
    return this.repository.getDetailCard(cardId);
  }
}
