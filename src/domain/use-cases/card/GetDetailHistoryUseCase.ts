import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {History} from '@entities/card/History';
export class GetDetailHistoryUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: GetDetailHistoryInput): Promise<ResultState<History>> {
    // call this.repository.getDetailHistory(...)
    return this.repository.getDetailHistory(input);
  }
}

export interface GetDetailHistoryInput {
  cardId: string;
  page: number;
  size: number;
  from: string;
  to: string;
}
