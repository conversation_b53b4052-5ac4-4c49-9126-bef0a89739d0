import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {CommonState} from '@entities/base/CommonState';
import {PaymentLevelSelector} from '@entities/card/PaymentLevelSelector';

export class AutoDebitPaymentUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: AutoDebitPaymentInput): Promise<ResultState<CommonState>> {
    return this.repository.autoDebitPayment(input);
  }
}

export interface AutoDebitPaymentInput {
  cardId: string;
  accountId: string;
  paymentRate: PaymentLevelSelector;
}
