import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {CommonState} from '@entities/base/CommonState';
import {CardSecretInfo} from '@entities/card/CardSecretInfo';
export class GetCardSecretInfoUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: GetCardSecretInfoInput): Promise<ResultState<CommonState<CardSecretInfo>>> {
    // call this.repository.getCardSecretInfo(...)
    return this.repository.getCardSecretInfo(input);
  }
}

export interface GetCardSecretInfoInput {
  cardId: string;
}
