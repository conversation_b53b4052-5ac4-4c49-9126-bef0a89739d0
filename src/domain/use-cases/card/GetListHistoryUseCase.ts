import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {ListHistory} from '@entities/card/History';
export class GetListHistoryUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: GetListHistoryInput): Promise<ResultState<ListHistory>> {
    // call this.repository.getListHistory(...)
    return this.repository.getListHistory(input);
  }
}

export interface GetListHistoryInput {
  cardId: string;
  page: number;
  size: number;
  from: string;
  to: string;
}
