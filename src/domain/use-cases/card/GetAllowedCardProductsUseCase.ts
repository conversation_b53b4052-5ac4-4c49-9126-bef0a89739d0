import {ResultState} from '@core/ResultState';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {Card} from '@entities/card/Card';

export class GetAllowedCardProductsUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<Card[]>> {
    // call this.repository.getAllowedCardProducts(...)
    return this.repository.getAllowedCardProducts();
  }
}

export interface GetAllowedCardProductsInput {
  // add more input
}
