import {ResultState} from '@core/ResultState';
import {BaseInput} from '@domain/entities/base/BaseInput';
import {ICommonRepository} from '@domain/repositories/ICommonRepository';
import {Province} from '@entities/common/Province';

export class GetProvincesUseCase {
  private readonly repository: ICommonRepository;

  constructor(repository: ICommonRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<Province[]>> {
    // call this.repository.getProvinces(...)
    return this.repository.getProvinces();
  }
}

export interface GetProvincesInput extends BaseInput {
  // add more input
}
