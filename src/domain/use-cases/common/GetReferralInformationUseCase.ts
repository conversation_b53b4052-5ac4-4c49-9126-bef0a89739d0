import {ResultState} from '@core/ResultState';
import {BaseInput} from '@domain/entities/base/BaseInput';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {ReferralInformation} from '@entities/card/ReferralInformation';

export class GetReferralInformationUseCase {
  private readonly repository: ICardRepository;

  constructor(repository: ICardRepository) {
    this.repository = repository;
  }

  public async execute(input: GetReferralInformationInput): Promise<ResultState<ReferralInformation>> {
    return this.repository.getReferralInformation(input);
  }
}

export interface GetReferralInformationInput extends BaseInput {
  referralCode: string;
}
