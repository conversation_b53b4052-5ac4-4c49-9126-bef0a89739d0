import {ResultState} from '@core/ResultState';
import {BaseInput} from '@domain/entities/base/BaseInput';
import {ICommonRepository} from '@domain/repositories/ICommonRepository';
import {District} from '@entities/common/District';

export class GetDistrictsUseCase {
  private readonly repository: ICommonRepository;

  constructor(repository: ICommonRepository) {
    this.repository = repository;
  }

  public async execute(input: GetDistrictsInput): Promise<ResultState<District[]>> {
    // call this.repository.getDistricts(...)
    return this.repository.getDistricts(input);
  }
}

export interface GetDistrictsInput extends BaseInput {
  provinceId: string;
}
