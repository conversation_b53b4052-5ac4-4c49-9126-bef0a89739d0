import {ResultState} from '@core/ResultState';
import {BaseInput} from '@domain/entities/base/BaseInput';
import {ICommonRepository} from '@domain/repositories/ICommonRepository';
import {Ward} from '@entities/common/Ward';

export class GetWardsUseCase {
  private readonly repository: ICommonRepository;

  constructor(repository: ICommonRepository) {
    this.repository = repository;
  }

  public async execute(input: GetWardsInput): Promise<ResultState<Ward[]>> {
    // call this.repository.getWards(...)
    return this.repository.getWards(input);
  }
}

export interface GetWardsInput extends BaseInput {
  provinceId: string;
  districtId: string;
}
