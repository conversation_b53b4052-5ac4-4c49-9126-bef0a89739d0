import {ResultState} from '@core/ResultState';
import {ICommonRepository} from '@domain/repositories/ICommonRepository';
import {Branch} from '@entities/common/Branch';

export class GetBranchesUseCase {
  private readonly repository: ICommonRepository;

  constructor(repository: ICommonRepository) {
    this.repository = repository;
  }

  public async execute(): Promise<ResultState<Branch[]>> {
    // call this.repository.getBranchs(...)
    return this.repository.getBranches();
  }
}
