import {ResultState} from '@core/ResultState';
import {IDailyRepository} from '@domain/repositories/IDailyRepository';
import {ListLinkAccount} from '@entities/daily/ListLinkAccount';

export class GetListLinkAccountUseCase {
  private readonly repository: IDailyRepository;

  constructor(repository: IDailyRepository) {
    this.repository = repository;
  }

  public async execute(input: GetListLinkAccountInput): Promise<ResultState<ListLinkAccount>> {
    // call this.repository.getListLinkAccount(...)
    return this.repository.getListLinkAccount(input);
  }
}

export interface GetListLinkAccountInput {
  from: number;
}
