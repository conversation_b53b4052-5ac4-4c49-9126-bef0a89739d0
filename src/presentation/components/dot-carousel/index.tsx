import React, {useMemo, useRef, useState} from 'react';
import {Style<PERSON>rop, View, ViewStyle} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  scrollTo,
  SharedValue,
  useAnimatedReaction,
  useAnimatedRef,
  useDerivedValue,
  useSharedValue,
} from 'react-native-reanimated';
import type {CarouselState, DecreasingDot, DotConfig, ScrollableDotConfig} from './type';

import {useMSBStyles} from 'msb-shared-component';
import Dot from './dot';
import InvisibleFiller from './invisible-filler';
import {styleSheet} from './styles';

export interface CarouselDotsProps {
  length: number;
  currentIndex: SharedValue<number>;
  progress: SharedValue<number>;
  maxIndicators: number;
  activeIndicatorConfig: DotConfig;
  inactiveIndicatorConfig: DotConfig;
  decreasingDots: DecreasingDot[];
  verticalOrientation?: boolean;
}

const calculateDotSize = (dot: DotConfig): number => {
  'worklet';
  return dot.width + 2 * dot.margin;
};
const calculateDecreasingDotSize = (dot: DecreasingDot): number => {
  'worklet';
  return calculateDotSize(dot.config) * (dot.quantity * 2);
};
const calculateIndicatorDotSize = (
  maxIndicators: number,
  activeIndicatorConfig: DotConfig,
  inactiveIndicatorConfig: DotConfig,
): number => {
  'worklet';
  return calculateDotSize(activeIndicatorConfig) + calculateDotSize(inactiveIndicatorConfig) * (maxIndicators - 1);
};

const calculateOffsetSize = (decreasingDot: DecreasingDot[], offset: number): number => {
  'worklet';
  const minimumSize = calculateDotSize(decreasingDot[decreasingDot.length - 1].config);
  const result = decreasingDot.reduce(
    (acc, dot) => {
      if (acc.offset === 0) {
        return acc;
      }
      if (acc.offset - dot.quantity <= 0) {
        return {
          offset: 0,
          totalSize: acc.totalSize + calculateDotSize(dot.config) * acc.offset,
        };
      }
      return {
        offset: acc.offset - dot.quantity,
        totalSize: acc.totalSize + calculateDotSize(dot.config) * dot.quantity,
      };
    },
    {offset, totalSize: 0},
  );
  return result.totalSize + result.offset * minimumSize;
};
const CarouselDots = ({
  length,
  currentIndex,
  progress,
  maxIndicators,
  activeIndicatorConfig,
  inactiveIndicatorConfig,
  decreasingDots,
  verticalOrientation = false,
}: CarouselDotsProps): React.JSX.Element => {
  const {styles} = useMSBStyles(styleSheet);

  const refScrollView = useAnimatedRef<Animated.ScrollView>();
  const positiveMomentum = useRef<boolean>(false);
  const carouselState = useRef<CarouselState>({
    currentIndex: 0,
    state: 1,
  });
  const list = useMemo(() => [...Array(length).keys()], [length]);

  const offsetSizeMap = useDerivedValue(() => {
    const map: Record<number, number> = {};

    for (let i = 1 - maxIndicators; i < length; i++) {
      map[i] = calculateOffsetSize(decreasingDots, i);
    }

    return map;
  }, [decreasingDots, length, maxIndicators]);

  useAnimatedReaction(
    () => currentIndex.value,
    (curIndex, prevIndex) => {
      if (curIndex === prevIndex) {
        return;
      }

      positiveMomentum.current = curIndex - (prevIndex ?? 0) > 0;
      let internalState = carouselState.current.state;
      internalState += curIndex - (prevIndex ?? 0);
      const finalState = internalState;
      if (internalState > maxIndicators) {
        internalState = maxIndicators;
      }
      if (internalState < 1) {
        internalState = 1;
      }
      if (internalState) {
        carouselState.current = {
          currentIndex: curIndex,
          state: internalState,
        };
      }

      if (length > maxIndicators && (finalState > maxIndicators || finalState < 1)) {
        const moveTo = positiveMomentum.current
          ? offsetSizeMap.value[Math.round(curIndex - maxIndicators + 1)]
          : offsetSizeMap.value[Math.round(curIndex)];

        scrollTo(refScrollView, moveTo ?? 0, 0, true);
      }
    },
    [length, maxIndicators, offsetSizeMap],
  );

  const containerSize = useMemo(() => {
    return (
      decreasingDots.reduce((acc, current) => calculateDecreasingDotSize(current) + acc, 0) +
      calculateIndicatorDotSize(maxIndicators, activeIndicatorConfig, inactiveIndicatorConfig)
    );
  }, [activeIndicatorConfig, decreasingDots, inactiveIndicatorConfig, maxIndicators]);

  const dotStyle = useMemo<StyleProp<ViewStyle>>(
    () =>
      verticalOrientation ? {flex: 1, height: containerSize} : {flex: 1, flexDirection: 'row', width: containerSize},
    [verticalOrientation, containerSize],
  );

  if (length <= maxIndicators) {
    return (
      <View style={styles.container}>
        {list.map(i => {
          return (
            <Dot
              key={i}
              index={i}
              length={length}
              animValue={progress}
              activeIndicatorConfig={activeIndicatorConfig}
              inactiveIndicatorConfig={inactiveIndicatorConfig}
              verticalOrientation={verticalOrientation}
            />
          );
        })}
      </View>
    );
  }
  const invisibleFillerSize = decreasingDots.reduce((acc, current) => calculateDecreasingDotSize(current) + acc, 0) / 2;

  return (
    <View style={dotStyle}>
      <Animated.ScrollView
        ref={refScrollView}
        contentContainerStyle={[styles.scrollContainer]}
        bounces={false}
        horizontal={!verticalOrientation}
        scrollEnabled={false}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}>
        <InvisibleFiller size={invisibleFillerSize} verticalOrientation={verticalOrientation} />
        {list.map(i => {
          return (
            <Dot
              key={i}
              index={i}
              length={length}
              animValue={progress}
              activeIndicatorConfig={activeIndicatorConfig}
              inactiveIndicatorConfig={inactiveIndicatorConfig}
              verticalOrientation={verticalOrientation}
            />
          );
        })}
        <InvisibleFiller size={invisibleFillerSize} verticalOrientation={verticalOrientation} />
      </Animated.ScrollView>
    </View>
  );
};

interface CarouselDotsWrapperProps extends CarouselDotsProps {
  scrollableDotsConfig?: ScrollableDotConfig;
}

const MIN_TRANSLATION_DOT_ANIMATION = 15;

const CustomCarouselDots = ({scrollableDotsConfig, ...rest}: CarouselDotsWrapperProps) => {
  const {styles} = useMSBStyles(styleSheet);

  const [dotsCarouselActive, setDotsCarouselActive] = useState(false);
  const accDesplacementPos = useSharedValue(0);
  const accDesplacementNeg = useSharedValue(0);
  const lastTranslationX = useSharedValue(0);
  const prevMomentum = useSharedValue<null | boolean>(null);
  const lastXOfMomentum = useSharedValue<null | number>(null);
  const lastCallTime = useSharedValue(0);
  const throttleDelay = 150;

  const handleGoUp = (up: boolean) => {
    const prevActive = scrollableDotsConfig?.setIndex.value ?? 0;
    const newActive = up ? Math.min(prevActive + 1, rest.length - 1) : Math.max(prevActive - 1, 0);
    scrollableDotsConfig?.onNewIndex?.(newActive);
  };

  const throttledHandleGoUp = (momentum: boolean) => {
    const now = Date.now();
    if (now - lastCallTime.value >= throttleDelay) {
      lastCallTime.value = now;
      runOnJS(handleGoUp)(momentum);
    }
  };
  const gesture = Gesture.Pan()
    .onStart(() => {
      accDesplacementPos.value = 0;
      accDesplacementNeg.value = 0;
      runOnJS(setDotsCarouselActive)(true);
    })
    .onUpdate(e => {
      const momentum = e.translationX - lastTranslationX.value >= 0;
      lastTranslationX.value = e.translationX;
      if (prevMomentum.value !== momentum) {
        lastXOfMomentum.value = e.translationX;
        prevMomentum.value = momentum;
        accDesplacementPos.value = 0;
        accDesplacementNeg.value = 0;
      }
      if (
        momentum &&
        e.translationX >= MIN_TRANSLATION_DOT_ANIMATION + accDesplacementPos.value + (lastXOfMomentum.value ?? 0)
      ) {
        accDesplacementPos.value = e.translationX - (lastXOfMomentum.value ?? 0);
        runOnJS(throttledHandleGoUp)(true);
      } else if (
        !momentum &&
        e.translationX <= -MIN_TRANSLATION_DOT_ANIMATION + accDesplacementNeg.value + (lastXOfMomentum.value ?? 0)
      ) {
        accDesplacementNeg.value = e.translationX - (lastXOfMomentum.value ?? 0);
        runOnJS(throttledHandleGoUp)(false);
      }
    })
    .onEnd(() => runOnJS(setDotsCarouselActive)(false));

  return scrollableDotsConfig ? (
    <GestureDetector gesture={gesture}>
      <View
        style={[
          scrollableDotsConfig.container || styles.scrollableDotsContainer,
          dotsCarouselActive && {
            backgroundColor: scrollableDotsConfig.containerBackgroundColor || 'rgba(230,230,230, 0.5)',
          },
        ]}>
        <View style={{height: rest.activeIndicatorConfig.height}}>
          <CarouselDots {...rest} />
        </View>
      </View>
    </GestureDetector>
  ) : (
    <View
      style={{
        height: rest.activeIndicatorConfig.height + (rest.activeIndicatorConfig.borderWidth ?? 0),
      }}>
      <CarouselDots {...rest} />
    </View>
  );
};
export default CustomCarouselDots;
