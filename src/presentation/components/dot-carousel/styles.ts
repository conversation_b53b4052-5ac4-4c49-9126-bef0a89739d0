import {createMSBStyleSheet} from 'msb-shared-component';
import {I18nManager} from 'react-native';

export const styleSheet = createMSBStyleSheet(() => ({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
  },
  scrollableDotsContainer: {
    alignItems: 'center',
    borderRadius: 15,
    height: 30,
    justifyContent: 'center',
    paddingHorizontal: 15,
  },
}));
