import React, {useMemo} from 'react';
import {View} from 'react-native';
import {InvisibleFillerProps} from '../type';
import styles from './styles';

const InvisibleFiller: React.FC<InvisibleFillerProps> = ({size, verticalOrientation}) => {
  const style = useMemo(() => {
    return {
      height: verticalOrientation ? size : 1,
      width: verticalOrientation ? 1 : size,
    };
  }, [verticalOrientation]);

  return <View style={[styles.container, style]} />;
};

export default InvisibleFiller;
