import type {StyleProp, ViewStyle} from 'react-native';
import {SharedValue} from 'react-native-reanimated';

export interface DotConfig {
  height: number;
  opacity: number;
  color: string;
  margin: number;
  borderWidth?: number;
  borderColor?: string;
  borderRadius: number;
  width: number; // Add this line for custom width
}
export interface DecreasingDot {
  quantity: number;
  config: DotConfig;
}

export interface CarouselState {
  currentIndex: number;
  state: number;
}

export interface ScrollableDotConfig {
  setIndex: SharedValue<number>;
  onNewIndex?: (index: number) => void;
  containerBackgroundColor: string;
  container?: StyleProp<ViewStyle>;
}

export interface InvisibleFillerProps {
  size: number;
  verticalOrientation: boolean;
}

export interface DotProps {
  activeIndicatorConfig: DotConfig;
  inactiveIndicatorConfig: DotConfig;
  index: number;
  animValue: SharedValue<number>;
  verticalOrientation: boolean;
  length: number;
}
