import {getDotStyle} from '../utils';

const activeConfig = {color: 'red', width: 10, height: 10, opacity: 1, borderRadius: 5, margin: 2};
const inactiveConfig = {color: 'gray', width: 8, height: 8, opacity: 0.5, borderRadius: 4, margin: 2};
const decreasingDots = [
  {config: {color: 'blue', width: 6, height: 6, opacity: 0.3, borderRadius: 3, margin: 2}, quantity: 1},
  {config: {color: 'green', width: 4, height: 4, opacity: 0.2, borderRadius: 2, margin: 2}, quantity: 2},
];

describe('getDotStyle', () => {
  it('trả về activeIndicatorConfig nếu index là currentIndex', () => {
    const result = getDotStyle({
      index: 2,
      currentIndex: 2,
      maxIndicators: 5,
      activeIndicatorConfig: activeConfig,
      inactiveIndicatorConfig: inactiveConfig,
      decreasingDots,
      indicatorState: 3,
    });
    expect(result).toEqual(activeConfig);
  });

  it('tr<PERSON> về inactiveIndicatorConfig nếu index nằm trong khoảng hiển thị nhưng không phải currentIndex', () => {
    const result = getDotStyle({
      index: 1,
      currentIndex: 2,
      maxIndicators: 5,
      activeIndicatorConfig: activeConfig,
      inactiveIndicatorConfig: inactiveConfig,
      decreasingDots,
      indicatorState: 3,
    });
    expect(result).toEqual(inactiveConfig);
  });

  it('trả về config của decreasingDots nếu index nằm ngoài khoảng hiển thị', () => {
    const result = getDotStyle({
      index: 0,
      currentIndex: 4,
      maxIndicators: 3,
      activeIndicatorConfig: activeConfig,
      inactiveIndicatorConfig: inactiveConfig,
      decreasingDots,
      indicatorState: 2,
    });
    expect(result).toEqual(decreasingDots[decreasingDots.length - 1].config);
  });

  it('trả về config của decreasingDots phù hợp nếu index nằm trong vùng decreasing', () => {
    const result = getDotStyle({
      index: 0,
      currentIndex: 2,
      maxIndicators: 3,
      activeIndicatorConfig: activeConfig,
      inactiveIndicatorConfig: inactiveConfig,
      decreasingDots,
      indicatorState: 2,
    });
    // Trường hợp này sẽ lấy config của decreasingDots[0]
    expect(result).toEqual(decreasingDots[0].config);
  });
});
