import React, {useMemo} from 'react';
import {StyleProp, ViewStyle} from 'react-native';
import Animated, {Extrapolation, interpolate, interpolateColor, useAnimatedStyle} from 'react-native-reanimated';
import type {DotProps} from '../type';

const Dot = ({
  activeIndicatorConfig,
  inactiveIndicatorConfig,
  index,
  verticalOrientation,
  animValue,
  length,
}: DotProps): React.JSX.Element => {
  const animStyle = useAnimatedStyle(() => {
    const activeWidth = activeIndicatorConfig.width;
    const width = inactiveIndicatorConfig.width;

    const activeHeight = activeIndicatorConfig.height;
    const height = inactiveIndicatorConfig.height;

    const activeBackgroundColor = activeIndicatorConfig.color;
    const backgroundColor = inactiveIndicatorConfig.color;

    const activeBorderRadius = activeIndicatorConfig.borderRadius;
    const borderRadius = inactiveIndicatorConfig.borderRadius;

    let val = Math.abs(animValue?.value - index);

    if (index === 0 && animValue?.value > length - 1) {
      val = Math.abs(animValue?.value - length);
    }

    const inputRange = [0, 1, 2];

    return {
      width: interpolate(val, inputRange, [activeWidth, width, width], Extrapolation.CLAMP),
      height: interpolate(val, inputRange, [activeHeight, height, height], Extrapolation.CLAMP),
      borderRadius: interpolate(
        val,
        inputRange,
        [activeBorderRadius ?? borderRadius ?? 0, borderRadius ?? 0, borderRadius ?? 0],
        Extrapolation.CLAMP,
      ),
      backgroundColor: interpolateColor(val, inputRange, [activeBackgroundColor, backgroundColor, backgroundColor]),
    };
  }, [index, length, animValue]);

  const dotStyle = useMemo<StyleProp<ViewStyle>>(() => {
    return {
      borderColor: inactiveIndicatorConfig?.borderColor,
      borderRadius: inactiveIndicatorConfig.height,
      borderWidth: inactiveIndicatorConfig?.borderWidth,
      height: inactiveIndicatorConfig.height,
      backgroundColor: inactiveIndicatorConfig.color,
      marginHorizontal: verticalOrientation ? 0 : inactiveIndicatorConfig.margin,
      marginVertical: verticalOrientation ? inactiveIndicatorConfig.margin : 0,
    };
  }, [inactiveIndicatorConfig, verticalOrientation]);

  return <Animated.View style={[dotStyle, animStyle]} />;
};

export default Dot;
