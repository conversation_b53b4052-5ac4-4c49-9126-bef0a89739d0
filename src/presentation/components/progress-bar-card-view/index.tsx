import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles.tsx';
import {ProgressBarCardViewProps} from './type';

const ProgressBarCardView: React.FC<ProgressBarCardViewProps> = ({progress}) => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <View style={styles.container}>
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground} />
        <View id="cm.progress-bar.progress" style={[styles.progressBar, {width: `${progress}%`}]} />
      </View>
    </View>
  );
};

export default ProgressBarCardView;
