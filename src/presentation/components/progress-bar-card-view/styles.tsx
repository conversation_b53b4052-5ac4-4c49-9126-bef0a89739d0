import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorGlobal}) => ({
  container: {
    marginTop: 16,
    alignItems: 'center',
  },
  progressContainer: {width: '100%', overflow: 'hidden', borderRadius: 6},
  progressBackground: {
    height: 6,
    width: '100%',
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: 6,
    borderWidth: 1.5,
    borderColor: ColorGlobal.Brand100,
    overflow: 'hidden',
  },
  progressBar: {
    borderRadius: 6,
    backgroundColor: ColorAlias.IconBrand,
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
  },
}));
