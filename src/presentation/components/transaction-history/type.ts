import {HistoryItem, HistoryItemType} from '@entities/card/History.ts';

export interface HistoryCardHeaderProps {
  title: string;
}

export interface HistoryCardItemProps {
  historyItem: HistoryItem<HistoryItemType.ITEM>;
  testID: string;
  isLast: boolean;
  loading: boolean;
  onPress?: () => void;
}

export interface HistoryFooterProps {
  hasError: boolean;
  isEmpty: boolean;
  loading: boolean;
  loadingMore: boolean;
}

export interface OnSearchTransaction {
  onSearchTransaction?: () => void;
}

export interface HistoryHeaderSectionProps extends OnSearchTransaction {
  isEmpty: boolean;
}
