import {sharedStyleSheet} from '@components/common/styles';
import {HistoryCardLoadMoreSkeleton, HistoryCardSkeleton} from '@components/loading-skeleton/HistoryCardSkeleton';
import {translate} from '@locales';
import {EmptyType, MSBEmptyState, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {HistoryFooterProps} from './type';

const HistoryFooter: React.FC<HistoryFooterProps> = ({hasError, isEmpty, loading, loadingMore}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  if (loading) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <HistoryCardSkeleton isLoading={true} />
      </View>
    );
  }

  if (loadingMore) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <HistoryCardLoadMoreSkeleton isLoading={true} />
      </View>
    );
  }

  if (hasError) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.history-card.empty-state"
          type={EmptyType.System}
          emptyTitle={translate('error_title')}
          emptySubTitle={translate('error_desc')}
        />
        <View style={styles.hideShadowEffect} />
      </View>
    );
  }

  if (isEmpty) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.history-card.empty-state"
          type={EmptyType.Editable}
          emptyTitle={translate('history_card.no_data')}
          emptySubTitle={translate('history_card.no_data_desc')}
        />
        <View style={styles.hideShadowEffect} />
      </View>
    );
  }

  return null;
};

export default HistoryFooter;
