import {sharedStyleSheet} from '@components/common/styles.ts';
import ShadowHorizontalLineView from '@components/line-view/ShadowHorizontalLineView.tsx';
import {TIME_24H_FORMAT} from '@constants';
import {convertDateTimeTo, formatAmountText} from '@utils';
import {MSBTextBase, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles.ts';
import {HistoryCardItemProps} from './type';

const HistoryCardItem: React.FC<HistoryCardItemProps> = ({testID, isLast, loading, historyItem, onPress}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const history = historyItem.data;

  return (
    <MSBTouchable
      testID={testID}
      activeOpacity={0.9}
      accessible={false}
      style={[sharedStyles.shadow, styles.itemContainer, !loading && isLast && styles.itemBorderBottom]}
      onPress={onPress}>
      <View style={styles.itemContentContainer}>
        <View testID={'cm.history-card-item.container'} style={styles.itemSubContentContainer}>
          <MSBTextBase type={Typography?.caption_regular} testID={'cm.history-card-item.title'} style={styles.title}>
            {history?.transDetails}
          </MSBTextBase>
          <View style={styles.itemDescContainer}>
            <View style={styles.itemMoneyContainer}>
              <MSBTextBase
                testID="cm.history-card-item.amount"
                type={Typography?.small_semiBold}
                style={history.drcr === '1' ? styles.itemMoneyIn : styles.itemMoneyOut}>
                {history.drcr === '1' ? '+' : '-'}
                {formatAmountText(history.transAmount)}
              </MSBTextBase>
              <MSBTextBase
                testID="cm.history-card-item.currency-label"
                type={Typography?.small_regular}
                style={styles.itemCurrency}>
                {history.currencyLabel}
              </MSBTextBase>
            </View>
            <MSBTextBase
              testID="cm.history-card-item.trans-date"
              type={Typography?.caption_medium}
              style={styles.itemTime}>
              {convertDateTimeTo(history.transDate ?? '', TIME_24H_FORMAT)}
            </MSBTextBase>
          </View>
        </View>
      </View>
      {!historyItem.isFirstSection && <ShadowHorizontalLineView padding={16} />}
    </MSBTouchable>
  );
};

export default HistoryCardItem;
