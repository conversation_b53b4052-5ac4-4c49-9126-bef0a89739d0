import {sharedStyleSheet} from '@components/common/styles.ts';
import {MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles.ts';
import {HistoryCardHeaderProps} from './type';

const HistoryCardHeader: React.FC<HistoryCardHeaderProps> = ({title}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  return (
    <View style={[sharedStyles.shadow, styles.historySectionContainer]}>
      <MSBTextBase type={Typography?.caption_regular} style={styles.historySectionTitle}>
        {title}
      </MSBTextBase>
    </View>
  );
};

export default HistoryCardHeader;
