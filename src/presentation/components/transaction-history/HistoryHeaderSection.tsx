import {sharedStyleSheet} from '@components/common/styles';
import {translate} from '@locales';
import {MSBIcon, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {HistoryHeaderSectionProps} from './type';

const HistoryHeaderSection: React.FC<HistoryHeaderSectionProps> = ({isEmpty, onSearchTransaction}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  return (
    <View style={[sharedStyles.shadow, styles.searchContainer]}>
      <MSBTextBase testID="cm.history-card.title" type={Typography?.base_medium} style={styles.title}>
        {translate('history_card.title')}
      </MSBTextBase>
      {!isEmpty && (
        <MSBIcon
          testID="cm.history-card.search-btn"
          icon="search-orange"
          iconSize={24}
          onIconClick={onSearchTransaction}
          styleContainer={styles.filterButtonStyle}
        />
      )}
    </View>
  );
};

export default HistoryHeaderSection;
