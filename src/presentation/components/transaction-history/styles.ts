import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorDataView, ColorGlobal, SizeAlias, SizeGlobal}) => ({
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 52,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: ColorGlobal.NeutralWhite,
    borderTopLeftRadius: SizeAlias.Radius4,
    borderTopRightRadius: SizeAlias.Radius4,
    marginTop: 24,
  },
  title: {
    flex: 1,
    color: ColorDataView.TextMain,
  },
  filterButtonStyle: {
    marginLeft: 8,
  },
  filterButtonIconStyle: {
    width: 24,
    height: 24,
  },
  cardErrorContainer: {
    overflow: 'visible',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: SizeAlias.Radius3,
  },
  errorContainer: {
    borderBottomLeftRadius: SizeAlias.Radius4,
    borderBottomRightRadius: SizeAlias.Radius4,
    overflow: 'visible',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  hideShadowEffect: {
    height: 2,
    position: 'absolute',
    top: -1,
    left: 0,
    right: 0,
    backgroundColor: ColorGlobal.NeutralWhite,
  },
  detailContainer: {
    flexDirection: 'column',
  },
  bgCardContainer: {
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeAlias.Radius3,
    padding: 16,
    width: '100%',
    flexDirection: 'column',
  },
  historySectionTitle: {
    flex: 1,
    color: ColorDataView.TextSub,
  },
  historySectionContainer: {
    paddingTop: 16,
    paddingBottom: SizeGlobal.Size100,
    paddingLeft: SizeGlobal.Size400,
    justifyContent: 'flex-end',
    backgroundColor: ColorAlias.BackgroundSecondary,
  },
  itemContainer: {
    paddingVertical: SizeAlias.SpacingSmall,
    paddingHorizontal: 16,
    backgroundColor: ColorGlobal.NeutralWhite,
  },
  itemContentContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  itemSubContentContainer: {
    flexDirection: 'row',
  },
  itemDescContainer: {
    flexDirection: 'column',
    gap: 4,
  },
  itemMoneyContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  itemMoneyIn: {
    textAlign: 'right',
    color: ColorAlias.TextSuccess,
  },
  itemMoneyOut: {
    textAlign: 'right',
    color: ColorAlias.TextPrimary,
  },
  itemCurrency: {
    paddingLeft: 4,
    color: ColorDataView.TextCurrency,
  },
  itemTime: {
    textAlign: 'right',
    color: ColorDataView.TextSub,
  },
  itemLine: {
    height: 1,
    backgroundColor: ColorAlias.BorderDefault,
  },
  itemBorderBottom: {
    borderBottomLeftRadius: SizeAlias.Radius4,
    borderBottomRightRadius: SizeAlias.Radius4,
  },
  col: {flexDirection: 'column'},
}));
