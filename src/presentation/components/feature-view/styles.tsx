import {getSize} from 'msb-shared-component';
import {createStyleSheet} from 'react-native-unistyles';

export const styleSheet = createStyleSheet(({ColorAlias, Typography}) => ({
  featureContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexDirection: 'column',
    paddingHorizontal: getSize(8),
    paddingVertical: getSize(12),
  },
  featureIcon: {
    width: 32,
    height: 32,
  },
  featureTitle: {
    ...Typography?.caption_regular,
    color: ColorAlias.TextPrimary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 6,
    textAlign: 'center',
  },
  rowFeaturesContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  rowFeaturesContainer2: {
    marginBottom: 16,
  },
  content: {gap: 16, paddingTop: 24},
}));
