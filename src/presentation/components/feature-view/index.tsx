import FastImage from '@d11/react-native-fast-image';
import {CardFeature} from '@entities/card/CardFeature.ts';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import {debounceTimes} from '@utils';
import {hostSharedModule} from 'msb-host-shared-module';
import {MSBIcon, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo, useRef} from 'react';
import {View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {Text} from '../text';
import {styleSheet} from './styles.tsx';
import {FeaturesViewHostProps, FeaturesViewProps, FeatureViewProps} from './type.ts';

const FeaturesViewHost: React.FC<FeaturesViewHostProps> = ({features, onCardFunction}) => {
  const {styles} = useMSBStyles(styleSheet);

  const {bottom} = useSafeAreaInsets();

  const debounceOnCardRef = useRef(
    debounceTimes((item: CardFeature) => {
      onCardFunction?.(item);
    }, 300),
  );

  const onPress = useCallback(
    (item: CardFeature) => {
      hostSharedModule?.d?.domainService?.hideBottomSheet();
      debounceOnCardRef.current(item);
    },
    [onCardFunction],
  );

  return (
    <BottomSheetFlatList
      testID={'cm.bottom-sheet.features-view-host'}
      data={features}
      numColumns={3}
      contentContainerStyle={[styles.content, {paddingBottom: bottom + 16}]}
      keyExtractor={(item, index) => `${item.id}-${index}`}
      renderItem={(data: {item: CardFeature; index: number}) => {
        return <FeatureView featureModel={data.item} onPress={() => onPress(data.item)} />;
      }}
    />
  );
};

const FeatureView: React.FC<FeatureViewProps> = ({featureModel, onPress}) => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <MSBTouchable testID={`cm.feature-view.${featureModel.id}`} style={styles.featureContainer} onPress={onPress}>
      {typeof featureModel.icon === 'string' ? (
        <MSBIcon icon={featureModel.icon} iconSize={32} onIconClick={onPress} />
      ) : (
        <FastImage style={styles.featureIcon} source={featureModel.icon} />
      )}
      <Text style={styles.featureTitle}>{featureModel.label}</Text>
    </MSBTouchable>
  );
};

const FeaturesView: React.FC<FeaturesViewProps> = ({features, onPress}) => {
  const {styles} = useMSBStyles(styleSheet);

  const sliceFeatures = useMemo(() => features?.slice(0, 3) || [], [features]);

  return (
    <View style={[styles.rowFeaturesContainer]}>
      {sliceFeatures.map(item => (
        <FeatureView key={`feature-view-${item.id}`} featureModel={item} onPress={() => onPress?.(item)} />
      ))}
    </View>
  );
};

export {FeaturesView, FeaturesViewHost};
