import {Block} from '@components/block';
import {Text} from '@components/text';
import {MSBIcon, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {CellPropsItemType, TableProps} from './types';

const Table: React.FC<TableProps> = ({tableHead, tableData, flexArr}) => {
  const {
    theme: {ColorTab, ColorGlobal, SizeTab, Typography},
  } = useMSBStyles();
  const renderHead = useCallback(() => {
    return (
      <Block
        borderTopWidth={1}
        borderBottomWidth={1}
        borderTopColor={ColorTab.BorderDefault}
        borderBottomColor={ColorTab.BorderDefault}
        direction="row"
        color={'rgba(247, 248, 249, 1)'}>
        {tableHead.map((head, index) => (
          <Block
            flex={flexArr[index]}
            key={`head-${index}`}
            paddingHorizontal={SizeTab.BaseSpacingVertical}
            paddingVertical={SizeTab.BaseSpacingVertical}>
            <Text type={Typography?.small_semiBold}>{head}</Text>
          </Block>
        ))}
      </Block>
    );
  }, [tableHead, flexArr]);

  const renderData = useCallback(() => {
    return tableData.map((row, rowIndex) => (
      <Block
        key={`row-${rowIndex}`}
        borderBottomWidth={1}
        borderBottomColor={ColorTab.BorderDefault}
        direction="row"
        alignItems="center"
        color={ColorGlobal.NeutralWhite}>
        {row.map((col, colIndex) => (
          <Block
            key={`col-${colIndex}`}
            flex={flexArr[colIndex]}
            paddingHorizontal={SizeTab.BaseSpacingVertical}
            paddingVertical={SizeTab.BaseSpacingVertical}>
            {col.type === CellPropsItemType.CHECK ? (
              <MSBIcon icon="tone-check" iconSize={24} />
            ) : (
              <Text type={Typography?.small_regular}>{col.label}</Text>
            )}
          </Block>
        ))}
      </Block>
    ));
  }, [tableData, flexArr]);

  return (
    <Block>
      {/* Head */}
      {renderHead()}
      {renderData()}
    </Block>
  );
};

export default Table;
