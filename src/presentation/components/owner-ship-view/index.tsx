import {translate} from '@locales';
import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {Text} from '../text';
import {styleSheet} from './styles.tsx';
import {OwnerShipViewProps} from './type.ts';

export const OwnerShipView: React.FC<OwnerShipViewProps> = ({style}) => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <View style={[styles.ownerShipContainer, style]}>
      <Text testID="cm.owner-ship-view.sub-card-title" style={styles.ownerShipText}>
        {translate('detail_card.sub_card_title')}
      </Text>
    </View>
  );
};
