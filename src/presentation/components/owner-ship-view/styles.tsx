import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorBottomSheet, ColorCard, Typography}) => ({
  ownerShipContainer: {
    borderWidth: 1,
    borderColor: ColorBottomSheet.BorderDragHandle,
    borderRadius: 50,
    paddingHorizontal: 8,
    alignSelf: 'flex-start',
  },
  ownerShipText: {
    ...Typography?.caption_medium,
    color: ColorCard.TextSubtitle,
  },
}));
