import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {sharedStyleSheet} from '../common/styles.ts';
import {styleSheet} from './styles.tsx';
import {RoundedBackgroundViewProps} from './type.ts';

const RoundedBackgroundView: React.FC<React.PropsWithChildren<RoundedBackgroundViewProps>> = ({
  children,
  style = {padding: 16},
}) => {
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const {styles} = useMSBStyles(styleSheet);

  return <View style={StyleSheet.flatten([styles.card, sharedStyles.shadow, style])}>{children}</View>;
};

export default RoundedBackgroundView;
