import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {AmountInputProps} from 'msb-shared-component';
import {Ref} from 'react';
import {Control, FieldPath, FieldValues} from 'react-hook-form';
import {StyleProp, ViewStyle} from 'react-native';

export interface FormButtonProps<T extends FieldValues> {
  testID?: string;
  control: Control<T, any, T>;
  label: string;
  isProcessing?: boolean;
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
}

export interface FormSectionProps {
  testID?: string;
  title: string;
  onPressInfo?: () => void;
}

export interface FormLineProps {
  testID?: string;
  left: string;
  right: string;
}

export interface InformProps {
  testID?: string;
  content: string;
}

export interface FormAddressSelectionProps<T extends FieldValues> extends FormControl<T> {
  ref?: Ref<FormAddressSelectionRef>;
  testID?: string;
  label: string;
  placeholder: string;
  disabled?: boolean;
  isCityMode?: boolean;
  isNormalMode?: boolean;
  placeholderSearch: string;
  getItems: () => Promise<SelectionItem[]>;
  /**
   * Callback is called when option is selected
   */
  onValueChange?: (current: SelectionItem, previous: Nullish<SelectionItem>) => void;
}

export interface FormAddressSelectionWatchProps<T extends FieldValues>
  extends Omit<FormAddressSelectionProps<T>, 'control'> {
  control: Control<T, any, T>;
  watchName: FieldPath<T>;
}

export interface FormAddressSelectionRef {
  trigger: () => void;
}

export interface FormTextAreaProps<T extends FieldValues> extends FormControl<T> {
  label: string;
  placeholder?: string;
}

export interface FormAmountInputProps<T extends FieldValues>
  extends FormControl<T>,
    Omit<AmountInputProps, 'onChangeText' | 'value'> {}

export interface FormDatePickerProps<T extends FieldValues> extends FormControl<T> {
  placeholder?: string;
  defaultDate?: string | Date;
  defaultTime?: string | Date;
  minDate?: string | Date;
  maxDate?: string | Date;
  formatDate?: string;
  type?: 'date' | 'time';
  testID?: string;
  label?: string;
  containerStyle?: ViewStyle;
  styleInputCustom?: ViewStyle;
  disabled?: boolean;
  errorContent?: string;
  hintText?: string;
}

export interface FormChipProps<T extends FieldValues> extends FormControl<T> {
  label: string;
  options: SelectionItem[];
  interceptor?: (previous: SelectionItem | null, next: SelectionItem) => void;
}

export interface FormRadioProps<T extends FieldValues> extends FormControl<T> {
  label: string;
  options: SelectionItem[];
}

export interface FormAmountSuggestProps<T extends FieldValues>
  extends Omit<FormControl<T>, 'control' | 'nameTrigger'> {}
