import {MSBTextAreaInput} from 'msb-shared-component';
import React from 'react';
import {FieldPath, FieldValues, useController} from 'react-hook-form';
import {Block} from '../block';
import {FormTextAreaProps} from './types';

const FormTextArea = <T extends FieldValues = FieldValues>({
  control,
  nameTrigger,
  label,
  placeholder,
  ...props
}: FormTextAreaProps<T>) => {
  const {field} = useController({
    control,
    name: nameTrigger as FieldPath<T>,
  });

  return (
    <Block>
      <MSBTextAreaInput
        {...props}
        label={label}
        value={field.value}
        onChangeText={field.onChange}
        placeholder={placeholder}
        onBlur={field.onBlur}
      />
    </Block>
  );
};

export default FormTextArea;
