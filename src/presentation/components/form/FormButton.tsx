import {MSBButton} from 'msb-shared-component';
import React from 'react';
import {FieldValues, useFormState} from 'react-hook-form';
import {FormButtonProps} from './types';

const FormButton = <T extends FieldValues>({
  control,
  label,
  style,
  isProcessing = false,
  onPress,
  ...props
}: FormButtonProps<T>) => {
  const {isValid, errors} = useFormState({control});
  console.log(errors, isValid, isProcessing);

  return <MSBButton {...props} disabled={!isValid || isProcessing} label={label} style={style} onPress={onPress} />;
};

export default FormButton;
