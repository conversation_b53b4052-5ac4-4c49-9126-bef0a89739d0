import {useAmountInputFocused} from '@presentation/store/AmountInputFocus';
import React, {useCallback} from 'react';
import {FieldPath, FieldPathValue, FieldValues} from 'react-hook-form';
import AmountSuggest from '../amount-suggest';
import {FormAmountSuggestProps} from './types';

const FormAmountSuggest = <T extends FieldValues>({setValue}: FormAmountSuggestProps<T>) => {
  const {isAmountInputNameFocused, amountSuggestList} = useAmountInputFocused();

  const fillAmountSuggest = useCallback(
    (amount: string) => {
      if (!isAmountInputNameFocused) {
        return;
      }
      setValue?.(isAmountInputNameFocused as FieldPath<T>, amount as FieldPathValue<T, FieldPath<T>>);
    },
    [isAmountInputNameFocused],
  );

  return (
    <AmountSuggest
      focused={isAmountInputNameFocused !== undefined}
      amountSuggest={amountSuggestList}
      onPress={fillAmountSuggest}
    />
  );
};

export default FormAmountSuggest;
