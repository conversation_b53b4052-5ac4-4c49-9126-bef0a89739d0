import {MSBDatePicker} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FieldPath, FieldValues, useController} from 'react-hook-form';
import {FormDatePickerProps} from './types';

const FormDatePicker = <T extends FieldValues = FieldValues>({
  control,
  nameTrigger,
  trigger,
  ...props
}: FormDatePickerProps<T>) => {
  const {field} = useController({
    control,
    name: nameTrigger as FieldPath<T>,
  });

  const onChangeDate = useCallback(
    (date: Date) => {
      field.onChange(date);
      trigger?.(field.name);
    },
    [field],
  );

  return <MSBDatePicker {...props} onChangeDate={onChangeDate} value={field.value} />;
};

export default FormDatePicker;
