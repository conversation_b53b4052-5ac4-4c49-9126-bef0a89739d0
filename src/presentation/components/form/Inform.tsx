import {getSize, MSBIcon, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {Block} from '../block';
import {Text} from '../text';
import {InformProps} from './types';

const Inform: React.FC<InformProps> = React.memo(({content}) => {
  const {
    theme: {ColorInform, SizeInform, SizeGlobal, Typography},
  } = useMSBStyles();
  return (
    <Block
      borderRadius={SizeInform.BorderRadius}
      borderWidth={SizeInform.BorderStroke}
      borderColor={ColorInform.BorderInfo}
      paddingVertical={SizeInform.SpacingTop}
      paddingHorizontal={SizeInform.SpacingLeft}
      gap={SizeGlobal.Size200}
      direction="row">
      <MSBIcon icon="info-tooltip-circle" iconSize={getSize(24)} />
      <Text type={Typography?.small_regular} flex={1}>
        {content}
      </Text>
    </Block>
  );
});

export default Inform;
