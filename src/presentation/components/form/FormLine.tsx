import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {Block} from '../block';
import {Text} from '../text';
import {FormLineProps} from './types';

const FormLine: React.FC<FormLineProps> = React.memo(({left, right}) => {
  const {
    theme: {Typography},
  } = useMSBStyles();
  return (
    <Block direction="row">
      <Text flex={1} type={Typography?.small_regular}>
        {left}
      </Text>
      <Text type={Typography?.small_medium}>{right}</Text>
    </Block>
  );
});

export default FormLine;
