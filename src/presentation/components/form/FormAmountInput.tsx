import {useErrorMessageTranslation} from '@presentation/hooks/useErrorMessageTranslation';
import {useAmountInputFocusActions} from '@store/AmountInputFocus';
import {generateAmountSuggestions, parseCurrencyToNumber} from '@utils/StringFormat';
import {MSBAmountInput} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FieldPath, FieldValues, useController} from 'react-hook-form';
import {FormAmountInputProps} from './types';

const FormAmountInput = <T extends FieldValues = FieldValues>({
  control,
  nameTrigger,
  trigger,
  onFocus,
  onBlur,
  ...props
}: FormAmountInputProps<T>) => {
  const {
    field,
    fieldState: {error},
  } = useController({
    control,
    name: nameTrigger as FieldPath<T>,
  });

  const {setIsFocused, resetFocus, setAmountSuggestList} = useAmountInputFocusActions();

  const generateSuggestList = useCallback(
    (currency: string) => {
      const amountSuggest = generateAmountSuggestions(parseCurrencyToNumber(currency).toString());
      setAmountSuggestList(amountSuggest);
    },
    [field, setAmountSuggestList],
  );

  const onWrapperBlur = useCallback(() => {
    onBlur?.();
    field.onBlur();
    trigger?.(field.name);
    resetFocus();
  }, [field, setIsFocused, trigger, onBlur]);

  const onWrapperFocus = useCallback(() => {
    onFocus?.();
    setIsFocused(field.name);
    generateSuggestList(field.value);
  }, [field, setIsFocused, resetFocus, generateSuggestList]);

  const onWrapperChangeText = useCallback(
    (text: string) => {
      field.onChange(text);
      generateSuggestList(text);
    },
    [field],
  );

  const message = useErrorMessageTranslation(error?.message);

  return (
    <MSBAmountInput
      {...props}
      onChangeText={onWrapperChangeText}
      value={field.value}
      onBlur={onWrapperBlur}
      onFocus={onWrapperFocus}
      errorContent={message}
      currency="null"
    />
  );
};

export default FormAmountInput;
