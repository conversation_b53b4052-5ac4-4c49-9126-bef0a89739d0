import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {MSBTouchableBox, TouchableBoxType, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FieldPath, FieldValues, useController} from 'react-hook-form';
import {Block} from '../block';
import {Text} from '../text';
import {FormRadioProps} from './types';

const FormRadio = <T extends FieldValues = FieldValues>({
  testID,
  control,
  nameTrigger,
  trigger,
  options,
  label,
}: FormRadioProps<T>) => {
  const {
    theme: {Typography, SizeGlobal},
  } = useMSBStyles();

  const {field} = useController({
    control,
    name: nameTrigger as FieldPath<T>,
  });

  const value = field.value as SelectionItem | null;

  const renderChips = useCallback(() => {
    return options.map(option => {
      return (
        <Block flex={1} key={`radio-${option.id}`}>
          <MSBTouchableBox
            testID={`cm.form-radio.${option.id}`}
            type={TouchableBoxType.Radio}
            onPress={() => {
              field.onChange(option);
              trigger?.();
            }}
            label={option.name}
            status={option.id === value?.id}
          />
        </Block>
      );
    });
  }, [options, value, trigger]);
  return (
    <Block testID={testID} gap={4}>
      <Text type={Typography?.small_medium}>{label}</Text>
      <Block direction="row" paddingVertical={SizeGlobal.Size200}>
        {renderChips()}
      </Block>
    </Block>
  );
};

export default FormRadio;
