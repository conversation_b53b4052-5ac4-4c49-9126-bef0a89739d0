import {MSBIcon, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {Block} from '../block';
import {sharedStyleSheet} from '../common/styles';
import HorizontalDivider from '../line-view/Divider';
import {Text} from '../text';
import {FormSectionProps} from './types';

const FormSection: React.FC<React.PropsWithChildren<FormSectionProps>> = React.memo(
  ({children, title, onPressInfo}) => {
    const {
      styles: sharedStyles,
      theme: {SizeAlias, SizeGlobal, ColorGlobal, Typography},
    } = useMSBStyles(sharedStyleSheet);
    return (
      <Block style={sharedStyles.shadow} borderRadius={SizeAlias.Radius3} color={ColorGlobal.NeutralWhite}>
        <Block padding={SizeGlobal.Size400} direction="row">
          <Text flex={1} type={Typography?.base_semiBold}>
            {title}
          </Text>
          {onPressInfo && (
            <MSBIcon
              testID="cm.form-section.tooltip"
              icon="tone-info-tooltip-circle"
              iconSize={20}
              onIconClick={onPressInfo}
            />
          )}
        </Block>
        <HorizontalDivider />
        <Block>{children}</Block>
      </Block>
    );
  },
);

export default FormSection;
