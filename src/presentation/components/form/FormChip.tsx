import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {MSBChip, MSBChipState, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FieldPath, FieldValues, useController} from 'react-hook-form';
import {Block} from '../block';
import {Text} from '../text';
import {FormChipProps} from './types';

const FormChip = <T extends FieldValues = FieldValues>({
  testID,
  control,
  nameTrigger,
  options,
  label,
  trigger,
  interceptor,
}: FormChipProps<T>) => {
  const {
    theme: {Typography, SizeGlobal},
  } = useMSBStyles();

  const {field} = useController({
    control,
    name: nameTrigger as FieldPath<T>,
  });

  const value = field.value as SelectionItem | null;

  const renderChips = useCallback(() => {
    return options.map(option => {
      return (
        <MSBTouchable
          testID={`cm.form-chip.${option.id}`}
          key={`chip-${option.id}`}
          debounce={false}
          onPress={() => {
            field.onChange(option);
            interceptor?.(value, option);
            trigger?.(field.name);
          }}>
          <MSBChip
            testID={`cm.form-chip.${option.id}.inner`}
            chipState={option.id === value?.id ? MSBChipState.Active : MSBChipState.Default}
            title={option.name}
          />
        </MSBTouchable>
      );
    });
  }, [field, options, value, trigger, interceptor]);
  return (
    <Block testID={testID} gap={4}>
      <Text type={Typography?.small_medium}>{label}</Text>
      <Block direction="row" paddingVertical={SizeGlobal.Size200} flexWrap={'wrap'} gap={8}>
        {renderChips()}
      </Block>
    </Block>
  );
};

export default FormChip;
