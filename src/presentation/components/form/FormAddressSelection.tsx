import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {isNil} from 'lodash';
import {hostSharedModule} from 'msb-host-shared-module';
import {MSBSelection} from 'msb-shared-component';
import React, {useCallback, useImperativeHandle} from 'react';
import {FieldPath, FieldValues, useController, useWatch} from 'react-hook-form';
import {ListSelectionWithBottomSheet} from '../bottom-sheet/list-selection';
import {FormAddressSelectionProps, FormAddressSelectionWatchProps} from './types';

const FormAddressSelection = <T extends FieldValues = FieldValues>({
  ref,
  testID,
  control,
  nameTrigger,
  defaultValue,
  label,
  placeholder,
  placeholderSearch = '',
  disabled = false,
  isNormalMode = true,
  isCityMode = false,
  onValueChange,
  getItems,
  trigger,
  ...props
}: FormAddressSelectionProps<T>) => {
  const {field} = useController({
    control,
    name: nameTrigger as FieldPath<T>,
    defaultValue,
  });

  const value = field.value as Nullish<SelectionItem>;

  useImperativeHandle(ref, () => ({
    trigger: onChange,
  }));

  const onSelectedItem = useCallback(
    (item: SelectionItem) => {
      hostSharedModule.d.domainService.hideBottomSheet();
      field.onChange(item);
      onValueChange?.(item, field.value);
      trigger?.(field.name);
    },
    [nameTrigger, field, onValueChange],
  );

  const onChange = useCallback(async () => {
    const data = await getItems();

    console.log('[FormAddressSelection]', nameTrigger, data.length);

    hostSharedModule.d.domainService?.showBottomSheet({
      header: placeholder,
      enableDynamicSizing: false,
      children: (
        <ListSelectionWithBottomSheet
          data={data}
          onSelectedItem={onSelectedItem}
          selectedItem={field.value}
          isNormalMode={isNormalMode}
          placeholder={placeholder}
          isCityMode={isCityMode}
        />
      ),
    });
  }, [getItems, placeholderSearch, isNormalMode, isCityMode, field, onSelectedItem, placeholder]);

  return (
    <MSBSelection
      {...props}
      label={label}
      value={value?.name ?? ''}
      placeholder={placeholder}
      onChange={onChange}
      disabled={disabled}
      testID={testID}
    />
  );
};

const FormAddressSelectionWatch = <T extends FieldValues = FieldValues>({
  watchName,
  ...props
}: FormAddressSelectionWatchProps<T>) => {
  const value = useWatch({control: props.control, name: watchName});

  return <FormAddressSelection {...props} disabled={isNil(value)} />;
};

export {FormAddressSelection, FormAddressSelectionWatch};
