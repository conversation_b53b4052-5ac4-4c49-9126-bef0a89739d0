import images from '@assets/images';
import {cardAspectRatio} from '@constants/sizes';
import FastImage from '@d11/react-native-fast-image';
import {getSize, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {Image, View} from 'react-native';
import Animated, {useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {styleSheet} from './styles';
import {CardImageProps} from './type';

const AnimatedImage = Animated.createAnimatedComponent(Image);

const CardOpenCardImage: React.FC<CardImageProps> = ({cardVisual, imageWidth = 96, ...props}) => {
  const {styles} = useMSBStyles(styleSheet);
  const visible = useSharedValue(1);
  const aspectRatio = useSharedValue(cardAspectRatio);

  const defaultAnimStyle = useAnimatedStyle(() => {
    return {
      opacity: visible.value,
    };
  });

  return (
    <View style={[styles.container, imageWidth ? {width: getSize(imageWidth)} : {}]}>
      <View style={styles.default}>
        <AnimatedImage
          resizeMode={props.resizeMode}
          style={[styles.default, defaultAnimStyle]}
          source={images.card_default}
        />
        <FastImage
          style={[styles.cardImage]}
          {...props}
          source={{
            uri: cardVisual?.images?.[0]?.imageURL ?? '',
          }}
          resizeMode="contain"
          onLoadStart={() => (visible.value = 1)}
          onLoadEnd={() => {
            visible.value = withTiming(0);
          }}
          onLoad={({nativeEvent}) => {
            if (nativeEvent.height > nativeEvent.width) {
              aspectRatio.value = nativeEvent.width / nativeEvent.height;
            }
          }}
        />
      </View>
    </View>
  );
};

export default CardOpenCardImage;
