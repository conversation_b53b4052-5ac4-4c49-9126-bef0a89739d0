import {SlideTabBarKey} from '@presentation/screens/list-card/types';
import {useAssetCreditCardSelectors, useTabSceneSelectors} from '@presentation/store/ListCard';
import {useTabViewModel} from '@presentation/view-models/useTabViewModel';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {hostSharedModule} from 'msb-host-shared-module';
import {useCallback, useEffect} from 'react';

export const useAssetCreditCard = () => {
  const navigation = useNavigation<NavigationProp<DefaultSegmentStackParamList>>();

  const {getCardList} = useTabViewModel();
  const {cards} = useTabSceneSelectors(SlideTabBarKey.Credits);
  const {error, loading} = useAssetCreditCardSelectors();

  useEffect(() => {
    getCardList();
  }, []);

  const goToCard = useCallback(() => {
    navigation.navigate('CardStack', {
      flow: 'ASSET_CREDIT_CARD',
    });
  }, [navigation]);

  const openNow = useCallback(() => {
    // navigation.navigate('CardStack', {
    //   flow: 'OPEN_NOW',
    // });
    hostSharedModule.d.domainService.undevelopedFeature();
  }, [navigation]);

  return {
    error,
    loading,
    cards,
    goToCard,
    getCardList,
    openNow,
  };
};
