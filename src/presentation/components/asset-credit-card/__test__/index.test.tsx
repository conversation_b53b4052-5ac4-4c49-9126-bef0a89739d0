import {CardDomainStatus} from '@domain/entities/card/CardDomainStatus';
import {CardType} from '@entities/card/CardType';
import {translate} from '@locales';
import {act, render, screen, userEvent, within} from '@presentation/__test__/test-utils';
import {NavigationContainer} from '@react-navigation/native';
import {formatMoney} from '@utils/StringFormat';
import {
  mockListCardResponse,
  mockResponseForGetListCard,
  mockServerFailureForGetListCard,
  mocResponseOnlyDebitForGetListCard,
} from 'mocks/service-apis/get-list-cards';
import React from 'react';
import {ReactTestInstance} from 'react-test-renderer';
import AssetCreditCard, {AssetEmptyView, AssetErrorView, AssetView} from '../index';

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => {
  return {
    ...jest.requireActual('@react-navigation/native'),
    useNavigation: () => ({
      navigate: mockNavigate,
    }),
  };
});

describe('Asset Credit Card', () => {
  afterAll(() => {
    jest.unmock('@react-navigation/native');
  });

  it('should render asset view when has credit cards', async () => {
    mockResponseForGetListCard();
    render(
      <NavigationContainer>
        <AssetCreditCard />
      </NavigationContainer>,
    );

    const creditCards = mockListCardResponse.filter(
      el => el.type === CardType.Credit && el.status !== CardDomainStatus.AccountClosed,
    );
    expect(((await screen.findByTestId('cm.asset-card-credit.container')).children[0] as ReactTestInstance).type).toBe(
      AssetView,
    );
    expect(
      await screen.findByText(
        translate('asset_credit_card.credit_card_with_count', {
          count: creditCards.length,
        }),
      ),
    ).toBeOnTheScreen();

    const firstCreditCard = creditCards[0];
    expect(screen.getByText(formatMoney(firstCreditCard.availableBalance!))).toBeOnTheScreen();
    expect(screen.getByText(firstCreditCard.currency)).toBeOnTheScreen();
    const pressBtn = screen.getByTestId('cm.asset-credit-card.go-to-card');
    const event = userEvent.setup();
    await event.press(pressBtn);
    expect(mockNavigate).toHaveBeenLastCalledWith('CardStack', {flow: 'ASSET_CREDIT_CARD'});
  });

  it('should render empty view when not have credit cards', async () => {
    mocResponseOnlyDebitForGetListCard();
    render(
      <NavigationContainer>
        <AssetCreditCard />
      </NavigationContainer>,
    );
    const container = await screen.findByTestId('cm.asset-card-credit.container');

    expect((container.children[0] as ReactTestInstance).type).toBe(AssetEmptyView);
  });

  it('should show error view when server fails and render content after retry succeeds', async () => {
    mockServerFailureForGetListCard();
    render(
      <NavigationContainer>
        <AssetCreditCard />
      </NavigationContainer>,
    );
    const container = await screen.findByTestId('cm.asset-card-credit.container');

    expect((container.children[0] as ReactTestInstance).type).toBe(AssetErrorView);
    expect(within(container).getByText(translate('asset_credit_card.credit_card'))).toBeOnTheScreen();
    expect(within(container).getByText(translate('asset_credit_card.temporary_disruption'))).toBeOnTheScreen();

    mockResponseForGetListCard();
    const pressBtn = screen.getByTestId('cm.asset-credit-card.reload');
    const event = userEvent.setup();
    await event.press(pressBtn);

    expect(((await screen.findByTestId('cm.asset-card-credit.container')).children[0] as ReactTestInstance).type).toBe(
      AssetView,
    );
  });

  it('should show error view when server fails and render content after refreshing with ref', async () => {
    const ref = React.createRef<AssetCommonRef>();

    mockServerFailureForGetListCard();
    render(
      <NavigationContainer>
        <AssetCreditCard ref={ref} />
      </NavigationContainer>,
    );
    const container = await screen.findByTestId('cm.asset-card-credit.container');

    expect((container.children[0] as ReactTestInstance).type).toBe(AssetErrorView);
    expect(within(container).getByText(translate('asset_credit_card.credit_card'))).toBeOnTheScreen();
    expect(within(container).getByText(translate('asset_credit_card.temporary_disruption'))).toBeOnTheScreen();

    mockResponseForGetListCard();

    await act(async () => {
      await ref.current?.onRefresh?.();
    });

    expect(((await screen.findByTestId('cm.asset-card-credit.container')).children[0] as ReactTestInstance).type).toBe(
      AssetView,
    );
  });
});
