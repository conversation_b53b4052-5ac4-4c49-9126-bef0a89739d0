import {CardDomainStatus} from '@entities/card/CardDomainStatus';
import {translate, translations} from '@locales';
import {LocaleProvider} from 'msb-communication-lib';
import {useHostInjection} from 'msb-host-shared-module';
import {
  ButtonSize,
  ButtonType,
  MSBButton,
  MSBIcon,
  MSBIconButton,
  MSBLoadingItemSkeleton,
  MSBTouchable,
  useMSBStyles,
} from 'msb-shared-component';
import React, {useCallback, useImperativeHandle} from 'react';
import {Block} from '../block';
import MoneyView from '../money-view';
import {Text} from '../text';
import {useAssetCreditCard} from './hook';
import {AssetCreditCardProps, AssetEmptyViewProps, AssetErrorViewProps, AssetViewProps} from './types';

const AssetLoading = () => {
  const {
    theme: {SizeGlobal, <PERSON>ze<PERSON>lia<PERSON>, ColorCard, Shadow},
  } = useMSBStyles();
  return (
    <Block
      direction="row"
      alignItems="center"
      style={Shadow.center}
      borderRadius={SizeAlias.Radius3}
      gap={SizeGlobal.Size300}
      color={ColorCard.SurfaceDefault}
      padding={SizeAlias.SpacingSmall}>
      <MSBLoadingItemSkeleton loading />
    </Block>
  );
};

export const AssetEmptyView: React.FC<AssetEmptyViewProps> = () => {
  // const {
  //   theme: {SizeGlobal, SizeDataView, Typography, ColorDataView, SizeAlias, ColorCard, ColorAlias, Shadow},
  // } = useMSBStyles();
  /**
   * Phase 1: ẩn block thẻ tín dụng
   * Phase 2: sau khi tính năng mở thẻ tín dụng onl → Block Thẻ TD hiển thị message “Quý khách chưa sở hữu sản phẩm Thẻ tín dụng”
   */
  return null;

  // @todo phase 2
  //   return (
  //     <MSBTouchable onPress={onPress}>
  //       <Block
  //         direction="row"
  //         alignItems="center"
  //         style={Shadow.center}
  //         borderRadius={SizeAlias.Radius3}
  //         gap={SizeGlobal.Size300}
  //         color={ColorCard.SurfaceDefault}
  //         padding={SizeAlias.SpacingSmall}>
  //         <Block flex={1} gap={SizeGlobal.Size200} direction="row">
  //           <MSBIcon icon="tone-card" iconSize={24} />
  //           <Block flex={1} gap={SizeDataView.SpacingTop}>
  //             <Text type={Typography?.small_medium} color={ColorDataView.TextMain}>
  //               {translate('asset_credit_card.credit_card')}
  //             </Text>
  //             <Text type={Typography?.small_regular} color={ColorAlias.TextSecondary}>
  //               {translate('asset_credit_card.no_credit_card_message')}
  //             </Text>
  //           </Block>
  //         </Block>

  //         <MSBButton
  //           onPress={onPress}
  //           label={translate('asset_credit_card.open_now')}
  //           buttonType={ButtonType.Tertiary}
  //           buttonSize={ButtonSize.Small}
  //         />
  //       </Block>
  //     </MSBTouchable>
  //   );
};

export const AssetErrorView: React.FC<AssetErrorViewProps> = ({onPress}) => {
  const {
    theme: {SizeGlobal, SizeDataView, Typography, ColorDataView, SizeAlias, ColorAlias, Shadow},
  } = useMSBStyles();
  return (
    <Block
      direction="row"
      alignItems="center"
      style={Shadow.center}
      borderRadius={SizeAlias.Radius3}
      gap={SizeGlobal.Size300}
      color={ColorAlias.SurfaceError}
      padding={SizeAlias.SpacingSmall}>
      <Block flex={1} gap={SizeGlobal.Size200} direction="row">
        <MSBIcon icon="tone-card" iconSize={24} />
        <Block flex={1} gap={SizeDataView.SpacingTop}>
          <Text type={Typography?.small_medium} color={ColorDataView.TextMain}>
            {translate('asset_credit_card.credit_card')}
          </Text>
          <Text type={Typography?.small_regular} color={ColorAlias.TextError}>
            {translate('asset_credit_card.temporary_disruption')}
          </Text>
        </Block>
      </Block>

      <MSBButton
        testID="cm.asset-credit-card.reload"
        onPress={onPress}
        label={translate('asset_credit_card.reload')}
        buttonType={ButtonType.Tertiary}
        buttonSize={ButtonSize.Small}
      />
    </Block>
  );
};

export const AssetView: React.FC<AssetViewProps> = ({label, availableBalance, currency, onPress}) => {
  const {
    theme: {SizeGlobal, SizeDataView, Typography, ColorDataView, ColorCard, SizeAlias, Shadow},
  } = useMSBStyles();
  return (
    <MSBTouchable onPress={onPress}>
      <Block
        direction="row"
        alignItems="center"
        style={Shadow.center}
        borderRadius={SizeAlias.Radius3}
        gap={SizeGlobal.Size300}
        color={ColorCard.SurfaceDefault}
        padding={SizeAlias.SpacingSmall}>
        <Block flex={1} gap={SizeGlobal.Size200} direction="row">
          <MSBIcon icon="tone-wallet" iconSize={24} />
          <Block flex={1} gap={SizeDataView.SpacingTop}>
            <Text type={Typography?.small_medium} color={ColorDataView.TextMain}>
              {label}
            </Text>
            <MoneyView type="base" money={availableBalance} currency={currency} />
          </Block>
        </Block>

        <MSBIconButton
          testID="cm.asset-credit-card.go-to-card"
          icon="right"
          buttonType={ButtonType.Tertiary}
          buttonSize={ButtonSize.Small}
          iconSize={24}
          onPress={onPress}
        />
      </Block>
    </MSBTouchable>
  );
};

const AssetCreditCard: React.FC<AssetCreditCardProps> = ({ref}) => {
  const {locale} = useHostInjection();
  const {error, loading, cards, goToCard, getCardList, openNow} = useAssetCreditCard();

  useImperativeHandle(ref, () => ({
    onRefresh: getCardList,
  }));

  const renderContent = useCallback(() => {
    if (loading) {
      return <AssetLoading />;
    }

    if (error) {
      return <AssetErrorView onPress={getCardList} />;
    }

    const listCardOK = cards.filter(card => card.status !== CardDomainStatus.CardClosed);

    if (listCardOK.length > 0) {
      return (
        <AssetView
          label={translate('asset_credit_card.credit_card_with_count', {
            count: listCardOK.length,
          })}
          availableBalance={listCardOK[0].availableBalance}
          currency={listCardOK[0].currency}
          onPress={goToCard}
        />
      );
    }

    return <AssetEmptyView onPress={openNow} />;
  }, [error, loading, cards]);

  return (
    <LocaleProvider translations={translations} defaultLocale={locale}>
      <Block testID="cm.asset-card-credit.container">{renderContent()}</Block>
    </LocaleProvider>
  );
};

export default AssetCreditCard;
