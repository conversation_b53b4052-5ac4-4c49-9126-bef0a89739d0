import {CardFeatureType} from '@entities/card/CardFeatureType';
import {LinkAccountItem, LinkAccountItemType} from '@entities/daily/ListLinkAccount';

export interface HeaderSectionProps {
  featureType: CardFeatureType;
}

export interface LinkAccountItemProps {
  featureType: CardFeatureType;
  isLast?: boolean;
  loadingMore?: boolean;
  activeRbsNumber: string;
  linking: LinkAccountItem<Exclude<LinkAccountItemType, LinkAccountItemType.HEADER_SECTION>>;
  onPress: (rbsNumber: string) => void;
}
