import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorCard, ColorDataView, ColorGlobal, SizeAlias}) => ({
  wrapItem: {marginHorizontal: 2, backgroundColor: ColorGlobal.NeutralWhite},
  itemContainer: {
    minHeight: 88,
    paddingHorizontal: 16,
    gap: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SizeAlias.SpacingSmall,
  },
  itemLast: {
    borderBottomLeftRadius: SizeAlias.Radius3,
    borderBottomRightRadius: SizeAlias.Radius3,
  },
  linkAccountContainer: {flexGrow: 1, paddingVertical: 8, gap: 4},
  linkAccountInfo: {flexDirection: 'row', alignItems: 'center', gap: 8},
  linkAccountName: {color: ColorDataView.TextSub, flex: 1},
  dot: {width: 4, height: 4, borderRadius: 2, backgroundColor: ColorAlias.SurfaceDisable},
  headerSectionContainer: {
    backgroundColor: ColorGlobal.NeutralWhite,
    marginHorizontal: 2,
    borderTopLeftRadius: SizeAlias.Radius3,
    borderTopRightRadius: SizeAlias.Radius3,
  },
  dividerContainer: {
    backgroundColor: ColorGlobal.NeutralWhite,
    position: 'absolute',
    left: 0,
    right: 0,
    top: -1,
    height: 3,
    justifyContent: 'flex-end',
  },
  divider: {backgroundColor: ColorCard.BorderDivider, height: 1},
  overflow: {overflow: 'visible'},
}));
