import {sharedStyleSheet} from '@components/common/styles';
import ShadowHorizontalLineView from '@components/line-view/ShadowHorizontalLineView';
import MoneyView from '@components/money-view';
import {LinkAccountItemType} from '@entities/daily/ListLinkAccount';
import {translate} from '@locales';
import {MSBTag, MSBTextBase, MSBTouchableBox, TagType, TouchableBoxType, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {Platform, TouchableOpacity, View} from 'react-native';
import {Block} from '../block';
import {styleSheet} from './styles';
import {LinkAccountItemProps} from './type';

/**
 * Để tránh cho phần shadow ở content của FlatList không bị cắt ở hai trên trái và phải
 * Workaround: Margin cho content container trừ đi 2px
 * Trong item của FlatList sẽ thêm container padding horizontal là 2px để fix issue trên
 *
 * Để section toàn bộ danh sách tài khoản liên kết có thể có shadow bọc ở riêng Android
 * Thì không thể dùng contentContainerStyle giống như ở bên iOS được
 * Nên mỗi item của FlatList sẽ cần shadow riêng có mình
 * Nhưng nó có một vấn đề là phần nhỏ shadow của item dưới sẽ ánh dưới item trên làm có một vệt nhỏ màu border
 * Để tránh issue đấy sẽ dùng
 * Workaround: cho phép overflow visible ở các item
 * Và tạo một nền trằng có height là 3px và move top là -(3-1)/2 = -1 so với vị trí top
 * => Để nó che đi các vệt do shadow này
 */
export const LinkAccountItemUI: React.FC<LinkAccountItemProps> = ({
  featureType,
  isLast = false,
  loadingMore = false,
  linking,
  activeRbsNumber,
  onPress,
}) => {
  const {
    styles,
    theme: {ColorDataView, Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  const active = activeRbsNumber === linking.data.id;

  const renderTag = useCallback(() => {
    if (linking.type !== LinkAccountItemType.DEFAULT) {
      return null;
    }

    return <MSBTag content={translate('update_debit_payment_account.linking')} color={TagType.normal} />;
  }, [linking, activeRbsNumber]);

  const onPressItem = useCallback(() => onPress(linking.data.id), [onPress, linking.data.id]);

  return (
    <TouchableOpacity
      testID={`cm.account-payment.${linking.data.id}`}
      activeOpacity={0.9}
      onPress={onPressItem}
      style={[
        Platform.OS === 'android' && styles.overflow,
        sharedStyles.shadow,
        styles.wrapItem,
        !loadingMore && isLast && styles.itemLast,
      ]}>
      <View style={[styles.itemContainer]}>
        <View style={styles.linkAccountContainer}>
          <View style={styles.linkAccountInfo}>
            <MSBTextBase type={Typography?.small_regular} style={{color: ColorDataView.TextSub}}>
              {linking.data.id}
            </MSBTextBase>
            <View style={styles.dot} />
            <MSBTextBase type={Typography?.small_regular} numberOfLines={1} style={styles.linkAccountName}>
              {linking.data.name}
            </MSBTextBase>
          </View>
          <MoneyView money={linking.data.availableBalance} currency={linking.data.currency} />
          {renderTag()}
        </View>

        <Block width={24} height={24}>
          <MSBTouchableBox type={TouchableBoxType.Radio} status={active} onPress={onPressItem} />
        </Block>
      </View>

      <ShadowHorizontalLineView padding={linking.type === LinkAccountItemType.DEFAULT ? 0 : 16} />
    </TouchableOpacity>
  );
};
