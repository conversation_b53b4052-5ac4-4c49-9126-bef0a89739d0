import {sharedStyleSheet} from '@components/common/styles';
import {CardFeatureType} from '@entities/card/CardFeatureType';
import {translate} from '@locales';
import {MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React, {useMemo} from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {HeaderSectionProps} from './type';

export const HeaderSection: React.FC<HeaderSectionProps> = ({featureType}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  const title = useMemo(() => {
    switch (featureType) {
      case CardFeatureType.AutoDebit:
        return translate('automatic_debit_payment.select_payment_account');
      case CardFeatureType.LinkedAccount:
        return translate('update_debit_payment_account.select_link_account');

      default:
        return '';
    }
  }, [featureType]);

  return (
    <View style={[sharedStyles.shadow, styles.headerSectionContainer]}>
      <View style={styles.headerSection}>
        <MSBTextBase type={Typography?.base_semiBold}>{title}</MSBTextBase>
      </View>
    </View>
  );
};
