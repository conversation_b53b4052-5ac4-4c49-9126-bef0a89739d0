import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {Platform, StyleProp, ViewStyle} from 'react-native';
import Skeleton from 'react-native-reanimated-skeleton';
import {sharedStyleSheet} from '../common/styles';
import {getGeneralCardLayout, linkAccountItemLayout} from './layout';
import {UpdateDebitPaymentAccountSkeletonProps} from './types';

export const UpdateDebitPaymentAccountSkeleton: React.FC<
  React.PropsWithChildren<UpdateDebitPaymentAccountSkeletonProps>
> = ({theme, isLoading, containerStyle, children}) => {
  const {ColorGlobal, SizeAlias} = theme;
  return (
    <Skeleton
      containerStyle={[containerStyle]}
      isLoading={isLoading}
      layout={[
        {
          paddingHorizontal: 16,
          paddingVertical: 24,
          gap: 24,
          children: [
            {
              backgroundColor: ColorGlobal.NeutralWhite,
              padding: 16,
              borderRadius: SizeAlias.Radius3,
              children: [getGeneralCardLayout(theme)],
            },
            {
              backgroundColor: ColorGlobal.NeutralWhite,
              borderRadius: SizeAlias.Radius3,
              children: [
                {
                  height: 56,
                  justifyContent: 'center',
                  paddingHorizontal: 16,
                  children: [
                    {
                      width: '60%',
                      height: 24,
                    },
                  ],
                },
                {width: 'auto', height: 1},
                linkAccountItemLayout({
                  isDefault: true,
                }),
                ...Array(3)
                  .fill(0)
                  .map(_ => linkAccountItemLayout({})),
                linkAccountItemLayout({isLast: true}),
              ],
            },
          ],
        },
      ]}>
      {children}
    </Skeleton>
  );
};

interface LoadLinkAccountSkeletonProps {
  isLoading: boolean;
  containerStyle?: StyleProp<ViewStyle>;
}

export const LoadLinkAccountSkeleton: React.FC<React.PropsWithChildren<LoadLinkAccountSkeletonProps>> = ({
  isLoading,
  containerStyle,
  children,
}) => {
  const {
    styles: sharedStyles,
    theme: {ColorGlobal, SizeAlias},
  } = useMSBStyles(sharedStyleSheet);
  return (
    <Skeleton
      containerStyle={[containerStyle]}
      isLoading={isLoading}
      layout={[
        {
          backgroundColor: ColorGlobal.NeutralWhite,
          marginHorizontal: 2,
          borderBottomRightRadius: SizeAlias.Radius3,
          borderBottomLeftRadius: SizeAlias.Radius3,
          ...Platform.select({
            android: sharedStyles.shadow,
          }),
          children: [
            {
              height: 1,
              width: 'auto',
              marginHorizontal: 16,
            },
            linkAccountItemLayout({isLast: true}),
          ],
        },
      ]}>
      {children}
    </Skeleton>
  );
};
