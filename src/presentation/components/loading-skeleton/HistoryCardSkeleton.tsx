import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import Skeleton from 'react-native-reanimated-skeleton';
import {getHistoryCardLayout, getHorizontalDividerLayout, getItemTransactionLogLayout} from './layout';
import {styleSheet} from './styles';
import {BaseSkeletonProps} from './types';

export const HistoryCardSkeleton: React.FC<React.PropsWithChildren<BaseSkeletonProps>> = ({isLoading, children}) => {
  const {styles, theme} = useMSBStyles(styleSheet);
  return (
    <Skeleton isLoading={isLoading} containerStyle={styles.fullW} layout={[getHistoryCardLayout(theme)]}>
      {children}
    </Skeleton>
  );
};

interface HistoryCardLoadMoreSkeletonProps {
  isLoading: boolean;
}

export const HistoryCardLoadMoreSkeleton: React.FC<React.PropsWithChildren<HistoryCardLoadMoreSkeletonProps>> = ({
  isLoading,
  children,
}) => {
  const {styles, theme} = useMSBStyles(styleSheet);
  return (
    <Skeleton
      isLoading={isLoading}
      containerStyle={styles.fullW}
      layout={[getHorizontalDividerLayout(theme), getItemTransactionLogLayout(theme)]}>
      {children}
    </Skeleton>
  );
};
