import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import Skeleton from 'react-native-reanimated-skeleton';
import {getFormLayout, getFormSectionLayout, getLinkAccountLayout, getTextAreaLayout} from './layout';
import {styleSheet} from './styles';
import {CardOpenRegistrationInformationSkeletonProps} from './types';

const CardOpenRegistrationInformationSkeleton: React.FC<
  React.PropsWithChildren<CardOpenRegistrationInformationSkeletonProps>
> = ({isLoading, children, containerStyle}) => {
  const {theme} = useMSBStyles(styleSheet);
  return (
    <Skeleton
      isLoading={isLoading}
      containerStyle={containerStyle}
      layout={[
        getFormSectionLayout(theme, [getLinkAccountLayout(theme), getFormLayout(true), getFormLayout()]),
        getFormSectionLayout(theme, [getFormLayout(), getFormLayout(), getFormLayout(), getTextAreaLayout(theme)]),
      ]}>
      {children}
    </Skeleton>
  );
};

export default CardOpenRegistrationInformationSkeleton;
