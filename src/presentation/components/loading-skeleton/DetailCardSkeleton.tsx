import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import Skeleton from 'react-native-reanimated-skeleton';
import {
  getDataViewHorizontalLayout,
  getFeatureViewLayout,
  getGeneralCardLayout,
  getHistoryCardLayout,
  getHorizontalDividerLayout,
} from './layout';
import {styleSheet} from './styles';
import {BaseSkeletonProps} from './types';

const DetailCardSkeleton: React.FC<React.PropsWithChildren<BaseSkeletonProps>> = ({isLoading, children}) => {
  const {styles, theme} = useMSBStyles(styleSheet);
  return (
    <Skeleton
      isLoading={isLoading}
      containerStyle={styles.detailCardContainer}
      layout={[
        {
          paddingHorizontal: 16,
          paddingVertical: 24,
          gap: 24,
          children: [
            {
              backgroundColor: theme.ColorGlobal.NeutralWhite,
              borderRadius: theme.SizeAlias.Radius3,
              padding: 16,
              width: '100%',
              flexDirection: 'column',
              gap: 16,
              children: [
                getGeneralCardLayout(theme),
                {width: '100%', height: 1},
                {
                  gap: 12,
                  children: [getDataViewHorizontalLayout(theme), getDataViewHorizontalLayout(theme)],
                },
              ],
            },
            {
              backgroundColor: theme.ColorGlobal.NeutralWhite,
              borderRadius: theme.SizeAlias.Radius4,
              width: '100%',
              flexDirection: 'column',
              children: [
                {paddingVertical: 8, children: [getFeatureViewLayout(theme)]},
                getHorizontalDividerLayout(theme),
                {
                  padding: 16,
                  gap: 8,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  children: [
                    {width: 24, height: 24},
                    {width: 56, height: 16},
                  ],
                },
              ],
            },
            {
              backgroundColor: theme.ColorGlobal.NeutralWhite,
              borderRadius: theme.SizeAlias.Radius4,
              width: '100%',
              flexDirection: 'column',
              children: [
                {
                  height: 52,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingHorizontal: 16,
                  children: [
                    {width: '50%', height: 24},
                    {width: 32, height: 32},
                  ],
                },
                getHistoryCardLayout(theme),
              ],
            },
          ],
        },
      ]}>
      {children}
    </Skeleton>
  );
};

export default DetailCardSkeleton;
