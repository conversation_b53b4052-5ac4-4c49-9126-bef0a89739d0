import {ThemeMainType} from 'msb-shared-component';
import {StyleProp, ViewStyle} from 'react-native';
import {ICustomViewStyle} from 'react-native-reanimated-skeleton';

export interface BaseSkeletonProps {
  isLoading: boolean;
}

export interface LinkAccountItemLayoutParams {
  isLast?: boolean;
  isDefault?: boolean;
}

export interface UpdateDebitPaymentAccountSkeletonProps extends BaseSkeletonProps {
  containerStyle: StyleProp<ViewStyle>;
  theme: ThemeMainType;
}

export interface CardOpenRegistrationInformationSkeletonProps extends BaseSkeletonProps {
  containerStyle: StyleProp<ViewStyle>;
}

export type ThemeCustomViewStyle = (theme: ThemeMainType) => ICustomViewStyle;
export type ThemeCustomViewStyleWithChildren = (theme: ThemeMainType, children: ICustomViewStyle[]) => ICustomViewStyle;
