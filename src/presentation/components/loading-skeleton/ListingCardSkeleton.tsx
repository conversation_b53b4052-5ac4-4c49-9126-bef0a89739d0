import React from 'react';
import Skeleton, {ICustomViewStyle} from 'react-native-reanimated-skeleton';
import {styleSheet} from './styles';

import {screenDimensions} from '@constants/sizes';
import {ThemeMainType, useMSBStyles} from 'msb-shared-component';
import {getFeatureViewLayout} from './layout';
import {BaseSkeletonProps} from './types';

export const getListingCardLayout: (theme: ThemeMainType) => ICustomViewStyle[] = theme => {
  const {ColorGlobal, SizeAlias, SizeButton, SizeGlobal} = theme;
  const SLIDE_WIDTH = screenDimensions.width * 0.85;
  const SIDE_WIDTH = screenDimensions.width * 0.075 - SizeGlobal.Size400;
  const ITEM_GAP = SizeGlobal.Size400;
  const BANNER_WIDTH = screenDimensions.width * 0.76 - ITEM_GAP;
  const BANNER_HEIGHT = (BANNER_WIDTH * 120) / 273;

  return [
    {
      width: screenDimensions.width,
      height: SizeGlobal.Size1200,
      backgroundColor: ColorGlobal.NeutralWhite,
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      children: [
        {width: 95, height: 24},
        {width: 80, height: 24},
      ],
    },
    {
      width: screenDimensions.width,
      flexDirection: 'row',
      justifyContent: 'space-between',
      children: [
        {
          width: SIDE_WIDTH,
          height: '100%',
          borderTopRightRadius: SizeAlias.Radius3,
          borderBottomRightRadius: SizeAlias.Radius3,
          backgroundColor: ColorGlobal.NeutralWhite,
          children: [{width: 0, height: 0}],
        },
        {
          width: SLIDE_WIDTH,
          borderRadius: SizeAlias.Radius3,
          backgroundColor: ColorGlobal.NeutralWhite,
          gap: ITEM_GAP,
          padding: ITEM_GAP,
          flexDirection: 'column',
          children: [
            {
              width: '100%',
              height: ((SLIDE_WIDTH - 2 * ITEM_GAP) * 5398) / 8560,
            },
            {
              width: '100%',
              flexDirection: 'row',
              gap: ITEM_GAP,
              alignItems: 'center',
              children: [
                {
                  flex: 1,
                  gap: 4,
                  children: [
                    {width: '70%', height: 24},
                    {width: '30%', height: 20},
                  ],
                },
                {width: 32, height: 32, borderRadius: SizeButton.BorderRadius},
              ],
            },

            {
              width: '100%',
              gap: 8,
              justifyContent: 'space-between',
              children: [
                {width: '100%', height: 6},
                {
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  children: [
                    {
                      flexDirection: 'column',
                      gap: 4,
                      children: [
                        {width: 52, height: 16},
                        {width: 140, height: 24},
                      ],
                    },
                    {
                      flexDirection: 'column',
                      alignItems: 'flex-end',
                      gap: 4,
                      children: [
                        {width: 140, height: 16},
                        {width: 100, height: 24},
                      ],
                    },
                  ],
                },
              ],
            },
            {
              marginHorizontal: -16,
              marginTop: 8,
              children: [getFeatureViewLayout(theme)],
            },
          ],
        },
        {
          width: SIDE_WIDTH,
          height: '100%',
          borderTopLeftRadius: SizeAlias.Radius3,
          borderBottomLeftRadius: SizeAlias.Radius3,
          backgroundColor: ColorGlobal.NeutralWhite,
          children: [{width: 0, height: 0}],
        },
      ],
    },
    {
      marginTop: 26,
      width: screenDimensions.width,
      paddingLeft: ITEM_GAP,
      gap: ITEM_GAP,
      flexDirection: 'row',
      children: [
        {
          width: BANNER_WIDTH,
          height: BANNER_HEIGHT,
          borderRadius: SizeAlias.Radius3,
          backgroundColor: ColorGlobal.NeutralWhite,
          padding: 8,
          children: [{width: '100%', height: '100%'}],
        },
        {
          flex: 1,
          height: BANNER_HEIGHT,
          borderTopLeftRadius: SizeAlias.Radius3,
          borderBottomLeftRadius: SizeAlias.Radius3,
          backgroundColor: ColorGlobal.NeutralWhite,
          paddingVertical: 8,
          paddingLeft: 8,
          children: [{width: '100%', height: '100%'}],
        },
      ],
    },
  ];
};

const ListingCardSkeleton: React.FC<React.PropsWithChildren<BaseSkeletonProps>> = ({isLoading, children}) => {
  const {styles, theme} = useMSBStyles(styleSheet);

  return (
    <Skeleton isLoading={isLoading} containerStyle={styles.listingCardContainer} layout={getListingCardLayout(theme)}>
      {children}
    </Skeleton>
  );
};

export default ListingCardSkeleton;
