import {cardAspectRatio} from '@constants/sizes';
import {ICustomViewStyle} from 'react-native-reanimated-skeleton';
import {LinkAccountItemLayoutParams, ThemeCustomViewStyle, ThemeCustomViewStyleWithChildren} from './types';

export const getFeatureViewLayout: ThemeCustomViewStyle = () => ({
  width: '100%',
  flexDirection: 'row',
  justifyContent: 'center',
  children: Array(3).fill({
    flex: 1,
    height: 78,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    children: [
      {width: 32, height: 32},
      {width: 60, height: 16},
    ],
  }),
});

export const getDataViewHorizontalLayout: ThemeCustomViewStyle = ({SizeDataView}) => ({
  flexDirection: 'row',
  gap: SizeDataView.SpacingHorizontal,
  justifyContent: 'space-between',
  children: [
    {width: '45%', height: 20},
    {width: '40%', height: 20},
  ],
});

export const getHorizontalDividerLayout: ThemeCustomViewStyle = () => ({
  width: '100%',
  height: 1,
});

export const getItemTransactionSectionLayout: ThemeCustomViewStyle = ({SizeGlobal}) => ({
  height: 36,
  paddingBottom: SizeGlobal.Size100,
  paddingLeft: SizeGlobal.Size400,
  justifyContent: 'flex-end',
  children: [{width: '20%', height: 16}],
});

export const getItemTransactionLogLayout: ThemeCustomViewStyle = ({SizeAlias, SizeItem}) => ({
  paddingVertical: SizeAlias.SpacingSmall,
  paddingHorizontal: SizeAlias.SpacingSmall,
  gap: 4,
  flexDirection: 'row',
  justifyContent: 'space-between',
  children: [
    {
      flex: 1,
      height: 32,
      children: [{width: '100%', height: 16}],
    },
    {
      width: '40%',
      gap: SizeItem.SpacingGapVertical,
      height: 36,
      alignItems: 'flex-end',
      children: [
        {width: '60%', height: 16},
        {width: '15%', height: 16},
      ],
    },
  ],
});

export const getHistoryCardLayout: ThemeCustomViewStyle = theme => ({
  children: [
    getHorizontalDividerLayout(theme),
    getItemTransactionSectionLayout(theme),
    getItemTransactionLogLayout(theme),
    getHorizontalDividerLayout(theme),
    getItemTransactionLogLayout(theme),
    getItemTransactionSectionLayout(theme),
    getItemTransactionLogLayout(theme),
    getHorizontalDividerLayout(theme),
    getItemTransactionLogLayout(theme),
  ],
});

export const getGeneralCardLayout: ThemeCustomViewStyle = () => ({
  flexDirection: 'row',
  gap: 12,
  children: [
    {
      width: 113,
      aspectRatio: cardAspectRatio,
      borderRadius: 8,
    },
    {
      flex: 1,
      gap: 4,
      children: [
        {width: 53, height: 16},
        {
          gap: 4,
          children: [
            {width: '80%', height: 24},
            {width: '85%', height: 20},
          ],
        },
      ],
    },
  ],
});

export const linkAccountItemLayout = ({
  isLast = false,
  isDefault = false,
}: LinkAccountItemLayoutParams): ICustomViewStyle => {
  const itemChildren: ICustomViewStyle[] = [
    {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      children: [
        {width: '35%', height: 20},
        {width: 4, height: 4, borderRadius: 2},
        {width: '45%', height: 20},
      ],
    },
    {width: '50%', height: 24},
  ];
  if (isDefault) {
    itemChildren.push({width: 87, height: 24, borderRadius: 12});
  }

  const children: ICustomViewStyle[] = [
    {
      minHeight: 88,
      paddingLeft: 16,
      paddingRight: 16,
      gap: 10,
      flexDirection: 'row',
      alignItems: 'center',
      children: [
        {
          flex: 1,
          paddingVertical: 8,
          gap: 4,
          children: itemChildren,
        },
        {width: 24, height: 24},
      ],
    },
  ];
  if (!isLast) {
    children.push({width: 'auto', marginHorizontal: 16, height: 1});
  }

  return {
    marginHorizontal: 2,
    width: '100%',
    children,
  };
};

export const getLinkAccountLayout: ThemeCustomViewStyle = theme => {
  const {SizeGlobal} = theme;
  return {
    alignItems: 'center',
    flexDirection: 'row',
    gap: SizeGlobal.Size300,
    justifyContent: 'flex-start',
    children: [
      {
        width: 32,
        height: 32,
        borderRadius: 8,
      },
      {
        flexDirection: 'column',
        gap: SizeGlobal.Size100,
        children: [
          {
            width: 114,
            height: 24,
            borderRadius: 999,
            marginBottom: SizeGlobal.Size100,
          },
          {
            width: 238,
            height: 20,
            borderRadius: 999,
          },
        ],
      },
    ],
  };
};

export const getFormLayout: (helperText?: boolean) => ICustomViewStyle = (helperText = false) => {
  return {
    gap: 4,
    children: [
      {
        width: '55%',
        height: 20,
      },
      {
        width: '100%',
        borderRadius: 8,
        height: 44,
      },
      helperText
        ? {
            width: '90%',
            height: 20,
          }
        : {},
    ],
  };
};

export const getTextAreaLayout: ThemeCustomViewStyle = () => {
  return {
    gap: 4,
    children: [
      {
        flexDirection: 'row',
        justifyContent: 'space-between',
        children: [
          {width: '55%', height: 20},
          {
            width: 40,
            height: 20,
          },
        ],
      },
      {
        width: '100%',
        borderRadius: 8,
        height: 88,
      },
    ],
  };
};

export const getFormSectionLayout: ThemeCustomViewStyleWithChildren = (theme, children) => {
  const {ColorGlobal, SizeAlias, SizeGlobal} = theme;
  return {
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeAlias.Radius3,

    children: [
      {
        padding: SizeGlobal.Size400,
        flexDirection: 'row',
        children: [
          {
            height: 24,
            width: '50%',
          },
        ],
      },
      getHorizontalDividerLayout(theme),
      {
        padding: SizeGlobal.Size400,
        gap: SizeGlobal.Size400,
        children: children,
      },
    ],
  };
};
