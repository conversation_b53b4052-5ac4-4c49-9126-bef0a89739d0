import React, {useMemo} from 'react';
import {StyleProp, TextStyle} from 'react-native';

import {propsToStyle} from '@utils';

import {translate} from '@locales';
import {MSBTextBase} from 'msb-shared-component';
import {TextProps} from './type';

export const Text = ({
  t18n,
  text,
  flex,
  color,
  center,
  children,
  textAlign,
  fontStyle,
  lineHeight,
  t18nOptions,
  textTransform,
  letterSpacing,
  margin,
  marginBottom,
  marginLeft,
  marginRight,
  marginTop,
  style: styleOverride = {},
  ...rest
}: TextProps) => {
  // state
  const i18nText = useMemo(() => t18n && translate(t18n, t18nOptions), [t18n, t18nOptions]);

  const content = useMemo(() => i18nText?.toString() ?? text ?? children, [i18nText, text, children]);

  const styleComponent = useMemo<StyleProp<TextStyle>>(
    () => [
      [
        center && {textAlign: 'center'},
        propsToStyle([
          {flex},
          {margin},
          {marginLeft},
          {marginRight},
          {marginTop},
          {marginBottom},
          {color},
          {textAlign},
          {textTransform},
          {fontStyle},
          {letterSpacing},
          {lineHeight},
        ]),
      ],
    ],
    [
      flex,
      center,
      color,
      textAlign,
      textTransform,
      fontStyle,
      letterSpacing,
      lineHeight,
      margin,
      marginBottom,
      marginLeft,
      marginRight,
      marginTop,
    ],
  );

  return (
    <MSBTextBase allowFontScaling={false} {...rest} style={[styleComponent, styleOverride]}>
      {content}
    </MSBTextBase>
  );
};
