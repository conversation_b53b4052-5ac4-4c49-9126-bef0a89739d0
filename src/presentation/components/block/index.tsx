import {propsToStyle} from '@utils';
import {useMSBStyles} from 'msb-shared-component';
import React, {useMemo} from 'react';
import {StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import Animated from 'react-native-reanimated';
import {sharedStyleSheet} from '../common/styles';
import {BlockProps} from './type';

const styles = StyleSheet.create({
  block: {
    flex: 1,
  },
});

export const Block = ({
  animated,
  gap,
  left,
  flex,
  top,
  block,
  right,
  width,
  height,
  border,
  middle,
  bottom,
  zIndex,
  margin,
  shadow,
  opacity,
  padding,
  children,
  maxWidth,
  overflow,
  position,
  flexWrap,
  minWidth,
  alignSelf,
  maxHeight,
  minHeight,
  marginTop,
  marginLeft,
  alignItems,
  paddingTop,
  marginRight,
  borderStyle,
  paddingLeft,
  borderColor,
  borderWidth,
  borderRadius,
  paddingRight,
  marginBottom,
  paddingBottom,
  borderTopColor,
  justifyContent,
  borderTopWidth,
  paddingVertical,
  borderLeftWidth,
  borderLeftColor,
  borderRightColor,
  borderRightWidth,
  paddingHorizontal,
  borderBottomColor,
  borderBottomWidth,
  borderTopLeftRadius,
  borderTopRightRadius,
  color: backgroundColor,
  borderBottomLeftRadius,
  borderBottomRightRadius,
  direction: flexDirection,
  shadowConfig = {},
  style = {},
  ...rest
}: BlockProps) => {
  const {
    styles: sharedStyles,
    theme: {ColorAlias},
  } = useMSBStyles(sharedStyleSheet);

  const styleComponent = useMemo<StyleProp<ViewStyle>>(
    () => [
      block === true && styles.block,
      border === true && {
        borderWidth: 1,
        borderColor: ColorAlias.BorderDefault,
      },
      middle && {alignItems: 'center'},
      shadow && sharedStyles.shadow,
      propsToStyle([
        {margin},
        {gap},
        {marginLeft},
        {marginRight},
        {marginTop},
        {marginBottom},
        {flexDirection},
        {padding},
        {paddingRight},
        {paddingBottom},
        {paddingLeft},
        {paddingTop},
        {paddingHorizontal},
        {paddingVertical},
        {width},
        {height},
        {maxHeight},
        {maxWidth},
        {minHeight},
        {minWidth},
        {borderWidth},
        {borderColor},
        {backgroundColor},
        {justifyContent},
        {alignItems},
        {alignSelf},
        {borderRadius},
        {flex},
        {position},
        {flexWrap},
        {left},
        {right},
        {bottom},
        {top},
        {zIndex},
        {overflow},
        {borderBottomColor},
        {borderBottomLeftRadius},
        {borderBottomRightRadius},
        {borderLeftColor},
        {borderRightColor},
        {borderStyle},
        {borderTopColor},
        {borderTopLeftRadius},
        {borderTopRightRadius},
        {opacity},
        {borderBottomWidth},
        {borderLeftWidth},
        {borderRightWidth},
        {borderTopWidth},
      ]),
      style,
    ],
    [
      block,
      border,
      middle,
      shadow,
      shadowConfig,
      margin,
      marginLeft,
      marginRight,
      marginTop,
      marginBottom,
      flexDirection,
      padding,
      paddingRight,
      paddingBottom,
      paddingLeft,
      paddingTop,
      paddingHorizontal,
      paddingVertical,
      width,
      height,
      maxHeight,
      maxWidth,
      minHeight,
      minWidth,
      borderWidth,
      borderColor,
      backgroundColor,
      justifyContent,
      alignItems,
      alignSelf,
      borderRadius,
      flex,
      position,
      flexWrap,
      left,
      right,
      bottom,
      top,
      zIndex,
      overflow,
      borderBottomColor,
      borderBottomLeftRadius,
      borderBottomRightRadius,
      borderLeftColor,
      borderRightColor,
      borderStyle,
      borderTopColor,
      borderTopLeftRadius,
      borderTopRightRadius,
      opacity,
      borderBottomWidth,
      borderLeftWidth,
      borderRightWidth,
      borderTopWidth,
      style,
    ],
  );

  const Wrapper = useMemo(() => (animated ? Animated.View : View), [animated]);

  // render
  return (
    <Wrapper style={styleComponent} {...rest}>
      {children}
    </Wrapper>
  );
};
