import {translate} from '@locales';
import {MSBTouchable} from 'msb-shared-component';
import React, {useState} from 'react';
import {version} from '../../../../package.json';
import {Block} from '../block';
import {Text} from '../text';

const ModuleVersion = () => {
  const [visible, setVisible] = useState(false);

  if (process.env.APP_ENV && ['sit', 'dev', 'local'].includes(process.env.APP_ENV)) {
    return (
      <Block position="absolute" bottom={24} right={16} opacity={visible ? 1 : 0}>
        <MSBTouchable onPress={() => setVisible(prev => !prev)}>
          <Text>{translate('common.app_version', {version})}</Text>
        </MSBTouchable>
      </Block>
    );
  }

  return null;
};

export default ModuleVersion;
