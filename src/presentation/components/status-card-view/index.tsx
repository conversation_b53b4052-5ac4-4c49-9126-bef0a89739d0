import {CardDomainStatus, STATUS_GROUPS} from '@entities/card/CardDomainStatus.ts';
import {translate} from '@locales';
import {ThemeMainType, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {Text} from '../text/index.tsx';
import {styleSheet} from './styles.tsx';
import {CardStatusConfig, StatusCardViewProps} from './type';

const StatusCardView: React.FC<StatusCardViewProps> = ({status}) => {
  const {styles, theme} = useMSBStyles(styleSheet);

  const cardStatusConfig = getCardStatusConfig(status, theme);
  return cardStatusConfig ? (
    <View style={[styles.statusContainer, {backgroundColor: cardStatusConfig.backgroundColor}]}>
      <Text testID="cm.status-card-view.title" style={[styles.statusTitle, {color: cardStatusConfig.textColor}]}>
        {cardStatusConfig.title}
      </Text>
    </View>
  ) : null;
};

const getCardStatusConfig = (status: CardDomainStatus, {ColorTag}: ThemeMainType): CardStatusConfig | null => {
  if (status === CardDomainStatus.Active) {
    return {
      statusId: status,
      title: translate('status_card.active'),
      textColor: ColorTag.TextGreen,
      backgroundColor: ColorTag.SurfaceGreen,
    };
  }

  if (status === CardDomainStatus.Expired) {
    return {
      statusId: status,
      title: translate('status_card.expired'),
      textColor: ColorTag.TextBlack,
      backgroundColor: ColorTag.SurfaceBlack,
    };
  }

  if (status === CardDomainStatus.WaitingClose) {
    return {
      statusId: status,
      title: translate('status_card.waiting_close'),
      textColor: ColorTag.TextBlack,
      backgroundColor: ColorTag.SurfaceBlack,
    };
  }

  if (STATUS_GROUPS.INACTIVE.includes(status)) {
    return {
      statusId: status,
      title: translate('status_card.inactive'),
      textColor: ColorTag.TextBlack,
      backgroundColor: ColorTag.SurfaceBlack,
    };
  }

  if (STATUS_GROUPS.TEMP_BLOCK.includes(status)) {
    return {
      statusId: status,
      title: translate('status_card.block_temp'),
      textColor: ColorTag.TextBlack,
      backgroundColor: ColorTag.SurfaceBlack,
    };
  }

  if (STATUS_GROUPS.BLOCKED.includes(status)) {
    return {
      statusId: status,
      title: translate('status_card.close'),
      textColor: ColorTag.TextBlack,
      backgroundColor: ColorTag.SurfaceBlack,
    };
  }
  return null;
};
export default StatusCardView;
