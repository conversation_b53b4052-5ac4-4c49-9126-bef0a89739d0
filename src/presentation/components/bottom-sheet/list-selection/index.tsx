import {
  EmptyType,
  MSBEmptyState,
  MSBFastImage,
  MSBFolderImage,
  MSBSearchInput,
  MSBTextBase,
  MSBTouchable,
  TypographyType,
  useMSBStyles,
} from 'msb-shared-component';
import React, {useCallback, useMemo, useState} from 'react';
import {View} from 'react-native';

import {Block} from '@components/block';
import {Text} from '@components/text';
import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {Branch} from '@domain/entities/common/Branch';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import {translate} from '@locales';
import {convertViToEn} from '@utils/StringFormat';
import {styleSheet} from './styles';

// Hàm highlight
function highlightText({
  fullText,
  searchTerm,
  color,
  tpg,
}: {
  fullText: string;
  searchTerm: string;
  color: string;
  tpg?: TypographyType;
}) {
  if (!searchTerm.trim()) {
    return <Text>{fullText}</Text>;
  }

  const normalizedFullText = convertViToEn(fullText);
  const normalizedSearchTerm = convertViToEn(searchTerm.trim());

  const matchIndex = normalizedFullText.indexOf(normalizedSearchTerm);
  if (matchIndex === -1) {
    return <Text>{fullText}</Text>;
  }

  const beforeMatch = fullText.slice(0, matchIndex);
  const matchedText = fullText.slice(matchIndex, matchIndex + searchTerm.length);
  const afterMatch = fullText.slice(matchIndex + searchTerm.length);

  return (
    <MSBTextBase type={tpg?.base_medium}>
      {beforeMatch}
      <MSBTextBase type={tpg?.base_medium} style={{color: color}}>
        {matchedText}
      </MSBTextBase>
      {afterMatch}
    </MSBTextBase>
  );
}

export const ListSelectionWithBottomSheet = ({
  data,
  selectedItem,
  onSelectedItem,
  isNormalMode = true,
  placeholder,
  isCityMode = false,
}: {
  data: SelectionItem[];
  selectedItem?: SelectionItem;
  onSelectedItem: (value: SelectionItem) => void;
  isNormalMode?: boolean;
  placeholder?: string;
  isCityMode?: boolean;
}) => {
  const [textSearch, setTextSearch] = useState('');
  const {styles, theme} = useMSBStyles(styleSheet);
  const {SizeGlobal, ColorAlias, ColorGlobal, Typography} = theme;

  const handleSearchData = useCallback(
    (text: string) => {
      const normalizedText = convertViToEn(text.toLowerCase());
      return data.filter((item: SelectionItem) =>
        convertViToEn(item.name?.toLowerCase() || '').includes(normalizedText),
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data, convertViToEn],
  );
  const searchResult = useMemo(() => handleSearchData(textSearch), [textSearch, handleSearchData]);

  const renderItem = useCallback(
    (result: {item: SelectionItem; index: number}) => {
      const {id, name} = result.item;
      const isSelected = selectedItem?.id === id;
      return (
        <MSBTouchable
          testID={`cm-list-selection-item-${id}`}
          onPress={() => {
            onSelectedItem(result.item);
          }}>
          {isNormalMode ? (
            <View style={styles.normalContainer}>
              <MSBTextBase style={Typography?.base_medium}>
                {highlightText({fullText: name, searchTerm: textSearch, color: ColorAlias.TextBrand})}
              </MSBTextBase>
              {isSelected && (
                <MSBFastImage folder={MSBFolderImage.ICON_SVG} nameImage={'check'} style={styles.iconCheck} />
              )}
            </View>
          ) : (
            <View style={styles.branchContainer}>
              <View style={styles.container}>
                <MSBTextBase type={Typography?.base_medium}>
                  {highlightText({
                    fullText: result.item.name,
                    searchTerm: textSearch,
                    color: ColorAlias.TextBrand,
                    tpg: Typography,
                  })}
                </MSBTextBase>
                <View style={{height: SizeGlobal.Size100}} />
                <Text type={theme.Typography?.small_regular} color={ColorGlobal.Neutral600}>
                  {(result.item as Branch).hubAddress}
                </Text>
              </View>
            </View>
          )}
        </MSBTouchable>
      );
    },
    [selectedItem?.id, textSearch, onSelectedItem],
  );

  return (
    <BottomSheetFlatList
      data={textSearch !== '' ? searchResult : data}
      keyExtractor={(item: any) => (item.code ?? item.id).toString()}
      renderItem={renderItem}
      contentContainerStyle={styles.contentContainerStyle}
      showsVerticalScrollIndicator={true}
      bounces={false}
      overScrollMode="never"
      ListHeaderComponent={
        placeholder !== null && placeholder !== '' ? (
          <View style={styles.containerHeaderSearch}>
            <Block flex={1}>
              <MSBSearchInput
                value={textSearch}
                onChangeText={val => {
                  setTextSearch(val);
                }}
                isInputBottomSheet
                placeholder={placeholder}
              />
            </Block>
            {isCityMode && (
              <View style={styles.containerIcon}>
                <MSBFastImage folder={MSBFolderImage.ICON_SVG} nameImage="map-pin" style={styles.iconMapPin} />
              </View>
            )}
          </View>
        ) : null
      }
      ListEmptyComponent={
        <MSBEmptyState
          type={EmptyType.Search}
          emptyTitle={translate('additionalInfo.empty_result')}
          emptySubTitle={translate('additionalInfo.sub_empty_result')}
        />
      }
    />
  );
};
