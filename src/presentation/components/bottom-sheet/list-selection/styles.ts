import {screenDimensions} from '@constants/sizes';
import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeGlobal, SizeAlias}) => ({
  branchContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: SizeAlias.SpacingSmall,
    paddingVertical: SizeAlias.SpacingXSmall,
  },
  container: {
    flexGrow: 0,
    flexDirection: 'column',
  },
  containerHeaderSearch: {
    alignItems: 'center',
    flexDirection: 'row',
    maxWidth: screenDimensions.width,
    paddingHorizontal: SizeAlias.SpacingSmall,
    paddingVertical: SizeAlias.SpacingXSmall,
  },
  containerIcon: {
    alignItems: 'center',
    height: SizeGlobal.Size1000,
    justifyContent: 'center',
    marginLeft: SizeAlias.SpacingXSmall,
    width: SizeGlobal.Size1000,
  },
  contentContainerStyle: {
    paddingBottom: SizeGlobal.Size600,
  },
  normalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: SizeAlias.SpacingSmall,
    paddingVertical: SizeAlias.SpacingXSmall,
  },
  iconCheck: {
    width: SizeGlobal.Size800,
    height: SizeGlobal.Size800,
  },
  iconMapPin: {
    width: SizeGlobal.Size600,
    height: SizeGlobal.Size600,
  },
}));
