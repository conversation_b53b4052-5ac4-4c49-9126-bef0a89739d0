import {Text} from '@components/text';
import {BottomSheetView} from '@gorhom/bottom-sheet';
import {translate} from '@locales';
import {hostSharedModule} from 'msb-host-shared-module';
import {MSBIcon, MSBIcons, MSBIconSize, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';

const DontMissDealBottomSheet = () => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);

  return (
    <BottomSheetView>
      <View testID="cm.bottom-sheet.dont-miss-deal" style={styles.content}>
        <View style={styles.itemContainer}>
          <MSBIcon icon={MSBIcons.IconShieldCheck} iconSize={MSBIconSize.SIZE_24} />
          <Text
            style={styles.flex1}
            text={translate('select_card_to_open.benefit_1')}
            type={Typography?.base_regular}
          />
        </View>
        <View style={styles.itemContainer}>
          <MSBIcon icon={MSBIcons.IconGift} iconSize={MSBIconSize.SIZE_24} />
          <Text
            style={styles.flex1}
            text={translate('select_card_to_open.benefit_2')}
            type={Typography?.base_regular}
          />
        </View>
        <View style={styles.itemContainer}>
          <MSBIcon icon={MSBIcons.IconCard} iconSize={MSBIconSize.SIZE_24} />
          <Text
            style={styles.flex1}
            text={translate('select_card_to_open.benefit_3')}
            type={Typography?.base_regular}
          />
        </View>
      </View>
    </BottomSheetView>
  );
};

export const showCardOpenDontMissDealBottomSheet = (onGoBack: () => void) => {
  return hostSharedModule.d.domainService.showBottomSheet({
    header: translate('select_card_to_open.dont_miss_deal_title'),
    children: <DontMissDealBottomSheet />,
    onConfirm: hostSharedModule.d.domainService.hideBottomSheet,
    confirmBtnText: translate('select_card_to_open.button_continue'),
    onCancel: onGoBack,
    cancelBtnText: translate('select_card_to_open.button_exit'),
  });
};
