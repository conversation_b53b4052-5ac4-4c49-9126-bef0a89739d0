import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeGlobal}) => ({
  flex1: {
    flex: 1,
  },
  content: {
    paddingHorizontal: SizeGlobal.Size400,
    paddingVertical: SizeGlobal.Size800,
    rowGap: SizeGlobal.Size500,
  },
  buttons: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: SizeGlobal.Size400,
    paddingHorizontal: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size300,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: SizeGlobal.Size300,
  },
}));
