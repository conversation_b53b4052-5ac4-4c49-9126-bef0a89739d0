import Table from '@components/table';
import {CellPropsItemType} from '@components/table/types';
import {screenDimensions} from '@constants/sizes';
import {BottomSheetView} from '@gorhom/bottom-sheet';
import {translate} from '@locales';
import {useMSBStyles} from 'msb-shared-component';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const TableViewBottomSheet = () => {
  const {
    theme: {SizeGlobal},
  } = useMSBStyles();
  const {bottom} = useSafeAreaInsets();

  return (
    <BottomSheetView testID="cm.bottom-sheet.table" style={[{paddingBottom: bottom + SizeGlobal.Size400}]}>
      <Table
        tableHead={['', translate('select_card_to_open.type_digital'), translate('select_card_to_open.type_physical')]}
        flexArr={[
          (screenDimensions.width - 2 * 110) / screenDimensions.width,
          110 / screenDimensions.width,
          110 / screenDimensions.width,
        ]}
        tableData={[
          [
            {type: CellPropsItemType.LABEL, label: translate('select_card_to_open.usable_after_registration')},
            {type: CellPropsItemType.CHECK},
            {type: CellPropsItemType.CHECK},
          ],
          [
            {type: CellPropsItemType.LABEL, label: translate('select_card_to_open.online_payment')},
            {type: CellPropsItemType.CHECK},
            {type: CellPropsItemType.CHECK},
          ],
          [
            {type: CellPropsItemType.LABEL, label: translate('select_card_to_open.mobile_wallets')},
            {type: CellPropsItemType.CHECK},
            {type: CellPropsItemType.CHECK},
          ],
          [
            {type: CellPropsItemType.LABEL, label: translate('select_card_to_open.swipe_payment')},
            {type: CellPropsItemType.LABEL, label: ''},
            {type: CellPropsItemType.CHECK},
          ],
          [
            {type: CellPropsItemType.LABEL, label: translate('select_card_to_open.atm_withdraw_other_banks')},
            {type: CellPropsItemType.LABEL, label: ''},
            {type: CellPropsItemType.CHECK},
          ],
        ]}
      />
    </BottomSheetView>
  );
};

export default TableViewBottomSheet;
