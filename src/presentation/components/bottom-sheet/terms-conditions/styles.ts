import {screenDimensions} from '@constants/sizes';
import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, SizeAlias, ColorGlobal, SizeGlobal}) => {
  return {
    container: {
      flex: 1,
    },
    topView: {
      backgroundColor: ColorAlias.BackgroundPrimary,
      borderColor: ColorGlobal.Neutral100,
      borderRadius: SizeAlias.Radius4,
      height: screenDimensions.height * 0.8,
    },
    iconDown: {
      width: SizeGlobal.Size500,
      height: SizeGlobal.Size600,
    },
    shadowView: {
      backgroundColor: ColorAlias.BorderDefault,
      height: 1,
      shadowColor: ColorGlobal.Neutral300,
      shadowOffset: {
        width: 2,
        height: -2,
      },
      shadowOpacity: 0.8,
    },
  };
});
