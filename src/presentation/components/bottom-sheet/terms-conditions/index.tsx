import {BottomSheetView} from '@gorhom/bottom-sheet';
import {translate} from '@locales';
import {sharedStyleSheet} from '@presentation/components/common/styles';
import {hostSharedModule} from 'msb-host-shared-module';
import {useMSBStyles} from 'msb-shared-component';
import React, {useRef} from 'react';
import Pdf from 'react-native-pdf';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {styleSheet} from './styles';

const TermsConditionsBottomSheet = () => {
  const {styles, theme} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const insets = useSafeAreaInsets();

  const pdfRef = useRef<any>(null);

  return (
    <BottomSheetView
      style={[styles.topView, sharedStyles.shadow, {paddingBottom: insets.bottom + theme.SizeGlobal.Size400}]}
      pointerEvents="box-none">
      <Pdf
        ref={pdfRef}
        source={{
          uri: 'https://www.aeee.in/wp-content/uploads/2020/08/Sample-pdf.pdf',
          cacheFileName: 'true',
        }}
        style={styles.container}
        enableDoubleTapZoom={false}
        enableAnnotationRendering={false}
        enableAntialiasing={false}
        onError={error => console.log('PDF Error:', error)}
        trustAllCerts={false}
      />
    </BottomSheetView>
  );
};

export const showTermsConditionsBottomSheet = () => {
  return hostSharedModule.d.domainService.showBottomSheet({
    header: translate('card_open_note_card_usage.terms_and_conditions'),
    children: <TermsConditionsBottomSheet />,
  });
};
