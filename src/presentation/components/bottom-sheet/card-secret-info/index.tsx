import {Text} from '@components/text';
import {DISPLAY_CARD_DATE_FORMAT} from '@constants';
import {Card} from '@entities/card/Card.ts';
import {CardSecretInfo} from '@entities/card/CardSecretInfo.ts';
import {CardType} from '@entities/card/CardType.ts';
import {BottomSheetScrollView} from '@gorhom/bottom-sheet';
import {translate} from '@locales';
import Clipboard from '@react-native-clipboard/clipboard';
import {showToastMessage} from '@utils';
import {transformExpDate} from '@utils/Parser.ts';
import moment from 'moment';
import {hostSharedModule, ToastType} from 'msb-host-shared-module';
import {MSBFastImage, MSBFolderImage, MSBIcon, useMSBStyles} from 'msb-shared-component';
import React, {useMemo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {styleSheet} from './styles.tsx';
import {CardInfoProps} from './type';

const CardInfo: React.FC<CardInfoProps> = ({cardInfo, cardType}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {bottom} = useSafeAreaInsets();

  const card_info_data = useMemo(() => {
    return [
      {
        title: translate('card.bottom_sheet.cardholder_name'),
        value: cardInfo.clientName,
      },
      {
        title: translate(
          cardType === CardType.Debit
            ? 'card.bottom_sheet.linked_account_number'
            : 'card.bottom_sheet.debit_account_number',
        ),
        value: cardInfo.rbsNumber,
      },
      {
        title: translate('card.bottom_sheet.card_name'),
        value: cardInfo.productName,
      },
      {
        title: translate('card.bottom_sheet.card_number'),
        value: cardInfo.cardNumber,
        can_copy: true,
      },
      {
        title: translate('card.bottom_sheet.issue_date'),
        value: moment(cardInfo.cardDateOpen).format(DISPLAY_CARD_DATE_FORMAT),
      },
      {
        title: translate('card.bottom_sheet.expiry_date'),
        value: transformExpDate(cardInfo.expDate),
      },
      {
        title: translate('card.bottom_sheet.cvv_code'),
        value: cardInfo.cvv,
      },
    ];
  }, [cardInfo]);

  return (
    <BottomSheetScrollView
      contentContainerStyle={styles.container}
      enableFooterMarginAdjustment
      overScrollMode={'never'}
      showsVerticalScrollIndicator={false}>
      <View style={styles.warningContainer}>
        <MSBIcon icon="warning-circle-orange" iconSize={24} />
        <Text style={styles.warningTitle}>{translate('card_detail.card_info.warning')}</Text>
      </View>
      {card_info_data?.map((val, index) => {
        return (
          <View key={`bs-card-info-${val.value}-${index}`} style={styles.subContainer}>
            <Text style={styles.title}>{val.title}</Text>
            <Text style={styles.value}>{val.value}</Text>
            {val.can_copy && (
              <TouchableOpacity
                style={styles.copy}
                onPress={() => {
                  Clipboard.setString(val.value);
                  showToastMessage(translate('card_detail.card_info.copy_card_number_success'), ToastType.SUCCESS);
                }}>
                <MSBFastImage style={styles.warningImage} nameImage="copy-orange" folder={MSBFolderImage.ICON_SVG} />
              </TouchableOpacity>
            )}
          </View>
        );
      })}
      <View style={{height: bottom}} />
    </BottomSheetScrollView>
  );
};

export default CardInfo;

export const showCardSecretInfoBottomSheet = (secretInfo: CardSecretInfo, card: Card) => {
  return hostSharedModule.d.domainService.showBottomSheet({
    header: translate('card.bottom_sheet.card_info'),
    children: <CardInfo cardInfo={secretInfo} cardType={card.type} />,
  });
};
