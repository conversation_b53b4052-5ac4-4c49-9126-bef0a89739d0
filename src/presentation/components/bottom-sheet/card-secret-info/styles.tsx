import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorDataView, ColorInform, SizeInform, Typography}) => ({
  container: {
    flex: 1,
    padding: 16,
    gap: 16,
  },
  warningImage: {
    width: 24,
    height: 24,
  },
  warningTitle: {
    flex: 1,
    ...Typography?.small_regular,
    color: ColorInform.TextMessage,
  },
  warningContainer: {
    paddingHorizontal: SizeInform.SpacingLeft,
    paddingVertical: SizeInform.SpacingTop,
    flexDirection: 'row',
    borderWidth: SizeInform.BorderStroke,
    borderRadius: SizeInform.BorderRadius,
    borderColor: ColorInform.BorderWarning,
    backgroundColor: ColorInform.SurfaceWarning,
    gap: 8,
  },
  subContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    ...Typography?.small_regular,
    color: ColorDataView.TextSub,
  },
  value: {
    flex: 1,
    marginLeft: 16,
    textAlign: 'right',
    ...Typography?.small_medium,
    color: ColorDataView.TextMain,
  },
  copy: {
    width: 24,
    height: 24,
    marginLeft: 8,
  },
}));
