import {FeaturesViewHost} from '@components/feature-view';
import {CardFeature} from '@entities/card/CardFeature';
import {translate} from '@locales';
import {hostSharedModule} from 'msb-host-shared-module';
import React from 'react';

const formatData = (features: CardFeature[], numColumns: number) => {
  const remainder = features.length % numColumns;
  if (remainder !== 0) {
    const emptySlots = numColumns - remainder;
    for (let i = 0; i < emptySlots; i++) {
      features.push({id: `empty-${i}`, isEmpty: true, priority: 0});
    }
  }
  return features;
};

export const showCardFeaturesBottomSheet = (
  features: CardFeature[],
  onCardFunction?: (feature: CardFeature) => void,
) => {
  const data = formatData(features, 3);

  hostSharedModule.d.domainService.showBottomSheet({
    header: translate('detail_card.service'),
    children: <FeaturesViewHost features={data} onCardFunction={onCardFunction} />,
  });
};
