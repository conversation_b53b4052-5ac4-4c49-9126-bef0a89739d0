// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`renders TableViewBottomSheet correctly 1`] = `
<View
  style={
    [
      false,
      false,
      undefined,
      undefined,
      [],
      {},
    ]
  }
>
  <View
    style={
      [
        false,
        false,
        undefined,
        undefined,
        [
          {
            "flexDirection": "row",
          },
          {
            "backgroundColor": "rgba(247, 248, 249, 1)",
          },
          {
            "borderBottomColor": "rgb(222,229,239)",
          },
          {
            "borderTopColor": "rgb(222,229,239)",
          },
          {
            "borderBottomWidth": 1,
          },
          {
            "borderTopWidth": 1,
          },
        ],
        {},
      ]
    }
  >
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.7066666666666667,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Bold",
              "fontSize": 14,
              "letterSpacing": -0.28,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      />
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Bold",
              "fontSize": 14,
              "letterSpacing": -0.28,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      >
        i18n:select_card_to_open.type_digital
      </Text>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Bold",
              "fontSize": 14,
              "letterSpacing": -0.28,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      >
        i18n:select_card_to_open.type_physical
      </Text>
    </View>
  </View>
  <View
    style={
      [
        false,
        false,
        undefined,
        undefined,
        [
          {
            "flexDirection": "row",
          },
          {
            "backgroundColor": "rgb(255,255,255)",
          },
          {
            "alignItems": "center",
          },
          {
            "borderBottomColor": "rgb(222,229,239)",
          },
          {
            "borderBottomWidth": 1,
          },
        ],
        {},
      ]
    }
  >
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.7066666666666667,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Regular",
              "fontSize": 14,
              "letterSpacing": -0.42,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      >
        i18n:select_card_to_open.usable_after_registration
      </Text>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
  </View>
  <View
    style={
      [
        false,
        false,
        undefined,
        undefined,
        [
          {
            "flexDirection": "row",
          },
          {
            "backgroundColor": "rgb(255,255,255)",
          },
          {
            "alignItems": "center",
          },
          {
            "borderBottomColor": "rgb(222,229,239)",
          },
          {
            "borderBottomWidth": 1,
          },
        ],
        {},
      ]
    }
  >
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.7066666666666667,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Regular",
              "fontSize": 14,
              "letterSpacing": -0.42,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      >
        i18n:select_card_to_open.online_payment
      </Text>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
  </View>
  <View
    style={
      [
        false,
        false,
        undefined,
        undefined,
        [
          {
            "flexDirection": "row",
          },
          {
            "backgroundColor": "rgb(255,255,255)",
          },
          {
            "alignItems": "center",
          },
          {
            "borderBottomColor": "rgb(222,229,239)",
          },
          {
            "borderBottomWidth": 1,
          },
        ],
        {},
      ]
    }
  >
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.7066666666666667,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Regular",
              "fontSize": 14,
              "letterSpacing": -0.42,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      >
        i18n:select_card_to_open.mobile_wallets
      </Text>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
  </View>
  <View
    style={
      [
        false,
        false,
        undefined,
        undefined,
        [
          {
            "flexDirection": "row",
          },
          {
            "backgroundColor": "rgb(255,255,255)",
          },
          {
            "alignItems": "center",
          },
          {
            "borderBottomColor": "rgb(222,229,239)",
          },
          {
            "borderBottomWidth": 1,
          },
        ],
        {},
      ]
    }
  >
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.7066666666666667,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Regular",
              "fontSize": 14,
              "letterSpacing": -0.42,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      >
        i18n:select_card_to_open.swipe_payment
      </Text>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Regular",
              "fontSize": 14,
              "letterSpacing": -0.42,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      />
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
  </View>
  <View
    style={
      [
        false,
        false,
        undefined,
        undefined,
        [
          {
            "flexDirection": "row",
          },
          {
            "backgroundColor": "rgb(255,255,255)",
          },
          {
            "alignItems": "center",
          },
          {
            "borderBottomColor": "rgb(222,229,239)",
          },
          {
            "borderBottomWidth": 1,
          },
        ],
        {},
      ]
    }
  >
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.7066666666666667,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Regular",
              "fontSize": 14,
              "letterSpacing": -0.42,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      >
        i18n:select_card_to_open.atm_withdraw_other_banks
      </Text>
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <Text
        allowFontScaling={false}
        style={
          [
            {
              "color": "rgb(9,30,66)",
              "fontFamily": "Inter-Regular",
              "fontSize": 14,
              "letterSpacing": -0.42,
              "lineHeight": 20,
            },
            {
              "color": "rgb(9,30,66)",
            },
            [
              [
                [
                  undefined,
                  [],
                ],
              ],
              {},
            ],
          ]
        }
      />
    </View>
    <View
      style={
        [
          false,
          false,
          undefined,
          undefined,
          [
            {
              "paddingHorizontal": 12,
            },
            {
              "paddingVertical": 12,
            },
            {
              "flex": 0.*****************,
            },
          ],
          {},
        ]
      }
    >
      <View>
        <View
          style={
            [
              {
                "overflow": "hidden",
              },
              [
                {
                  "height": 24,
                  "width": 24,
                },
                undefined,
              ],
            ]
          }
        >
          <FastImageView
            defaultSource={null}
            onFastImageError={[Function]}
            resizeMode="contain"
            source={
              {
                "uri": "https://digibank-dev.msb.com.vn/asset-resource/mobile/themes/default/icon-svgs/1-0-0/tone-check.png",
              }
            }
            style={
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              }
            }
            testID="undefined-img"
          />
        </View>
      </View>
    </View>
  </View>
</View>
`;
