import FastImage, {FastImageProps} from '@d11/react-native-fast-image';
import React from 'react';
import {Image, View} from 'react-native';
import Animated, {useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {styles} from './styles';

const AnimatedImage = Animated.createAnimatedComponent(Image);

const MSBFastImage: React.FC<FastImageProps> = ({style, defaultSource, ...props}) => {
  const visible = useSharedValue(1);

  const defaultAnimStyle = useAnimatedStyle(() => {
    return {
      opacity: visible.value,
    };
  });

  return (
    <View style={style}>
      <AnimatedImage resizeMode={props.resizeMode} style={[styles.default, defaultAnimStyle]} source={defaultSource} />
      <FastImage
        style={[styles.cardImage]}
        {...props}
        onLoadStart={() => (visible.value = 1)}
        onLoadEnd={() => (visible.value = withTiming(0))}
      />
    </View>
  );
};

export default MSBFastImage;
