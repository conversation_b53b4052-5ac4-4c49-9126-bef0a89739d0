import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorCard, ColorGlobal}) => ({
  container: {
    height: 0.5,
    backgroundColor: ColorCard.BorderDivider,
    width: '100%',
  },
  horizontalDividerContainer: {
    height: 1,
    backgroundColor: ColorCard.BorderDivider,
  },
  dividerContainer: {
    backgroundColor: ColorGlobal.NeutralWhite,
    position: 'absolute',
    left: 0,
    right: 0,
    top: -0.5,
    height: 2,
    justifyContent: 'flex-end',
  },
  divider: {backgroundColor: ColorCard.BorderDivider, height: 1},
}));
