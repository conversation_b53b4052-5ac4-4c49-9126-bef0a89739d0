import React from 'react';
import {Block} from '../block';
import {BlockProps} from '../block/type';
import {useMSBStyles} from 'msb-shared-component';
import {styleSheet} from './styles';

const HorizontalDivider: React.FC<BlockProps> = props => {
  const {styles} = useMSBStyles(styleSheet);
  return <Block style={[styles.horizontalDividerContainer]} {...props} />;
};

export default HorizontalDivider;
