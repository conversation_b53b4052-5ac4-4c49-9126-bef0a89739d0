import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {HorizontalLineViewProps} from './type';

const HorizontalLineView: React.FC<HorizontalLineViewProps> = ({style = {marginHorizontal: 16}}) => {
  const {styles} = useMSBStyles(styleSheet);

  return <View style={[styles.container, style]} />;
};

export default HorizontalLineView;
