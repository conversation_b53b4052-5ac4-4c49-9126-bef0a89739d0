import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {ShadowHorizontalLineViewProps} from './type';

const ShadowHorizontalLineView: React.FC<ShadowHorizontalLineViewProps> = ({padding = 0}) => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <View style={[styles.dividerContainer, {paddingHorizontal: padding}]}>
      <View style={styles.divider} />
    </View>
  );
};

export default ShadowHorizontalLineView;
