import {cardAspectRatio} from '@constants/sizes';
import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorDataView, SizeGlobal, Typography}) => ({
  generalContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 12,
  },
  imageInfo: {
    width: 113,
    aspectRatio: cardAspectRatio,
    overflow: 'hidden',
  },
  headerInfoContainer: {
    flex: 1,
    width: '100%',
    rowGap: SizeGlobal.Size100,
  },
  headerInfoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeCardValue: {
    ...Typography?.caption_regular,
    color: ColorDataView.TextSub,
  },
  nameCardValue: {
    ...Typography?.base_semiBold,
    color: ColorDataView.TextMain,
  },
  rowCardInfoContainer: {
    width: '100%',
    flexDirection: 'row',
    alignContent: 'center',
  },
  numberCardValue: {
    ...Typography?.small_regular,
    color: ColorDataView.TextSub,
    marginStart: SizeGlobal.Size100,
  },
  ownerShipView: {marginEnd: SizeGlobal.Size100},
}));
