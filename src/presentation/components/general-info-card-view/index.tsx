import React from 'react';
import {StyleProp, StyleSheet, View, ViewStyle} from 'react-native';

import MSBFastImage from '@components/fast-image';
import {CardGeneralInfo} from '@entities/card/CardGeneralInfo.ts';
import {OwnerShip} from '@entities/card/OwnerShip.ts';
import images from '@images';
import {isNotNullOrUndefined} from '@utils/StringFormat.tsx';
import {MSBTextBase, useMSBStyles} from 'msb-shared-component';
import {Block} from '../block';
import {OwnerShipView} from '../owner-ship-view';
import StatusCardView from '../status-card-view';
import {styleSheet} from './styles.tsx';

const GeneralInfoCard = ({data, style}: {data: CardGeneralInfo; style?: StyleProp<ViewStyle>}) => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <View style={[styles.generalContainer, style]}>
      <Block style={styles.imageInfo}>
        <MSBFastImage
          style={StyleSheet.absoluteFillObject}
          source={{
            uri: data.imageUrl ?? '',
          }}
          defaultSource={images.card_default}
          resizeMode={'contain'}
        />
      </Block>
      <View style={styles.headerInfoContainer}>
        <View style={styles.headerInfoContent}>
          {data.ownership === OwnerShip.Sub && <OwnerShipView style={styles.ownerShipView} />}
          <MSBTextBase testID="cm.general-info-card.instrument" style={styles.typeCardValue}>
            {data.instrumentI18n}
          </MSBTextBase>
        </View>
        <MSBTextBase style={styles.nameCardValue} testID="cm.general-info-card.name">
          {data.name}
        </MSBTextBase>
        <View style={styles.rowCardInfoContainer}>
          {isNotNullOrUndefined(data.status) && <StatusCardView status={data.status} />}
          <MSBTextBase testID="cm.general-info-card.marked-number" style={[styles.numberCardValue]}>
            {data.markedNumber}
          </MSBTextBase>
        </View>
      </View>
    </View>
  );
};

export default GeneralInfoCard;
