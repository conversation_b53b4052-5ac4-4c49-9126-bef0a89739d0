import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorDataView, ColorItem, SizeGlobal, Typography}) => ({
  container: {flexDirection: 'row', columnGap: SizeGlobal.Size100},
  moneyTxt: {
    color: ColorItem.TextMain,
    ...Typography?.base_semiBold,
  },
  currencyTxt: {
    ...Typography?.base_regular,
    color: ColorDataView.TextCurrency,
  },
}));
