import {formatMoney} from '@utils/StringFormat.tsx';
import {useMSBStyles} from 'msb-shared-component';
import React, {useMemo} from 'react';
import {View, ViewStyle} from 'react-native';
import {Text} from '../text';
import {styleSheet} from './styles.tsx';

type MoneyViewType = 'base' | 'small';
type StyleWithType = {
  [type in MoneyViewType]: object | undefined;
};

const MoneyView = ({
  type = 'small',
  money,
  currency,
  moreStyles = {},
}: {
  type?: MoneyViewType;
  money: Nullish<number>;
  currency: Nullish<string>;
  moreStyles?: ViewStyle;
}) => {
  const {
    styles,
    theme: {ColorDataView, ColorItem, Typography},
  } = useMSBStyles(styleSheet);

  const typeMoney = useMemo(() => {
    const result: StyleWithType = {
      base: Typography?.base_semiBold,
      small: Typography?.small_semiBold,
    };
    return result[type];
  }, [type, Typography]);

  const typeCurrency = useMemo(() => {
    const result: StyleWithType = {
      base: Typography?.base_regular,
      small: Typography?.small_regular,
    };
    return result[type];
  }, [type, Typography]);

  return (
    <View style={[styles.container, moreStyles]}>
      <Text type={typeMoney} testID="cm.money-view.money" color={ColorItem.TextMain}>
        {formatMoney(money ?? 0) ?? ''}
      </Text>
      <Text type={typeCurrency} testID="cm.money-view.currency" color={ColorDataView.TextCurrency}>
        {currency ?? ''}
      </Text>
    </View>
  );
};

export default MoneyView;
