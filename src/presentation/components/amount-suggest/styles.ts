import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorCard, ColorButton, ColorField, Typography}) => {
  return {
    flatList: {
      width: '100%',
    },
    itemContainer: {
      alignItems: 'center',
      flex: 1,
      justifyContent: 'center',
    },
    keyboardAccessory: {
      backgroundColor: ColorAlias.SurfaceDisable,
      height: 40,
      width: '100%',
    },
    separator: {
      backgroundColor: ColorCard.BorderDivider,
      height: 25,
      marginTop: 9,
      width: 1,
    },
    txtDone: {
      ...Typography?.base_medium,
      color: ColorButton.TextTertiaryDefault,
      textAlign: 'center',
    },
    txtTitleItem: {
      ...Typography?.small_medium,
      color: ColorField.TextFilled,
      textAlign: 'center',
    },
  };
});
