import {MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo} from 'react';
import {FlatList, Keyboard, Text, View} from 'react-native';

import {screenDimensions} from '@constants/sizes';
import {KeyboardToolbar} from 'react-native-keyboard-controller';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {styleSheet} from './styles';
import {AmountSuggestItemProps, AmountSuggestProps} from './types';

const AmountSuggest = ({focused, amountSuggest, style, onPress}: AmountSuggestProps) => {
  const {styles} = useMSBStyles(styleSheet);
  const {bottom} = useSafeAreaInsets();

  const renderSeparator = useCallback(() => <View style={styles.separator} />, []);

  const renderContent = useMemo(() => {
    if (focused && amountSuggest.length > 0) {
      return (
        <View style={[styles.keyboardAccessory, style]}>
          <FlatList
            testID="cm.amount-suggest.list"
            keyboardShouldPersistTaps="always"
            data={amountSuggest}
            horizontal
            keyExtractor={amount => amount}
            renderItem={({item, index}) => (
              <Item item={item} index={index} length={amountSuggest.length} onPress={onPress} />
            )}
            showsHorizontalScrollIndicator={false}
            scrollEnabled={false}
            style={styles.flatList}
            ItemSeparatorComponent={renderSeparator}
          />
        </View>
      );
    }
  }, [focused, amountSuggest, onPress]);

  return (
    <KeyboardToolbar
      content={renderContent}
      showArrows={false}
      doneText={null}
      offset={{opened: bottom, closed: bottom + 40}}
    />
  );
};

const Item: React.FC<AmountSuggestItemProps> = ({item, index, length, onPress}) => {
  const {styles} = useMSBStyles(styleSheet);
  return (
    <MSBTouchable
      onPress={() => {
        Keyboard.dismiss();
        if (index !== length - 1) {
          onPress(item);
        }
      }}
      style={[styles.itemContainer, {width: screenDimensions.width / length}]}>
      <Text style={index === length - 1 ? styles.txtDone : styles.txtTitleItem} numberOfLines={2}>
        {item}
      </Text>
    </MSBTouchable>
  );
};

export default AmountSuggest;
