import {render, screen, userEvent} from '@presentation/__test__/test-utils';
import {generateAmountSuggestions} from '@utils/StringFormat';
import AmountSuggest from '..';

describe('Amount Suggest', () => {
  it('should render suggestions in the keyboard toolbar when amount input is focused', async () => {
    const mockFn = jest.fn();

    const listSuggestion = generateAmountSuggestions('123456');
    render(<AmountSuggest focused amountSuggest={listSuggestion} onPress={mockFn} />);

    const list = await screen.findByTestId('cm.amount-suggest.list');
    expect(list).toBeOnTheScreen();

    /**
     * '1,234,560',
     * '12,345,600',
     * '123,456,000',
     */
    expect(list.props.data).toBe(listSuggestion);
    const event = userEvent.setup();
    await event.press(screen.getByText('1,234,560'));
    expect(mockFn).toHaveBeenCalledTimes(1);
  });

  it('should not render suggestions when amount input is not focused', async () => {
    const mockFn = jest.fn();

    const listSuggestion = generateAmountSuggestions('123456');
    render(<AmountSuggest focused={false} amountSuggest={listSuggestion} onPress={mockFn} />);

    const toolbar = await screen.findByTestId('cm.keyboard.toolbar');
    expect(toolbar).toBeOnTheScreen();
    expect(toolbar.children).toEqual([]);
  });

  it('should not render suggestions when there are no suggestions for user', async () => {
    const mockFn = jest.fn();

    render(<AmountSuggest focused amountSuggest={[]} onPress={mockFn} />);

    const toolbar = await screen.findByTestId('cm.keyboard.toolbar');
    expect(toolbar).toBeOnTheScreen();
    expect(toolbar.children).toEqual([]);
  });
});
