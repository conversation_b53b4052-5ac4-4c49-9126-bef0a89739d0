import {fireEvent, render, RenderOptions, within} from '@testing-library/react-native';
import {UnistylesProvider} from 'react-native-unistyles';

import {screenDimensions} from '@constants/sizes';
import {CardType} from '@domain/entities/card/CardType';
import {screen} from '@presentation/__test__/test-utils';
import {ITEM_WIDTH} from '@presentation/screens/list-card/constants';
import {UserEventInstance} from '@testing-library/react-native/build/user-event/setup';

import MSBBottomSheet, {BOTTOM_SHEET_CONTAINER_TEST_ID} from 'mocks/bottom-sheet/MSBBottomSheet';
import MSBPopup, {MSB_POPUP_CANCEL_TEST_ID, MSB_POPUP_CONFIRM_TEST_ID} from 'mocks/popup/MSBPopup';
import {mockListCardResponse} from 'mocks/service-apis/get-list-cards';
import MSBToast from 'mocks/toast/MSBToast';
import {BottomSheetActions, PopupActions, ToastActions} from 'msb-host-shared-module';
import React, {useEffect, useRef} from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {ReactTestInstance} from 'react-test-renderer';

const AllTheProviders: React.FC<React.PropsWithChildren> = ({children}) => {
  const bottomSheetRef = useRef<BottomSheetActions>(null);
  const popupRef = useRef<PopupActions>(null);
  const toastRef = useRef<ToastActions>(null);

  useEffect(() => {
    global._MSB_TEST_BOTTOM_SHEET = bottomSheetRef;
    global._MSB_TEST_POPUP = popupRef;
    global._MSB_TEST_TOAST = toastRef;

    return () => {
      global._MSB_TEST_BOTTOM_SHEET = null;
      global._MSB_TEST_POPUP = null;
      global._MSB_TEST_TOAST = null;
    };
  }, [bottomSheetRef, popupRef, toastRef]);

  return (
    <UnistylesProvider>
      <SafeAreaProvider>{children}</SafeAreaProvider>
      <MSBBottomSheet ref={bottomSheetRef} />
      <MSBPopup ref={popupRef} />
      <MSBToast ref={toastRef} />
    </UnistylesProvider>
  );
};

export function customRender<T>(ui: React.ReactElement<T>, options?: RenderOptions) {
  return render(ui, {wrapper: AllTheProviders, ...options});
}

// re-export everything
export * from '@testing-library/react-native';

// override render method
export {customRender as render};

/**
 * Mockup utils
 */

export const mockScreenDimension = {fontScale: 1, height: 896, scale: 2, width: 414};

export async function shouldBeActiveTab(cardType: CardType) {
  const cardId = mockListCardResponse.find(el => el.type === cardType)?.id;
  const cardBtn = await screen.findByTestId(`cm.list-card.card-detail-btn.${cardId}`);
  expect(cardBtn).toBeOnTheScreen();
}

export async function asyncExpectElementOnScreenByTestId(testId: string): Promise<ReactTestInstance> {
  const setAutoDebitPaymentBtns = await screen.findAllByTestId(testId);

  const lastInstance = setAutoDebitPaymentBtns[setAutoDebitPaymentBtns.length - 1];
  expect(lastInstance).toBeOnTheScreen();
  return lastInstance;
}

export function expectElementOnScreenByTestId(testId: string): ReactTestInstance {
  const setAutoDebitPaymentBtns = screen.getAllByTestId(testId);
  const lastInstance = setAutoDebitPaymentBtns[setAutoDebitPaymentBtns.length - 1];
  expect(lastInstance).toBeOnTheScreen();
  return lastInstance;
}

export async function tapToCredit(userEvent: UserEventInstance): Promise<void> {
  const creditTabView = await screen.findByTestId('cm.tab-view.credits');
  await userEvent.press(creditTabView);
}

export async function tapToDebit(userEvent: UserEventInstance): Promise<void> {
  const debitTabView = await screen.findByTestId('cm.tab-view.debits');
  await userEvent.press(debitTabView);
}

export async function scrollToCardFromList(
  userEvent: UserEventInstance,
  cardId: string,
  cardType: CardType,
): Promise<ReactTestInstance> {
  const scrollView = await screen.findByTestId('cm.list-card.slider');
  const creditCards = mockListCardResponse.filter(el => el.type === CardType.Credit);
  const debitCards = mockListCardResponse.filter(el => el.type === CardType.Debit);

  const cardList = cardType === CardType.Credit ? creditCards : debitCards;
  const cardIndex = cardList.findIndex(el => el.id === cardId);

  await userEvent.scrollTo(scrollView, {
    x: cardIndex * screenDimensions.width,
    layoutMeasurement: {
      width: screenDimensions.width,
      height: 501,
    },
    contentSize: {height: 501, width: ITEM_WIDTH * cardList.length},
  });
  const cardBtn = await screen.findByTestId(`cm.list-card.card-detail-btn.${cardId}`);
  expect(cardBtn).toBeOnTheScreen();
  return cardBtn;
}

export async function shouldShowPopup(): Promise<ReactTestInstance> {
  const popup = await screen.findByTestId('msb.popup');
  expect(popup).toBeOnTheScreen();
  return popup;
}

export async function shouldShowToast(): Promise<ReactTestInstance> {
  const popup = await screen.findByTestId('msb.toast');
  expect(popup).toBeOnTheScreen();
  return popup;
}

export async function shouldShowBottomSheet(): Promise<ReactTestInstance> {
  const bottomSheet = await screen.findByTestId(BOTTOM_SHEET_CONTAINER_TEST_ID);
  expect(bottomSheet).toBeOnTheScreen();
  return bottomSheet;
}

export function getConfirmPopupButton(popup: ReactTestInstance): ReactTestInstance {
  const button = within(popup).getByTestId(MSB_POPUP_CONFIRM_TEST_ID);
  expect(button).toBeOnTheScreen();
  return button;
}

export function getCancelPopupButton(popup: ReactTestInstance): ReactTestInstance {
  const button = within(popup).getByTestId(MSB_POPUP_CANCEL_TEST_ID);
  expect(button).toBeOnTheScreen();
  return button;
}

export async function advanceTimersByTime(msToRun: number) {
  jest.advanceTimersByTime(msToRun);
  await flushPromises();
}

function flushPromises() {
  return new Promise(jest.requireActual('timers').setImmediate);
}

export function changeTextInputValue(input: ReactTestInstance, value: string) {
  fireEvent(input, 'focus');
  fireEvent(input, 'changeText', value);
  fireEvent(input, 'blur');
}
