import {createNativeStackNavigator} from '@react-navigation/native-stack';
import React from 'react';

import CardOpenConfirmInfoScreen from '@presentation/screens/card-open/confirm-info';
import CardOpenDetailCardScreen from '@presentation/screens/card-open/detail-card';
import CardOpenIssuanceContractScreen from '@presentation/screens/card-open/issuance-contract';
import CardOpenNoteCardUsageScreen from '@presentation/screens/card-open/note-card-usage';
import CardOpenNoticeDropOffScreen from '@presentation/screens/card-open/notice-dropoff';
import CardOpenRegistrationInformationScreen from '@presentation/screens/card-open/registration-information';
import CardOpenSelectCardScreen from '@presentation/screens/card-open/select-card-open';
import HistoryDetailScreen from '@presentation/screens/history-detail';
import ListHistoryScreen from '@presentation/screens/list-history';
import TransactionFilterScreen from '@presentation/screens/transaction-filter';
import AnnouncementResultsScreen from '../screens/announcement-results';
import AutomaticDebitPaymentScreen from '../screens/automatic-debit-payment';
import CreatePinScreen from '../screens/create-pin';
import DetailCardScreen from '../screens/detail-card';
import ListCardScreen from '../screens/list-card';
import ReInputPinScreen from '../screens/re-input-pin';
import UpdateDebitPaymentAccount from '../screens/update-debit-payment-account';
import {RootStackParamList} from './types';

const Stack = createNativeStackNavigator<RootStackParamList>();

const RootStack = () => {
  return (
    <Stack.Navigator initialRouteName="ListCardScreen" screenOptions={{headerShown: false}}>
      <Stack.Screen name="ListCardScreen" component={ListCardScreen} />
      <Stack.Screen name="DetailCardScreen" component={DetailCardScreen} />
      <Stack.Screen
        name="AnnouncementResultsScreen"
        component={AnnouncementResultsScreen}
        options={{gestureEnabled: false}}
      />
      <Stack.Screen name="CreatePinScreen" component={CreatePinScreen} />
      <Stack.Screen name="ReInputPinScreen" component={ReInputPinScreen} />
      <Stack.Screen name="AutomaticDebitPayment" component={AutomaticDebitPaymentScreen} />
      <Stack.Screen name="UpdateDebitPaymentAccount" component={UpdateDebitPaymentAccount} />
      <Stack.Screen name="CardOpenSelectCardScreen" component={CardOpenSelectCardScreen} />
      <Stack.Screen name="CardOpenIssuanceContractScreen" component={CardOpenIssuanceContractScreen} />
      <Stack.Screen name="CardOpenNoteCardUsageScreen" component={CardOpenNoteCardUsageScreen} />
      <Stack.Screen name="CardOpenConfirmInfoScreen" component={CardOpenConfirmInfoScreen} />
      <Stack.Screen name="CardOpenNoticeDropOffScreen" component={CardOpenNoticeDropOffScreen} />

      <Stack.Screen
        name="CardOpenDetailCardScreen"
        component={CardOpenDetailCardScreen}
        options={{headerShown: false}}
        initialParams={{flowId: ''}}
      />
      <Stack.Screen
        name="CardOpenRegistrationInformationScreen"
        component={CardOpenRegistrationInformationScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen name="TransactionFilterScreen" component={TransactionFilterScreen} options={{headerShown: false}} />
      <Stack.Screen name="ListHistoryScreen" component={ListHistoryScreen} options={{headerShown: false}} />
      <Stack.Screen name="HistoryDetailScreen" component={HistoryDetailScreen} options={{headerShown: false}} />
    </Stack.Navigator>
  );
};

export default RootStack;
