import {FormTransactionFilter} from '@domain/entities/form/FormTransactionFilter';
import {AnnouncementResultsParams} from '@entities/card/AnnouncementResults';
import {Card} from '@entities/card/Card';
import {CreatePinParams} from '@entities/card/CreatePinParams';
import {History} from '@entities/card/History';
import {ReInputPinParams} from '@entities/card/ReInputPinParams';
import {NativeStackScreenProps as RNStackScreenProps} from '@react-navigation/native-stack';

type CardOpenFlowParams = {
  flowId: string;
};

export type RootStackParamList = {
  ListCardScreen: undefined;
  DetailHistoryCardScreen: {item: History};
  DetailCardScreen: {id: string};
  AnnouncementResultsScreen: AnnouncementResultsParams;
  UpdateDebitPaymentAccount: {
    cardModel: Card;
  };
  AutomaticDebitPayment: {
    cardModel: Card;
  };
  CreatePinScreen: CreatePinParams;
  ReInputPinScreen: ReInputPinParams;
  CardOpenSelectCardScreen: CardOpenFlowParams;
  CardOpenIssuanceContractScreen: CardOpenFlowParams;
  CardOpenNoteCardUsageScreen: CardOpenFlowParams;
  CardOpenConfirmInfoScreen: CardOpenFlowParams;
  CardOpenNoticeDropOffScreen: CardOpenFlowParams;
  CardOpenDetailCardScreen: CardOpenFlowParams;
  CardOpenRegistrationInformationScreen: CardOpenFlowParams;
  TransactionFilterScreen: {
    cardId: string;
  };
  ListHistoryScreen: {
    cardId: string;
    filterOptions: FormTransactionFilter;
  };
  HistoryDetailScreen: {
    history: History;
  };
};

export type ApplicationScreenProps<T extends keyof RootStackParamList> = RNStackScreenProps<RootStackParamList, T>;
