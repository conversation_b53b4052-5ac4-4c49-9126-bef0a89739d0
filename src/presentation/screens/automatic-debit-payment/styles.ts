import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorCard, ColorGlobal, SizeAlias}) => ({
  headerContainer: {
    paddingTop: 2,
  },
  headerContentContainer: {
    marginBottom: 24,
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeAlias.Radius3,
    marginHorizontal: 2,
  },
  footerContainer: {paddingTop: 12, paddingHorizontal: 16},
  contentContainerStyle: {
    marginHorizontal: 14,
    marginTop: 22,
    paddingBottom: 24,
    overflow: 'hidden',
  },
  list: {flex: 1},
  innerList: {height: '100%'},
  headerContent: {
    color: ColorCard.TextSubtitle,
  },
  statementItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hideShadowEffect: {
    height: 2,
    position: 'absolute',
    top: -1,
    left: 0,
    right: 0,
    backgroundColor: ColorGlobal.NeutralWhite,
  },
  errorContainer: {
    borderBottomLeftRadius: SizeAlias.Radius4,
    borderBottomRightRadius: SizeAlias.Radius4,
    overflow: 'visible',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    marginHorizontal: 2,
  },
}));
