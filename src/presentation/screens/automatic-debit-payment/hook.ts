import {ApplicationScreenProps} from '@app-navigation/types';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {isLeft} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {CommonStateStatus} from '@entities/base/CommonState';
import {EAnnouncementResultsStatus, EAnnouncementResultsType} from '@entities/card/EAnnouncementResults';
import {PaymentLevelSelector} from '@entities/card/PaymentLevelSelector';
import {useRoute} from '@react-navigation/native';
import {waitForFinishingModalAnimation} from '@utils/index';
import useCardViewModel from '@view-models/useCardViewModel';
import {useLinkAccountViewModel} from '@view-models/useLinkAccountViewModel';
import {hostSharedModule} from 'msb-host-shared-module';
import {useCallback, useEffect, useState} from 'react';
import {NativeScrollEvent, NativeSyntheticEvent} from 'react-native';

export const useAutomaticDebitPaymentViewModel = () => {
  const {navigation, navigationToResultScreen, updateCardDetails} = useCardViewModel();
  const {
    params: {cardModel},
  } = useRoute<ApplicationScreenProps<'UpdateDebitPaymentAccount'>['route']>();

  const {loadingFirst, loadingMore, linkItems, hasError, internalCardDetail, getListLinkAccount} =
    useLinkAccountViewModel(cardModel);

  const [activeRbsNumber, setActiveRbsNumber] = useState(cardModel.rbsNumber);
  const [paymentLevel, setPaymentLevel] = useState<PaymentLevelSelector | undefined>(cardModel.paymentRate);

  useEffect(() => {
    if (!internalCardDetail) {
      return;
    }

    navigation.setParams({
      cardModel: internalCardDetail,
    });
    if (internalCardDetail?.rbsNumber !== cardModel.rbsNumber) {
      setActiveRbsNumber(internalCardDetail?.rbsNumber);
    }
    if (internalCardDetail?.paymentRate !== cardModel.paymentRate) {
      setPaymentLevel(internalCardDetail?.paymentRate);
    }
  }, [internalCardDetail]);

  useEffect(() => {
    getListLinkAccount(true);
  }, []);

  const onRefresh = useCallback(() => {
    getListLinkAccount(true);
  }, []);

  const onMomentumScrollEnd = useCallback(
    ({nativeEvent: {layoutMeasurement, contentOffset, contentSize}}: NativeSyntheticEvent<NativeScrollEvent>) => {
      const paddingToBottom = 36;
      const isCanLoadMore = layoutMeasurement?.height + contentOffset?.y >= contentSize?.height - paddingToBottom;

      if (!isCanLoadMore) {
        return;
      }

      getListLinkAccount();
    },
    [],
  );

  const autoDebitPayment = async () => {
    if (!paymentLevel) {
      return;
    }

    hostSharedModule.d.domainService.addSpinnerRequest();

    await waitForFinishingModalAnimation();

    const changeCardStatusUseCase = DIContainer.getInstance().getAutoDebitPaymentUseCase();
    const result = await changeCardStatusUseCase.execute({
      cardId: cardModel.id,
      accountId: activeRbsNumber,
      paymentRate: paymentLevel,
    });

    if (isLeft(result)) {
      hostSharedModule.d.domainService.addSpinnerCompleted();
      const error = result.error;

      if (error.code === MSBErrorCode.Unauthorized) {
        //Common handles show auth popup automatically
        return;
      }

      navigationToResultScreen(cardModel, EAnnouncementResultsStatus.fail, EAnnouncementResultsType.autoDebitPayment, {
        errorCode: error?.code,
      });
      return;
    }

    const data = result.data;
    if (data?.status === CommonStateStatus.SUCCESS && cardModel?.id) {
      await updateCardDetails(cardModel.id);

      navigationToResultScreen(
        cardModel,
        EAnnouncementResultsStatus.success,
        EAnnouncementResultsType.autoDebitPayment,
      );
    }
    hostSharedModule.d.domainService.addSpinnerCompleted();
  };

  return {
    cardModel,
    activeRbsNumber,
    linkItems,
    loadingFirst,
    loadingMore,
    paymentLevel,
    hasError,
    setPaymentLevel,
    setActiveRbsNumber,
    autoDebitPayment,
    onMomentumScrollEnd,
    onRefresh,
  };
};
