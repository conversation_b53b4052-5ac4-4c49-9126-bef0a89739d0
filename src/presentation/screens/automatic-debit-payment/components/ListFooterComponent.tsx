import {sharedStyleSheet} from '@components/common/styles';
import {LoadLinkAccountSkeleton} from '@components/loading-skeleton/UpdateDebitPaymentAccountSkeleton';
import {translate} from '@locales';
import {EmptyType, MSBEmptyState, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from '../styles';
import {ListFooterComponentProps} from './type';

const ListFooterComponent: React.FC<ListFooterComponentProps> = ({hasError, isEmpty, loadingMore}) => {
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const {styles} = useMSBStyles(styleSheet);

  if (loadingMore) {
    return <LoadLinkAccountSkeleton isLoading={true} />;
  }

  if (hasError) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.auto-debit-payment.empty-state"
          type={EmptyType.System}
          emptyTitle={translate('error_title')}
          emptySubTitle={translate('error_desc')}
        />
        <View style={styles.hideShadowEffect} />
      </View>
    );
  }

  if (isEmpty) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.auto-debit-payment.empty-state"
          type={EmptyType.Editable}
          emptyTitle={translate('automatic_debit_payment.no_data')}
          emptySubTitle={translate('automatic_debit_payment.no_data_desc')}
        />
        <View style={styles.hideShadowEffect} />
      </View>
    );
  }

  return null;
};

export default ListFooterComponent;
