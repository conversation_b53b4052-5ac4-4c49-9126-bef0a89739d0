import {PaymentLevelSelector} from '@entities/card/PaymentLevelSelector';

export interface StatementSelector {
  selected: boolean;
  selecting: boolean;
  onPress: (level: PaymentLevelSelector) => void;
}

export interface StatementBalanceOptionProps extends StatementSelector {
  level: PaymentLevelSelector;
}

export interface ListHeaderComponentProps {
  selected?: PaymentLevelSelector;
  onPress: (level: PaymentLevelSelector) => void;
  currentPaymentLevel?: PaymentLevelSelector;
}

export interface ListFooterComponentProps {
  hasError: boolean;
  isEmpty: boolean;
  loadingMore: boolean;
}
