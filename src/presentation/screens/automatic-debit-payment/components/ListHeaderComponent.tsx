import {Block} from '@components/block';
import {sharedStyleSheet} from '@components/common/styles';
import HorizontalDivider from '@components/line-view/Divider';
import {HeaderSection} from '@components/link-account/HeaderSection';
import {Text} from '@components/text';
import {CardFeatureType} from '@entities/card/CardFeatureType';
import {PaymentLevelSelector} from '@entities/card/PaymentLevelSelector';
import {translate} from '@locales';
import {MSBTag, MSBTextBase, MSBTouchableBox, TagType, TouchableBoxType, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {styleSheet} from '../styles';
import {ListHeaderComponentProps, StatementBalanceOptionProps} from './type';

const StatementBalanceOption: React.FC<StatementBalanceOptionProps> = ({selecting, selected, level, onPress}) => {
  const {
    styles,
    theme: {Typography, SizeGlobal},
  } = useMSBStyles(styleSheet);

  const title = useMemo(() => {
    switch (level) {
      case PaymentLevelSelector.Fullpayment:
        return translate('automatic_debit_payment.full_statement_balance');
      case PaymentLevelSelector.Minpayment:
        return translate('automatic_debit_payment.minimum_statement_balance');

      default:
        return '';
    }
  }, [level]);

  const onTap = useCallback(() => {
    onPress(level);
  }, [level, onPress]);

  return (
    <TouchableOpacity testID={`cm.select-payment-level.${level}`} activeOpacity={0.9} onPress={onTap}>
      <Block style={styles.statementItem} paddingRight={SizeGlobal.Size400} minHeight={64} gap={10}>
        <Block
          flex={1}
          paddingVertical={SizeGlobal.Size300}
          paddingHorizontal={SizeGlobal.Size400}
          gap={4}
          justifyContent="center">
          <Text type={Typography?.base_medium}>{title}</Text>
          {selecting && <MSBTag content={translate('automatic_debit_payment.selecting')} color={TagType.normal} />}
        </Block>
        <Block width={24} height={24}>
          <MSBTouchableBox type={TouchableBoxType.Radio} status={selected} onPress={onTap} />
        </Block>
      </Block>
    </TouchableOpacity>
  );
};

const ListHeaderComponent: React.FC<ListHeaderComponentProps> = ({currentPaymentLevel, selected, onPress}) => {
  const {
    styles,
    theme: {SizeAlias, Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const renderPayment = useCallback(() => {
    const levels = Object.values(PaymentLevelSelector);
    return levels.map((level, index) => {
      return (
        <React.Fragment key={`${level}-${index}`}>
          <Block>
            <StatementBalanceOption
              level={level}
              selected={selected === level}
              onPress={onPress}
              selecting={currentPaymentLevel === level}
            />
          </Block>
          {index !== levels.length - 1 && <HorizontalDivider marginLeft={16} marginRight={16} />}
        </React.Fragment>
      );
    });
  }, [selected, onPress]);

  return (
    <View>
      <View style={[sharedStyles.shadow, styles.headerContentContainer]}>
        <View style={{paddingHorizontal: SizeAlias.SpacingSmall, paddingVertical: SizeAlias.Spacing2xSmall}}>
          <MSBTextBase type={Typography?.base_semiBold}>
            {translate('automatic_debit_payment.select_payment_level')}
          </MSBTextBase>
          <MSBTextBase type={Typography?.small_regular} style={styles.headerContent}>
            {translate('automatic_debit_payment.select_auto_payment_level_on_statement_due')}
          </MSBTextBase>
        </View>
        <HorizontalDivider gap={0} />
        {renderPayment()}
      </View>
      <HeaderSection featureType={CardFeatureType.AutoDebit} />
    </View>
  );
};

export default ListHeaderComponent;
