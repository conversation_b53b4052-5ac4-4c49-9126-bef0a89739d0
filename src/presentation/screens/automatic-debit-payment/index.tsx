import {HeaderSection} from '@components/link-account/HeaderSection';
import {LinkAccountItemUI} from '@components/link-account/LinkAccountItemUI';
import {UpdateDebitPaymentAccountSkeleton} from '@components/loading-skeleton/UpdateDebitPaymentAccountSkeleton';
import {CardFeatureType} from '@entities/card/CardFeatureType';
import {LinkAccountItem, LinkAccountItemType} from '@entities/daily/ListLinkAccount';
import {translate} from '@locales';
import {isNil} from 'lodash';
import {MSBButton, MSBPage, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo} from 'react';
import {FlatList, RefreshControl, View} from 'react-native';
import ListFooterComponent from './components/ListFooterComponent';
import ListHeaderComponent from './components/ListHeaderComponent';
import {useAutomaticDebitPaymentViewModel} from './hook';
import {styleSheet} from './styles';

const AutomaticDebitPaymentScreen = () => {
  const {styles, theme} = useMSBStyles(styleSheet);
  const {
    cardModel,
    activeRbsNumber,
    linkItems,
    loadingFirst,
    loadingMore,
    paymentLevel,
    hasError,
    setPaymentLevel,
    setActiveRbsNumber,
    autoDebitPayment,
    onMomentumScrollEnd,
    onRefresh,
  } = useAutomaticDebitPaymentViewModel();

  const renderItem = useCallback(
    (data: {item: LinkAccountItem; index: number}) => {
      if (data.item.type === LinkAccountItemType.HEADER_SECTION) {
        /**
         * Header section
         */
        return <HeaderSection featureType={CardFeatureType.AutoDebit} />;
      }

      return (
        <LinkAccountItemUI
          featureType={CardFeatureType.AutoDebit}
          loadingMore={loadingMore}
          isLast={data.index === linkItems.length - 1}
          linking={data.item}
          activeRbsNumber={activeRbsNumber}
          onPress={setActiveRbsNumber}
        />
      );
    },
    [linkItems.length, activeRbsNumber, loadingMore],
  );

  const extraData = useMemo(() => {
    return {
      cardModel,
      activeRbsNumber,
      loadingMore,
      paymentLevel,
      hasError,
    };
  }, [cardModel, activeRbsNumber, loadingMore, paymentLevel, hasError]);

  const enabled = useMemo(() => {
    const hasValidPaymentLevel = !isNil(paymentLevel);
    const hasValidRbsNumber = !isNil(activeRbsNumber) && activeRbsNumber !== '';
    const isChanged = paymentLevel !== cardModel.paymentRate || activeRbsNumber !== cardModel.rbsNumber;
    const hasAccounts = linkItems.length > 0;
    return hasValidPaymentLevel && hasValidRbsNumber && isChanged && hasAccounts;
  }, [cardModel, activeRbsNumber, paymentLevel, linkItems.length]);

  const renderListHeaderComponent = useCallback(() => {
    return (
      <ListHeaderComponent
        currentPaymentLevel={cardModel.paymentRate}
        selected={paymentLevel}
        onPress={setPaymentLevel}
      />
    );
  }, [paymentLevel, setPaymentLevel, cardModel.paymentRate]);

  return (
    <MSBPage
      testID="cm.automaticDebitPaymentScreen"
      isScrollable={false}
      headerProps={{
        title: translate('automatic_debit_payment.header_title'),
        hasBack: true,
      }}>
      <UpdateDebitPaymentAccountSkeleton theme={theme} isLoading={loadingFirst} containerStyle={styles.list}>
        <FlatList
          extraData={extraData}
          showsVerticalScrollIndicator={false}
          ListHeaderComponentStyle={styles.headerContainer}
          ListHeaderComponent={renderListHeaderComponent}
          ListFooterComponent={
            <ListFooterComponent loadingMore={loadingMore} hasError={hasError} isEmpty={linkItems.length === 0} />
          }
          data={linkItems}
          keyExtractor={(_, index) => `link-account-${index}`}
          contentContainerStyle={[styles.contentContainerStyle]}
          style={styles.innerList}
          renderItem={renderItem}
          onMomentumScrollEnd={onMomentumScrollEnd}
          refreshControl={<RefreshControl refreshing={loadingFirst} onRefresh={onRefresh} />}
        />
      </UpdateDebitPaymentAccountSkeleton>
      {!loadingFirst && (
        <View style={[styles.footerContainer]}>
          <MSBButton
            testID="cm.auto-debit-payment.confirm"
            disabled={!enabled}
            label={translate('update_debit_payment_account.confirm')}
            onPress={autoDebitPayment}
          />
        </View>
      )}
    </MSBPage>
  );
};

export default AutomaticDebitPaymentScreen;
