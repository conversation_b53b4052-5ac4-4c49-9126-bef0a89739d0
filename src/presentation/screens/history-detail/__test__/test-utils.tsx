import {mapToHistory} from '@data/mappers/card/HistoryMapper';
import {HistoryDTO} from '@data/models/card/HistoryDTO';
import {screen, userEvent, within} from '@presentation/__test__/test-utils';
import {goToDetailScreen} from '@presentation/screens/detail-card/__test__/test-utils';
import {goToListHistory} from '@presentation/screens/list-history/__test__/test-utils';
import {formatMoney, parseCurrencyToNumber} from '@utils/StringFormat';
import {mockListFilterTransactionHistoriesResponse} from 'mocks/service-apis/get-list-filter-transaction';
import {mockListTransactionHistoriesResponse} from 'mocks/service-apis/get-list-transaction';

export const goToTransactionDetailFromListHistory = async () => {
  await goToListHistory();

  /**
   * mock 5 transaction histories
   * spilt to 2 pages
   * page 1: 3 transactions
   * page 2: 2 transactions
   */
  const lastTransaction = within(await screen.findByTestId('cm.transaction-history.list')).getByTestId(
    `cm.history-card.history-item.${mockListFilterTransactionHistoriesResponse.transactionHistories[2].rrn}`,
  );
  const userSetup = userEvent.setup();
  await userSetup.press(lastTransaction);
};

export const goToTransactionDetailFromDetailCard = async () => {
  const testCardId = '246244460';
  await goToDetailScreen({testCardId});

  /**
   * mock 5 transaction histories
   * spilt to 2 pages
   * page 1: 3 transactions
   * page 2: 2 transactions
   */
  const lastTransaction = within(await screen.findByTestId('cm.detail-card.list')).getByTestId(
    `cm.history-card.history-item.${mockListTransactionHistoriesResponse.transactionHistories[2].rrn}`,
  );
  const userSetup = userEvent.setup();
  await userSetup.press(lastTransaction);
};

export const expectTransactionListToBeVisible = async (transactionResponse: HistoryDTO) => {
  const transaction = mapToHistory(transactionResponse);
  const prefix = transaction?.drcr === '1' ? '+' : '-';
  const amount = await screen.findByText(prefix + formatMoney(parseCurrencyToNumber(transaction.transAmount ?? 0)));
  expect(amount).toBeOnTheScreen();
  expect(screen.getByText(transaction.mCardNo)).toBeOnTheScreen();
  expect(screen.getByText(transaction.transDetails)).toBeOnTheScreen();
  expect(screen.getByText(transaction.tranMsg)).toBeOnTheScreen();
};
