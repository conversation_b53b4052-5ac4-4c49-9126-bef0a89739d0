import {mockListFilterTransactionHistoriesResponse} from 'mocks/service-apis/get-list-filter-transaction';
import {mockListTransactionHistoriesResponse} from 'mocks/service-apis/get-list-transaction';
import {
  expectTransactionListToBeVisible,
  goToTransactionDetailFromDetailCard,
  goToTransactionDetailFromListHistory,
} from './test-utils';

describe('Transaction History Details Screen', () => {
  it('should show transaction details when user comes from list history', async () => {
    await goToTransactionDetailFromListHistory();
    await expectTransactionListToBeVisible(mockListFilterTransactionHistoriesResponse.transactionHistories[2]);
  });

  it('should show transaction details when user comes from detail card', async () => {
    await goToTransactionDetailFromDetailCard();
    await expectTransactionListToBeVisible(mockListTransactionHistoriesResponse.transactionHistories[2]);
  });
});
