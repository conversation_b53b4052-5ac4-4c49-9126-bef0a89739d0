import {translate} from '@locales';
import {ApplicationScreenProps} from '@presentation/navigation/types';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useMemo} from 'react';

const useHistoryDetail = () => {
  const navigation = useNavigation();
  const {
    params: {history},
  } = useRoute<ApplicationScreenProps<'HistoryDetailScreen'>['route']>();

  const onPressRightIcon = () => {
    navigation.goBack();
  };

  const infos = useMemo(
    () => [
      {
        key: 'infos-1',
        title: translate('history_detail.card_number'),
        value: history.mCardNo,
      },
      {
        key: 'infos-2',
        title: translate('history_detail.transaction_content'),
        value: history?.transDetails ?? '',
      },
      {
        key: 'infos-3',
        title: translate('history_detail.transaction_channel'),
        value: history.tranMsg,
      },
    ],
    [history],
  );

  return {
    history,
    onPressRightIcon,
    infos,
  };
};

export default useHistoryDetail;
