import {Text} from '@presentation/components/text';
import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';

interface Props {
  title: string;
  value: string;
}

const HistoryInfoItem: React.FC<Props> = ({title, value}) => {
  const {
    styles,
    theme: {Typography, ColorDataView},
  } = useMSBStyles(styleSheet);
  return (
    <View style={styles.container}>
      <Text type={Typography?.small_regular} color={ColorDataView.TextSub}>
        {title}
      </Text>
      <Text type={Typography?.base_medium} color={ColorDataView.TextMain}>
        {value}
      </Text>
    </View>
  );
};

export default HistoryInfoItem;
