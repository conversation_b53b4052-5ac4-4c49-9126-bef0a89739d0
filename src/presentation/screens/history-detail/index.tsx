import {DISPLAY_DATE_FORMAT, TIME_24H_FORMAT} from '@constants';
import {translate} from '@locales';
import RoundedBackgroundView from '@presentation/components/background-custom-view';
import {sharedStyleSheet} from '@presentation/components/common/styles';
import HorizontalDivider from '@presentation/components/line-view/Divider';
import {Text} from '@presentation/components/text';
import {convertDateTimeTo} from '@utils';
import {formatMoney, highlightText, parseCurrencyToNumber} from '@utils/StringFormat';
import {MSBBackgroundFastImage, MSBIcon, MSBIcons, MSBIconSize, MSBPage, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {StyleSheet, View} from 'react-native';
import HistoryInfoItem from './components/history-info-item';
import useHistoryDetail from './hooks/useHistoryDetail';
import {styleSheet} from './styles';

const HistoryDetailScreen = () => {
  const {history, onPressRightIcon, infos} = useHistoryDetail();
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const {styles, theme} = useMSBStyles(styleSheet);

  const renderInfos = useCallback(
    () => infos.map(({key, ...item}) => <HistoryInfoItem key={key} {...item} />),
    [infos],
  );

  const renderHeaderRightButtons = useCallback(
    () => [
      {
        icon: 'tone-delete-cancel-close',
        isBgIcon: true,
        onPress: onPressRightIcon,
      },
    ],
    [onPressRightIcon],
  );

  const renderMoney = useCallback(() => {
    const prefix = history?.drcr === '1' ? '+' : '-';
    const amount = formatMoney(parseCurrencyToNumber(history?.transAmount ?? 0));
    const currency = history?.currencyLabel ?? '';

    return highlightText(`${prefix}${amount} ${currency}`, 'VND', styles.currentLabel, {});
  }, [history, styles]);

  const renderDateTime = useCallback(() => {
    const date = history?.transDate ?? '';
    return (
      <Text type={theme.Typography?.small_regular} color={theme.ColorAlias.TextSecondary}>
        {convertDateTimeTo(date, TIME_24H_FORMAT)} {translate('history_detail.day')}{' '}
        {convertDateTimeTo(date, DISPLAY_DATE_FORMAT)}
      </Text>
    );
  }, [history, theme]);

  return (
    <MSBPage
      style={styles.container}
      backgroundProps={{nameImage: 'bg_final'}}
      isScrollable={false}
      headerProps={{
        isLogo: true,
        rightButtons: renderHeaderRightButtons(),
      }}>
      <View style={styles.subContainer}>
        <RoundedBackgroundView style={StyleSheet.flatten([styles.roundBg, sharedStyles.shadow])}>
          <MSBBackgroundFastImage nameImage="bg_decorative_motifs" resizeMode="center" style={styles.backgroundImage}>
            <View style={styles.statusWrap}>
              <MSBIcon
                iconSize={MSBIconSize.SIZE_32}
                icon={history?.drcr !== '1' ? MSBIcons.IconUpDiagonalRight : 'down-diagonal-right-green'}
              />
              <View style={styles.statusContent}>
                <Text style={styles.money}>{renderMoney()}</Text>
                {renderDateTime()}
              </View>
            </View>
            <HorizontalDivider />
            <View style={styles.infoWrap}>{renderInfos()}</View>
          </MSBBackgroundFastImage>
        </RoundedBackgroundView>
      </View>
    </MSBPage>
  );
};

export default HistoryDetailScreen;
