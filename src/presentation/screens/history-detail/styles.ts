import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({Typography, SizeGlobal, ColorAlias}) => ({
  container: {
    flex: 1,
    paddingHorizontal: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size400,
  },
  subContainer: {
    flexDirection: 'column',
    flex: 1,
    width: '100%',
  },
  roundBg: {
    marginHorizontal: SizeGlobal.Size400,
    borderWidth: 0,
  },
  backgroundImage: {
    padding: SizeGlobal.Size400,
    rowGap: SizeGlobal.Size500,
  },
  infoWrap: {
    gap: SizeGlobal.Size400,
  },
  statusWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SizeGlobal.Size200,
  },
  statusContent: {
    flexDirection: 'column',
  },
  money: {
    ...Typography?.h4_bold,
    color: ColorAlias.TextPrimary,
  },
  currentLabel: {
    ...Typography?.h4_medium,
    color: ColorAlias.TextSecondary,
  },
}));
