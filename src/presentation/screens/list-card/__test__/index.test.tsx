import CardModuleMain from '@/CardModuleMain';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {CardFeatureType} from '@domain/entities/card/CardFeatureType';
import {CardType} from '@domain/entities/card/CardType';
import {expect} from '@jest/globals';
import {I18nKeys, translate} from '@locales';
import {
  expectElementOnScreenByTestId,
  fireEvent,
  getConfirmPopupButton,
  render,
  screen,
  scrollToCardFromList,
  shouldBeActiveTab,
  shouldShowPopup,
  tapToDebit,
  userEvent,
  within,
} from '@presentation/__test__/test-utils';
import {NavigationContainer} from '@react-navigation/native';
import {UserEventInstance} from '@testing-library/react-native/build/user-event/setup';
import {mockResponseForChangeStatus, mockServerFailureForChangeCardStatus} from 'mocks/service-apis/change-card-status';
import {
  mockRDBForCheckTransactionStatus,
  mockResponseForCheckTransactionStatus,
} from 'mocks/service-apis/check-transaction-status';
import {mockResponseForCreateNewPin} from 'mocks/service-apis/create-new-pin';
import {getMaskedNumberFromId, mockResponseForGetCardDetail} from 'mocks/service-apis/get-card-details';
import {
  mockResponseForGetListCard,
  mockServerFailureForGetListCard,
  mocResponseOnlyDebitForGetListCard,
} from 'mocks/service-apis/get-list-cards';
import {mockResponseForGetSecretInfo, mockServerFailureForGetSecretInfo} from 'mocks/service-apis/get-secret-info';
import {mockTransactionSigningSuccess} from 'mocks/transaction-signing';
import React from 'react';

describe('List Card Screen', () => {
  it('displays list card screen as default page', async () => {
    mockResponseForGetListCard();
    render(
      <NavigationContainer>
        <CardModuleMain />
      </NavigationContainer>,
    );

    expect(screen.getByText(translate('slide_card.top_bar_title'))).toBeVisible();

    // check first card is visible
    await shouldBeActiveTab(CardType.Credit);

    const userSetup = userEvent.setup();

    const cardItem = await scrollToCardFromList(userSetup, '246245020', CardType.Credit);
    expect(within(cardItem).getByText('**** 0025')).toBeOnTheScreen();

    await tapToDebit(userSetup);

    // check debit tab is active
    await shouldBeActiveTab(CardType.Debit);
  });

  it('should have active button if card status is CallIssuer', async () => {
    mockResponseForGetListCard();
    render(
      <NavigationContainer>
        <CardModuleMain />
      </NavigationContainer>,
    );

    // check first card is visible
    await shouldBeActiveTab(CardType.Credit);

    const userSetup = userEvent.setup();

    await tapToDebit(userSetup);

    const testCardId = '246447870';
    const cardItem = await scrollToCardFromList(userSetup, testCardId, CardType.Debit);
    expect(within(cardItem).getByText('**** 8756')).toBeOnTheScreen();

    // CallIssuer = '64',
    const activeButton = screen.getByTestId(`cm.active-card-button.${testCardId}`);
    expect(activeButton).toBeOnTheScreen();

    const cardTouchable = screen.getByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
    await userEvent.press(cardTouchable);

    // User can not go to details if card blocked
    expect(cardTouchable).toBeOnTheScreen();
  });

  it('should have lock overlay if card status is BlockCardTempByCustomer', async () => {
    mockResponseForGetListCard();
    render(
      <NavigationContainer>
        <CardModuleMain />
      </NavigationContainer>,
    );

    // check first card is visible
    await shouldBeActiveTab(CardType.Credit);

    const userSetup = userEvent.setup();
    const testCardId = '246245000';
    await scrollToCardFromList(userSetup, testCardId, CardType.Debit);
    expect(screen.getByText('**** 0039')).toBeOnTheScreen();

    expect(screen.getByTestId(`cm.list-card.card-status.${testCardId}`)).toBeOnTheScreen();
  });

  it('set debit tab to active when list have only debit cards', async () => {
    mocResponseOnlyDebitForGetListCard();
    render(
      <NavigationContainer>
        <CardModuleMain />
      </NavigationContainer>,
    );

    // check debit tab is active
    await shouldBeActiveTab(CardType.Debit);
  });

  it('displays error view when get list card failure', async () => {
    mockServerFailureForGetListCard();
    render(
      <NavigationContainer>
        <CardModuleMain />
      </NavigationContainer>,
    );

    // check error view is visible
    const errorView = await screen.findByTestId('cm.error-view');
    expect(errorView).toBeOnTheScreen();
  });

  it('displays empty view when get list card failure', async () => {
    render(
      <NavigationContainer>
        <CardModuleMain />
      </NavigationContainer>,
    );

    // check empty view is visible
    const cardEmptyTitle = await screen.findByTestId('cm.list-card.empty-title');
    expect(cardEmptyTitle).toBeOnTheScreen();
    const cardEmptyDes = await screen.findByTestId('cm.list-card.empty-subtitle');
    expect(cardEmptyDes).toBeOnTheScreen();
  });

  describe('active card from list screen', () => {
    it('should display announcement successfully if active card successfully', async () => {
      mockResponseForGetListCard();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
      );

      // check first card is visible
      await shouldBeActiveTab(CardType.Credit);

      const userSetup = userEvent.setup();

      await tapToDebit(userSetup);

      expect(screen.getByText('**** 8756')).toBeOnTheScreen();
      const testCardId = '246447870';
      const cardTouchable = await scrollToCardFromList(userSetup, testCardId, CardType.Debit);
      // CallIssuer = '64',
      const activeButton = screen.getByTestId(`cm.active-card-button.${testCardId}`);
      expect(activeButton).toBeOnTheScreen();

      await userEvent.press(cardTouchable);

      // User can not go to details if card blocked
      expect(cardTouchable).toBeOnTheScreen();

      mockResponseForChangeStatus();
      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();
      mockResponseForGetCardDetail();

      await userEvent.press(activeButton);

      // Announcement successful screen
      expect(screen.getByText(translate('announcement_results.active_card_success_content'))).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));

      const goToDetailBtn = screen.getByTestId('active_result_detail_button');

      mockResponseForGetCardDetail();
      await userEvent.press(goToDetailBtn);

      expect(screen.getByText(getMaskedNumberFromId(testCardId))).toBeOnTheScreen();
    });

    it('should display announcement error if active card failed', async () => {
      mockResponseForGetListCard();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
      );

      const userSetup = userEvent.setup();
      await shouldBeActiveTab(CardType.Credit);
      await tapToDebit(userSetup);

      await shouldBeActiveTab(CardType.Debit);
      expect(screen.getByText('**** 8756')).toBeOnTheScreen();

      const testCardId = '246447870';
      scrollToCardFromList(userSetup, testCardId, CardType.Debit);
      // CallIssuer = '64',
      const activeButton = screen.getByTestId(`cm.active-card-button.${testCardId}`);
      expect(activeButton).toBeOnTheScreen();

      const cardTouchable = screen.getByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
      await userEvent.press(cardTouchable);

      // User can not go to details if card blocked
      expect(cardTouchable).toBeOnTheScreen();

      mockResponseForGetCardDetail();
      mockTransactionSigningSuccess();
      mockRDBForCheckTransactionStatus();

      await userEvent.press(activeButton);

      expect(screen.getByText(translate('announcement_results.active_card_fail'))).toBeOnTheScreen();
      expect(
        screen.getByText(translate(`errors.${MSBErrorCode['RDB.CA.0018']}.description` as I18nKeys)),
      ).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');

      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));

      expect(screen.getByText(MSBErrorCode['RDB.CA.0018'])).toBeTruthy();

      const closeBtn = screen.getByTestId('active_result_close_button');
      await userEvent.press(closeBtn);

      expect(closeBtn).not.toBeOnTheScreen();
    });
  });

  describe('get secret info', () => {
    const renderScreen = async (event: UserEventInstance) => {
      mockResponseForGetListCard();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
      );

      await shouldBeActiveTab(CardType.Credit);

      const testCardId = '246245020';
      await scrollToCardFromList(event, testCardId, CardType.Credit);

      const cardTouchable = screen.getByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
      const getSecretInfoBtn = within(cardTouchable).getByTestId(`cm.feature-view.${CardFeatureType.SecurityInfo}`);
      return getSecretInfoBtn;
    };
    it('should show bottom sheet with info if everything is correct', async () => {
      const event = userEvent.setup();
      const getSecretInfoBtn = await renderScreen(event);

      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();
      mockResponseForGetSecretInfo();

      await event.press(getSecretInfoBtn);

      // cardNumber
      expect(await screen.findByText('****************')).toBeOnTheScreen();
      // CVV
      expect(await screen.findByText('873')).toBeOnTheScreen();
    });

    it('should show popup with error if get failed', async () => {
      const event = userEvent.setup();
      const getSecretInfoBtn = await renderScreen(event);
      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();
      mockServerFailureForGetSecretInfo();

      await event.press(getSecretInfoBtn);

      const popup = await shouldShowPopup();
      expect(await within(popup).findByText('internal_server_error')).toBeOnTheScreen();
      expect(within(popup).getByText(translate('errors.internal_server_error.title' as I18nKeys))).toBeOnTheScreen();
      expect(
        within(popup).getByText(translate('errors.internal_server_error.description' as I18nKeys)),
      ).toBeOnTheScreen();
    });
  });

  describe('lock active card', () => {
    const renderScreen = async (event: UserEventInstance, testCardId: string) => {
      mockResponseForGetListCard();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
      );

      await shouldBeActiveTab(CardType.Credit);

      const cardTouchable = await scrollToCardFromList(event, testCardId, CardType.Credit);

      const getSecretInfoBtn = within(cardTouchable).getByTestId(`cm.feature-view.${CardFeatureType.ChangeStatus}`);
      await event.press(getSecretInfoBtn);
      const popup = await shouldShowPopup();
      expect(within(popup).getByText(translate('card.lock_card_title'))).toBeOnTheScreen();
      expect(within(popup).getByText(translate('card.lock_card_desc'))).toBeOnTheScreen();
      const confirmBtn = getConfirmPopupButton(popup);
      return confirmBtn;
    };
    it('should open card details if everything is correct', async () => {
      const testCardId = '246204480';
      const event = userEvent.setup();
      const confirmBtn = await renderScreen(event, testCardId);

      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();
      mockResponseForChangeStatus();
      mockResponseForGetCardDetail();

      await event.press(confirmBtn);

      // Announcement successful screen
      const successContent = await screen.findByText(translate('announcement_results.lock_card_success'));
      expect(successContent).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));

      const goToDetailBtn = screen.getByTestId('active_result_detail_button');
      await event.press(goToDetailBtn);

      const cardDetailList = await screen.findByTestId('cm.detail-card.list');
      expect(within(cardDetailList).getByTestId('cm.general-info-card.marked-number').children).toContain(
        getMaskedNumberFromId(testCardId),
      );

      const backIcon = expectElementOnScreenByTestId('action-back');
      await event.press(backIcon);
      /**
       * When user back from details screen
       * Go to list card screen
       */
      expect(cardDetailList).not.toBeOnTheScreen();
      expect(successContent).not.toBeOnTheScreen();
      expect(await screen.findByTestId('cm.list-card.list')).toBeOnTheScreen();
    });

    it('should show popup with error if get failed', async () => {
      const testCardId = '246204480';
      const event = userEvent.setup();
      const confirmBtn = await renderScreen(event, testCardId);

      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();
      mockServerFailureForChangeCardStatus();
      mockResponseForGetCardDetail();

      await event.press(confirmBtn);

      // Announcement successful screen
      const successTitle = await screen.findByText(translate('announcement_results.lock_card_fail'));
      expect(successTitle).toBeOnTheScreen();

      const successContent = screen.getByText(translate(`errors.internal_server_error.description` as I18nKeys));
      expect(successContent).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));
      expect(screen.getByText(translate('card.call_hotline'))).toBeOnTheScreen();
    });
  });

  describe('create new pin', () => {
    it('should create new pin successfully if everything is correct', async () => {
      mockResponseForGetListCard();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
      );

      const userSetup = userEvent.setup();
      await shouldBeActiveTab(CardType.Credit);
      await tapToDebit(userSetup);

      const testCardId = '246192040';
      const cardTouchable = await scrollToCardFromList(userSetup, testCardId, CardType.Credit);

      const createNewPinBtn = within(cardTouchable).getByTestId(`cm.feature-view.${CardFeatureType.NewPin}`);
      await userSetup.press(createNewPinBtn);

      const createPinTitle = await screen.findByText(translate('create_pin.create_new_pin'));
      expect(createPinTitle).toBeOnTheScreen();
      const otpInputInCreateScreen = screen.getByTestId('otp-input-hidden');
      expect(otpInputInCreateScreen).toBeOnTheScreen();
      fireEvent.changeText(otpInputInCreateScreen, '123456');
      const errorText = await screen.findByText(translate('create_pin.pin_error'));
      expect(errorText).toBeOnTheScreen();

      fireEvent.changeText(otpInputInCreateScreen, '123123');

      const reInputPinTitle = await screen.findByText(translate('re_input_pin.title'));
      expect(reInputPinTitle).toBeOnTheScreen();
      const otpInputInReInputScreen = screen.getByTestId('otp-input-hidden');
      expect(otpInputInReInputScreen).toBeOnTheScreen();

      // Giả lập 5 lần nhập sai liên tiếp
      const wrongPins = ['123122', '123124', '123125', '123126', '123127'];
      for (let i = 0; i < wrongPins.length; i++) {
        fireEvent.changeText(otpInputInReInputScreen, wrongPins[i]);
        const remainingAttempts = 4 - i;

        if (i !== wrongPins.length - 1) {
          expect(
            await screen.findByText(translate('re_input_pin.re_pin_error', {remainingAttempts})),
          ).toBeOnTheScreen();
        }
      }

      const warningPopup = await shouldShowPopup();
      const confirmButton = getConfirmPopupButton(warningPopup);
      await userSetup.press(confirmButton);

      expect(await screen.findByText(translate('create_pin.create_new_pin'))).toBeOnTheScreen();

      const dupOtpInputInCreateScreen = screen.getByTestId('otp-input-hidden');
      expect(dupOtpInputInCreateScreen).toBeOnTheScreen();
      fireEvent.changeText(otpInputInCreateScreen, '123123');
      expect(await screen.findByText(translate('re_input_pin.title'))).toBeOnTheScreen();

      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();
      mockResponseForCreateNewPin();

      const dupOtpInputInReInputScreen = screen.getByTestId('otp-input-hidden');
      expect(dupOtpInputInReInputScreen).toBeOnTheScreen();
      fireEvent.changeText(dupOtpInputInReInputScreen, '123123');

      // Announcement successful screen
      const successContent = await screen.findByText(translate('announcement_results.create_new_pin_success'));
      expect(successContent).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));
    });
  });

  // describe('open card', () => {
  //   it('should open card successfully if card has dropoff', async () => {
  //     mockResponseForGetListCard();
  //     render(
  //       <NavigationContainer>
  //         <CardModuleMain />
  //       </NavigationContainer>,
  //     );
  //     const userSetup = userEvent.setup();
  //     const openCard = await screen.getByTestId(`cm.listCardScreen-icon-button-0`);
  //     mockResponseForInitializeCardOpeningFlow();
  //     await userSetup.press(openCard);
  //     const successContent = await screen.findByText(translate('card_open_notice_drop_off.screen_title'));
  //     expect(successContent).toBeOnTheScreen();
  //   });
  //   it('should open card successfully if card has not dropoff', async () => {
  //     mockResponseForGetListCard();
  //     render(
  //       <NavigationContainer>
  //         <CardModuleMain />
  //       </NavigationContainer>,
  //     );
  //     const userSetup = userEvent.setup();
  //     const openCard = await screen.getByTestId(`cm.listCardScreen-icon-button-0`);
  //     mockResponseForInitializeCardOpeningFlowNotDropOff();
  //     await userSetup.press(openCard);
  //     const successContent = await screen.findByText(translate('select_card_to_open.title'));
  //     expect(successContent).toBeOnTheScreen();
  //   });
  //   it('should open card fail', async () => {
  //     mockResponseForGetListCard();
  //     render(
  //       <NavigationContainer>
  //         <CardModuleMain />
  //       </NavigationContainer>,
  //     );
  //     const userSetup = userEvent.setup();
  //     const openCard = await screen.getByTestId(`cm.listCardScreen-icon-button-0`);
  //     mockResponseForInitializeCardOpeningError();
  //     await userSetup.press(openCard);

  //     const popup = await shouldShowPopup();
  //     // expect(await within(popup).findByText('internal_server_error')).toBeOnTheScreen();
  //     // expect(within(popup).getByText(translate('errors.internal_server_error.title' as I18nKeys))).toBeOnTheScreen();
  //     expect(within(popup).getByText(translate('errors.viewSecretInfo.closeButton' as I18nKeys))).toBeOnTheScreen();
  //   });
  // });
});
