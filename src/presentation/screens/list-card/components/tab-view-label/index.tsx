import {TabViewRoute} from '@entities/card/ListCard';
import {MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {TabDescriptor} from 'react-native-tab-view';
import {styleSheet} from './styles';

const TabViewLabel: Exclude<TabDescriptor<TabViewRoute>['label'], undefined> = ({route, focused}) => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <MSBTextBase style={[styles.default, focused && styles.active, route.disabled && styles.disable]}>
      {route.title}
    </MSBTextBase>
  );
};

export default TabViewLabel;
