import {screenDimensions} from '@constants/sizes';
import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorTab, Typography}) => ({
  default: {
    ...Typography?.base_regular,
    color: ColorTab.TextDefault,
    width: screenDimensions.width / 2 - 32,
    textAlign: 'center',
  },
  active: {
    ...Typography?.base_semiBold,
    color: ColorTab.TextActive,
  },
  disable: {
    ...Typography?.base_medium,
    color: ColorTab.TextDisable,
  },
}));
