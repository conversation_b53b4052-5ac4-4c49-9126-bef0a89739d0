import {cardAspectRatio, getCardRoundedCorner} from '@constants/sizes';
import {createMSBStyleSheet} from 'msb-shared-component';
import {StyleSheet} from 'react-native';
import {ITEM_GAP, ITEM_WIDTH} from '../../constants';

export const styleSheet = createMSBStyleSheet(
  ({ColorAlias, ColorBottomSheet, ColorButton, ColorCard, ColorDataView, ColorGlobal, SizeAlias, Typography}) => ({
    title: {
      ...Typography?.base_semiBold,
      color: ColorDataView.TextMain,
      flex: 1,
    },
    rightIcon: {
      width: 24,
      height: 24,
      tintColor: ColorAlias.IconBrand,
    },

    cardContainer: {
      backgroundColor: ColorGlobal.NeutralWhite,
      borderRadius: SizeAlias.Radius3,
      width: '100%',
    },

    cardImage: {
      width: '100%',
      height: '100%',
    },

    titleContainer: {
      width: '100%',
    },
    statusContainer: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: 'center',
      alignItems: 'center',
    },
    statusTitle: {
      ...Typography?.caption_medium,
    },
    rowCardInfoContainer: {
      width: '100%',
      flexDirection: 'row',
      alignContent: 'center',
    },
    cardNumber: {
      ...Typography?.small_medium,
      marginStart: 4,
      color: ColorDataView.TextSub,
      flex: 1,
    },
    progressBarContainer: {
      width: '100%',
      height: 4,
      borderRadius: 1,
      marginTop: 16,
      marginBottom: 4,
      backgroundColor: ColorGlobal.Brand100,
      overflow: 'hidden',
    },
    consumerContainer: {
      marginTop: 8,
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    consumerTitle: {
      ...Typography?.caption_regular,
      marginTop: 4,
      color: ColorDataView.TextSub,
    },
    moneyText: {
      ...Typography?.small_semiBold,
      color: ColorDataView.TextMain,
    },
    currency: {
      ...Typography?.small_regular,
      color: ColorDataView.TextCurrency,
    },
    moneyContainer: {
      flexDirection: 'column',
      flex: 1,
      gap: 4,
    },
    rowFeaturesContainer: {
      flexDirection: 'row',
      width: '100%',
      justifyContent: 'space-between',
      marginTop: 16,
    },
    basicInfoContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      gap: 16,
    },
    inforContainer: {width: '100%', paddingHorizontal: 16},
    descActiveTxt: {
      ...Typography?.caption_regular,
      color: ColorDataView.TextSub,
      marginTop: 22,
    },
    activeButton: {marginTop: 16, width: '100%'},
    activeContainer: {
      flexDirection: 'column',
      width: '100%',
      paddingHorizontal: 16,
    },
    activeLineView: {marginTop: 16},
    iconRight: {width: 24, height: 24},
    stateContainer: {flexDirection: 'column', gap: 4},
    moneyView: {flexDirection: 'row', justifyContent: 'flex-end'},
    imageContainer: {
      borderRadius: 8,
      padding: 16,
      overflow: 'hidden',
      alignSelf: 'center',
    },
    imageContent: {
      width: '100%',
      aspectRatio: cardAspectRatio,
      overflow: 'hidden',
    },
    overlay: {
      height: '100%',
      backgroundColor: 'rgba(0,0,0,0.6)',
    },
    iconStatus: {
      width: 32,
      height: 32,
    },
    statusTxt: {
      ...Typography?.caption_medium,
      color: ColorAlias.IconInverse,
    },
    iconRightContainer: {
      width: 32,
      height: 32,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: ColorButton.BorderSecondaryDefault,
      justifyContent: 'center',
      alignItems: 'center',
    },
    ownerShipContainer: {
      borderWidth: 1,
      borderColor: ColorBottomSheet.BorderDragHandle,
      borderRadius: 50,
      paddingHorizontal: 8,
      paddingVertical: 2,
      alignSelf: 'flex-start',
    },
    ownerShipText: {
      ...Typography?.caption_medium,
      color: ColorCard.TextSubtitle,
    },
    statusOverlayContainer: {
      ...StyleSheet.absoluteFillObject,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: getCardRoundedCorner(ITEM_WIDTH - 2 * ITEM_GAP),
      overflow: 'hidden',
    },
    cardLimitText: {
      width: '100%',
      textAlign: 'right',
    },
  }),
);

export const debitBlockStyle = StyleSheet.create({
  container: {flexDirection: 'column', paddingHorizontal: 16},
  moneyView: {marginTop: 16},
});
