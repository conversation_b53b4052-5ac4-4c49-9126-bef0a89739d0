import {Block} from '@components/block';
import {sharedStyleSheet} from '@components/common/styles.ts';
import {FeaturesView} from '@components/feature-view';
import HorizontalLineView from '@components/line-view';
import MoneyView from '@components/money-view';
import {OwnerShipView} from '@components/owner-ship-view';
import ProgressBarCardView from '@components/progress-bar-card-view';
import {Text} from '@components/text';
import {Card} from '@entities/card/Card.ts';
import {isNeedActiveCard} from '@entities/card/CardDomainStatus.ts';
import {CardType} from '@entities/card/CardType.ts';
import {OwnerShip} from '@entities/card/OwnerShip.ts';
import {translate} from '@locales';
import {ButtonSize, ButtonType, MSBButton, MSBIcon, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import CardImage from '../card-image/index.tsx';
import {debitBlockStyle, styleSheet} from './styles.tsx';
import {CardBodyViewProps} from './type';

const CardBodyView: React.FC<CardBodyViewProps> = props => {
  const {styles} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  let firstThreeFeatures = props.item.features?.slice(0, 3) || [];
  let visibleButtonCardActive = isNeedActiveCard(props.item?.status);

  const renderActiveButton = () => {
    if (visibleButtonCardActive) {
      return (
        <Block style={styles.activeContainer} paddingBottom={16}>
          <HorizontalLineView style={styles.activeLineView} />
          <Text style={styles.descActiveTxt}>{translate('slide_card.content_active')}</Text>
          <MSBButton
            testID={`cm.active-card-button.${props.item.id}`}
            style={styles.activeButton}
            label={translate('slide_card.active_button')}
            buttonType={ButtonType.Primary}
            buttonSize={ButtonSize.Small}
            onPress={() => {
              props.handleActiveCardListener(props.item);
            }}
          />
        </Block>
      );
    }
    return (
      <Block height={102} marginTop={16}>
        <FeaturesView features={firstThreeFeatures} onPress={props.handleFunction} />
      </Block>
    );
  };

  return (
    <MSBTouchable
      testID={`cm.list-card.card-detail-btn.${props.item?.id}`}
      onPress={props.handleNavToDetail}
      disabled={visibleButtonCardActive}
      activeOpacity={1}
      accessible={false}>
      <View style={[styles.cardContainer, sharedStyles.shadow]}>
        <View style={styles.imageContainer}>
          <CardImage card={props.item} />
        </View>
        <View style={styles.basicInfoContainer}>
          <Block gap={4} block>
            <Text style={styles.title} testID="cm.list-card.card-name" numberOfLines={1}>
              {props.item?.name}
            </Text>
            <View style={styles.rowCardInfoContainer}>
              {props.item.ownership === OwnerShip.Sub && <OwnerShipView />}
              <Text testID="cm.list-card.masked-number" style={styles.cardNumber} ellipsizeMode={'tail'}>
                {props.item?.maskedNumber}
              </Text>
            </View>
          </Block>
          {!visibleButtonCardActive && (
            <View testID="cm.card-body.open-detail-btn" style={styles.iconRightContainer}>
              <MSBIcon icon="right-orange" iconSize={24} onIconClick={props.handleNavToDetail} />
            </View>
          )}
        </View>
        <InfoView item={props.item} />
        {renderActiveButton()}
      </View>
    </MSBTouchable>
  );
};

const InfoView = ({item}: {item: Card}) => {
  return item.type === CardType.Credit ? (
    <CreditInfoView item={item} />
  ) : (
    <DebitInfoView money={item.availableBalance} currency={item.currency} />
  );
};

const CreditInfoView = ({item}: {item: Card}) => {
  const {styles} = useMSBStyles(styleSheet);
  const progress = ((item.availableBalance ?? 0) / Math.max(item.creditLimit ?? 0, 1)) * 100;

  return (
    <View style={styles.inforContainer}>
      <ProgressBarCardView progress={progress} />

      <View style={styles.consumerContainer}>
        <View style={styles.moneyContainer}>
          <Text style={styles.consumerTitle} numberOfLines={1} ellipsizeMode={'tail'}>
            {translate('slide_card.available')}
          </Text>
          <MoneyView money={item?.availableBalance} currency={item?.currency} />
        </View>
        <View style={styles.moneyContainer}>
          <Text style={[styles.consumerTitle, styles.cardLimitText]}>{translate('slide_card.card_limit')}</Text>
          <MoneyView money={item?.creditLimit} currency={item?.currency} moreStyles={styles.moneyView} />
        </View>
      </View>
    </View>
  );
};

const DebitInfoView = ({money, currency}: {money?: number | null; currency?: string | null}) => {
  return (
    <View style={debitBlockStyle.container}>
      <MoneyView money={money} currency={currency} moreStyles={debitBlockStyle.moneyView} type="base" />
    </View>
  );
};

export default CardBodyView;
