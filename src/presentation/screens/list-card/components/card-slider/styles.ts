import {createMSBStyleSheet} from 'msb-shared-component';
import {ITEM_WIDTH, SCREEN_WIDTH} from '../../constants';

export const styleSheet = createMSBStyleSheet(({ColorCarousel, SizeGlobal}) => ({
  contentContainer: {
    paddingHorizontal: (SCREEN_WIDTH - ITEM_WIDTH) / 2,
    paddingTop: SizeGlobal.Size600,
    paddingBottom: 4,
  },
  carouselContainer: {flexDirection: 'column', width: '100%'},
  carousel: {},
  inactiveIndicatorConfig: {
    color: ColorCarousel.DotDefault,
    margin: 4,
    opacity: 0.5,
    height: 8,
    width: 8,
    borderRadius: 8,
  },
  activeIndicatorConfig: {
    color: ColorCarousel.DotSelect,
    margin: 4,
    opacity: 1,
    height: 8,
    width: 24,
    borderRadius: 8,
  },
  scrollableDotsContainer: {
    alignItems: 'center',
    borderRadius: 12,
    height: 24,
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
}));
