import {Block} from '@components/block';
import CustomCarouselDots from '@components/dot-carousel';
import {DecreasingDot} from '@components/dot-carousel/type';
import {Card} from '@entities/card/Card.ts';
import {isJest} from '@utils/PlatformChecker';
import {useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo, useRef} from 'react';
import {FlatList, View} from 'react-native';
import Animated, {useAnimatedScrollHandler, useSharedValue} from 'react-native-reanimated';
import {ITEM_GAP, ITEM_WIDTH} from '../../constants.ts';
import CardBodyView from '../slide-card-body';
import {styleSheet} from './styles.ts';
import {CardSliderProps} from './type';

const WrapperFlatList = isJest() ? FlatList : Animated.FlatList;

export const CardSlider: React.FC<CardSliderProps> = props => {
  const {
    styles,
    theme: {ColorCarousel, ColorGlobal},
  } = useMSBStyles(styleSheet);

  const progress = useSharedValue(0);
  const activeIndex = useSharedValue(0);
  const flatListRef = useRef<Animated.FlatList<any>>(null);
  const decreasingDots: DecreasingDot[] = useMemo(
    () => [
      {
        config: {
          color: ColorCarousel.DotDefault,
          margin: 4,
          opacity: 0.5,
          height: 8,
          width: 8,
          borderRadius: 8,
        },
        quantity: 1,
      },
    ],
    [],
  );

  let renderItem = useCallback(
    (item: Card, _: number) => {
      return (
        <Block width={ITEM_WIDTH} paddingHorizontal={ITEM_GAP / 2}>
          <CardBodyView
            item={item}
            handleNavToDetail={async () => props.navigationToDetailCard(item)}
            handleFunction={(feature: any) => props.handleFeatureAction(feature, item)}
            handleActiveCardListener={async () => props.handleActiveCardListener(item, true)}
          />
        </Block>
      );
    },
    [props.navigationToDetailCard, props.handleFeatureAction, props.handleActiveCardListener],
  );

  const scrollHandler = useAnimatedScrollHandler(event => {
    progress.value = event.contentOffset.x / ITEM_WIDTH;
    activeIndex.value = Math.round(progress.value);
  });

  const snapOffsets = useMemo(() => props.itemList.map((_, index) => index * ITEM_WIDTH), [props.itemList]);

  /**
   * Allows skipping the measurement of dynamic content if you know the size (height or width) of items ahead of time.
   */
  const getItemLayout: (
    data: Nullish<ArrayLike<Card>>,
    index: number,
  ) => {length: number; offset: number; index: number} = useCallback(
    (_, index) => ({
      length: ITEM_WIDTH,
      offset: ITEM_WIDTH * index,
      index,
    }),
    [],
  );

  if (!props.itemList?.length) {
    return <View />;
  }
  return (
    <View style={styles.carouselContainer}>
      <WrapperFlatList
        ref={flatListRef}
        testID={'cm.list-card.slider'}
        data={props.itemList}
        horizontal
        keyExtractor={item => `card-slider-${item.id}`}
        snapToOffsets={snapOffsets}
        contentContainerStyle={styles.contentContainer}
        onScroll={scrollHandler}
        showsHorizontalScrollIndicator={false}
        pagingEnabled
        decelerationRate={'fast'}
        scrollEventThrottle={16}
        windowSize={11}
        initialNumToRender={11}
        maxToRenderPerBatch={11}
        style={styles.carousel}
        bounces={false}
        getItemLayout={getItemLayout}
        renderItem={({index, item}) => renderItem(item, index)}
      />

      <Block middle>
        <CustomCarouselDots
          length={props.itemList.length}
          currentIndex={activeIndex}
          progress={progress}
          maxIndicators={6}
          activeIndicatorConfig={styles.activeIndicatorConfig}
          inactiveIndicatorConfig={styles.inactiveIndicatorConfig}
          decreasingDots={decreasingDots}
          scrollableDotsConfig={{
            setIndex: activeIndex,
            onNewIndex: newIndex => {
              flatListRef?.current?.scrollToOffset?.({
                offset: newIndex * ITEM_WIDTH,
                animated: true,
              });
            },
            containerBackgroundColor: ColorGlobal.NeutralWhite,
            container: styles.scrollableDotsContainer,
          }}
        />
      </Block>
    </View>
  );
};
