import {screenDimensions} from '@constants/sizes';
import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeAlias}) => ({
  imageContainer: {
    flex: 1,
    borderRadius: SizeAlias.Radius3,
    overflow: 'hidden',
    marginLeft: 16,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  containerCustomStyle: {
    width: screenDimensions.width,
    flexGrow: 0,
  },
}));
