import FastImage from '@d11/react-native-fast-image';
import {useMSBStyles} from 'msb-shared-component';
import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';
import Skeleton from 'react-native-reanimated-skeleton';
import {styleSheet} from './styles';
import {BannerCardItemProps} from './type';

export const BannerCardItem: React.FC<BannerCardItemProps> = ({source}) => {
  const {styles} = useMSBStyles(styleSheet);
  const [loading, setLoading] = useState(true);

  return (
    <View style={styles.imageContainer}>
      <FastImage
        source={source}
        style={styles.image}
        resizeMode={'stretch'}
        onLoadStart={() => setLoading(true)}
        onLoadEnd={() => setLoading(false)}
      />
      <Skeleton isLoading={loading} containerStyle={StyleSheet.absoluteFillObject} layout={[styles.image]} />
    </View>
  );
};
