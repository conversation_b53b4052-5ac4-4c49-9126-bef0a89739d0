import {screenDimensions} from '@constants/sizes.ts';
import {FastImageProps} from '@d11/react-native-fast-image';
import {useMSBStyles} from 'msb-shared-component';
import React, {memo, useCallback} from 'react';
import {View} from 'react-native';
import Carousel, {CarouselRenderItem} from 'react-native-reanimated-carousel';
import {BannerCardItem} from './BannerCardItem.tsx';
import {styleSheet} from './styles.tsx';
import {BannerCardProps} from './type';

const SLIDE_WIDTH = screenDimensions.width * 0.76;
const SLIDE_HEIGHT = (SLIDE_WIDTH * 120) / 273;

const BannerCard: React.FC<BannerCardProps> = ({itemList}) => {
  const {styles} = useMSBStyles(styleSheet);

  const renderItem: CarouselRenderItem<FastImageProps['source']> = useCallback(({item, index}) => {
    return <BannerCardItem key={`banner-card-${index}`} source={item} />;
  }, []);

  return (
    <View>
      <Carousel
        data={itemList}
        loop={false}
        renderItem={renderItem}
        vertical={false}
        height={SLIDE_HEIGHT}
        width={SLIDE_WIDTH}
        style={styles.containerCustomStyle}
      />
    </View>
  );
};

export default memo(BannerCard);
