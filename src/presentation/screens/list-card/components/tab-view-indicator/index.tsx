import {LocaleDirection} from '@react-navigation/native';
import * as React from 'react';
import {type StyleProp, type ViewStyle} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {NavigationState, Route, SceneRendererProps} from 'react-native-tab-view';
import {styles} from './styles';

export type GetTabWidth = (index: number) => number;

export type Props<T extends Route> = SceneRendererProps & {
  navigationState: NavigationState<T>;
  width: 'auto' | `${number}%` | number;
  getTabWidth: GetTabWidth;
  direction: LocaleDirection;
  style?: StyleProp<ViewStyle>;
  gap?: number;
  children?: React.ReactNode;
};

export function TabBarIndicator<T extends Route>({
  getTabWidth,
  layout,
  navigationState,
  width,
  style,
  children,
}: Props<T>) {
  const isIndicatorShown = React.useRef(false);
  const isWidthDynamic = width === 'auto';

  const opacity = useSharedValue(isWidthDynamic ? 0 : 1);

  const indicatorVisible = isWidthDynamic
    ? layout.width && navigationState.routes.slice(0, navigationState.index).every((_, r) => getTabWidth(r))
    : true;

  React.useEffect(() => {
    if (
      !isIndicatorShown.current &&
      isWidthDynamic &&
      // We should fade-in the indicator when we have widths for all the tab items
      indicatorVisible
    ) {
      isIndicatorShown.current = true;

      opacity.value = withTiming(1, {
        duration: 150,
        easing: Easing.in(Easing.linear),
      });
    }
  }, [indicatorVisible, isWidthDynamic, opacity]);

  const {routes} = navigationState;

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: width === 'auto' ? 1 : width,
      start: `${(100 / routes.length) * navigationState.index}%`,
      opacity: width === 'auto' ? opacity.value : 1,
    };
  });

  return <Animated.View style={[styles.indicator, animatedStyle, style]}>{children}</Animated.View>;
}
