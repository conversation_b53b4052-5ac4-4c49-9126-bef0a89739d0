import {translate} from '@locales';
import {MSBEmptyState, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles.tsx';

const ListCardEmpty = () => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <View style={styles.container}>
      <MSBEmptyState
        testID="cm.list-card.empty"
        emptyTitle={translate('card.list_card_empty_title')}
        emptySubTitle={translate('card.list_card_empty_desc')}
      />
    </View>
  );
};

export default ListCardEmpty;
