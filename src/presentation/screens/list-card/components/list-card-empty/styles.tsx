import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, SizeTag, Typography}) => ({
  container: {
    padding: 40,
    marginHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: SizeTag.BorderRadius,
  },
  contentContainer: {
    paddingVertical: 4,
    alignItems: 'center',
  },
  image: {
    width: 144,
    height: 144,
    padding: 8,
  },
  title: {
    ...Typography?.base_semiBold,
    textAlign: 'center',
    color: ColorAlias.TextPrimary,
  },
  desc: {
    ...Typography?.small_regular,
    paddingTop: 4,
    textAlign: 'center',
    color: ColorAlias.TextPrimary,
  },
}));
