import images from '@assets/images';
import {cardAspectRatio, getCardRoundedCorner} from '@constants/sizes';
import FastImage from '@d11/react-native-fast-image';
import {CardDomainStatus, STATUS_GROUPS} from '@domain/entities/card/CardDomainStatus';
import {translate} from '@locales';
import {Text} from '@presentation/components/text';
import {isNotNullOrUndefined} from '@utils/StringFormat';
import {MSBIcon, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {Image, View} from 'react-native';
import Animated, {useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {ITEM_GAP, ITEM_WIDTH} from '../../constants';
import {styleSheet as sharedStyleSheet} from '../slide-card-body/styles';
import {styleSheet} from './styles';
import {CardImageProps} from './type';

const AnimatedImage = Animated.createAnimatedComponent(Image);

const CardImage: React.FC<CardImageProps> = ({card, ...props}) => {
  const {styles} = useMSBStyles(sharedStyleSheet);
  const {styles: internalStyles} = useMSBStyles(styleSheet);
  const visible = useSharedValue(1);
  const aspectRatio = useSharedValue(cardAspectRatio);

  const defaultAnimStyle = useAnimatedStyle(() => {
    return {
      opacity: visible.value,
    };
  });

  const overlayAnimStyle = useAnimatedStyle(() => {
    const horizontalWidth = ITEM_WIDTH - 3 * ITEM_GAP;
    const verticalHeight = horizontalWidth / cardAspectRatio;
    return {
      opacity: visible.value === 0 && aspectRatio.value !== 0 ? 1 : 0,
      aspectRatio: aspectRatio.value,
      borderRadius: getCardRoundedCorner(aspectRatio.value === cardAspectRatio ? horizontalWidth : verticalHeight),
    };
  });

  const statusUIData = getStatusUIData(card.status);

  return (
    <View style={styles.imageContent}>
      <View style={styles.cardImage}>
        <AnimatedImage
          resizeMode={props.resizeMode}
          style={[internalStyles.default, defaultAnimStyle]}
          source={images.card_default}
        />
        <FastImage
          style={[internalStyles.cardImage]}
          {...props}
          source={{
            uri: card.cardVisual?.images?.[0]?.imageURL ?? '',
          }}
          resizeMode="contain"
          onLoadStart={() => (visible.value = 1)}
          onLoadEnd={() => {
            visible.value = withTiming(0);
          }}
          onLoad={({nativeEvent}) => {
            if (nativeEvent.height > nativeEvent.width) {
              aspectRatio.value = nativeEvent.width / nativeEvent.height;
            }
          }}
        />
      </View>

      {isNotNullOrUndefined(statusUIData) && (
        <Animated.View style={[styles.statusOverlayContainer]}>
          <Animated.View style={[styles.overlay, overlayAnimStyle]} />
          <View style={styles.statusContainer}>
            {typeof statusUIData?.icon === 'string' ? (
              <MSBIcon icon={statusUIData?.icon} iconSize={32} />
            ) : (
              <FastImage resizeMode={'contain'} style={styles.iconStatus} source={statusUIData?.icon} />
            )}
            <Text testID={`cm.list-card.card-status.${card.id}`} style={styles.statusTxt}>
              {statusUIData?.title}
            </Text>
          </View>
        </Animated.View>
      )}
    </View>
  );
};

export default CardImage;

const getStatusUIData = (status?: CardDomainStatus) => {
  if (!status) {
    return null;
  }
  if (status === CardDomainStatus.Expired) {
    return {
      icon: 'extend-card-white',
      title: translate('status_card.expired'),
    };
  }

  if (status === CardDomainStatus.WaitingClose) {
    return {
      icon: 'card-remove-white',
      title: translate('status_card.waiting_close'),
    };
  }

  if (STATUS_GROUPS.INACTIVE.includes(status)) {
    return {
      icon: 'card-remove-white',
      title: translate('status_card.inactive'),
    };
  }

  if (STATUS_GROUPS.TEMP_BLOCK.includes(status)) {
    return {
      icon: 'lock-white',
      title: translate('status_card.block_temp'),
    };
  }

  if (STATUS_GROUPS.BLOCKED.includes(status)) {
    return {
      icon: 'lock-white',
      title: translate('status_card.close'),
    };
  }

  return null;
};
