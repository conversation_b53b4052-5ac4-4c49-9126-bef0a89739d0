import {listBannerCard} from '@constants';
import {TabViewRoute} from '@entities/card/ListCard';
import {useTabSceneSelectors} from '@store/ListCard';
import {useTabViewModel} from '@view-models/useTabViewModel';
import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {RefreshControl, ScrollView} from 'react-native';
import {TabViewProps} from 'react-native-tab-view';
import {styleSheet} from '../../styles';
import BannerCard from '../banner_card';
import {CardSlider} from '../card-slider';

const CardScene: TabViewProps<TabViewRoute>['renderScene'] = ({route}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {getCardList, navigationToDetailCard, handleFeatureAction, handleActiveCardListener} = useTabViewModel();
  const {cards} = useTabSceneSelectors(route.key);

  return (
    <ScrollView
      testID="cm.list-card.list"
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}
      refreshControl={<RefreshControl refreshing={false} onRefresh={getCardList} />}
      contentContainerStyle={styles.contentContainerStyle}>
      <CardSlider
        itemList={cards}
        navigationToDetailCard={navigationToDetailCard}
        handleFeatureAction={handleFeatureAction}
        handleActiveCardListener={handleActiveCardListener}
      />
      <BannerCard itemList={listBannerCard} />
    </ScrollView>
  );
};

export default CardScene;
