import ListingCardSkeleton from '@components/loading-skeleton/ListingCardSkeleton.tsx';
import {listBannerCard} from '@constants';
import {screenDimensions} from '@constants/sizes.ts';
import {TabViewRoute} from '@entities/card/ListCard.ts';
import {translate} from '@locales';
import {Block} from '@presentation/components/block/index.tsx';
import ModuleVersion from '@presentation/components/module-version/index.tsx';
import {EmptyType, MSBEmptyState, MSBPage, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {RefreshControl, ScrollView, View} from 'react-native';
import {SceneMap, TabBar, TabDescriptor, TabView, TabViewProps} from 'react-native-tab-view';
import BannerCard from './components/banner_card';
import CardScene from './components/card-scene';
import ListCardEmpty from './components/list-card-empty';
import {TabBarIndicator} from './components/tab-view-indicator/index.tsx';
import TabViewLabel from './components/tab-view-label';
import useListCardViewModel from './hook.tsx';
import {styleSheet} from './styles.tsx';
import {SlideTabBarKey} from './types.ts';

const renderScene = SceneMap({
  [SlideTabBarKey.Credits]: CardScene,
  [SlideTabBarKey.Debits]: CardScene,
});

const ListCardScreen = () => {
  const {theme, styles} = useMSBStyles(styleSheet);

  const {error, loading, tabViewIndex, isEmpty, topTabbar, setTabViewIndex, getCardList, onCreateCard} =
    useListCardViewModel();

  const onRefresh = useCallback(() => {
    getCardList();
  }, [getCardList]);

  const renderTopTabBar = useCallback<Exclude<TabViewProps<TabViewRoute>['renderTabBar'], undefined>>(
    props => {
      return (
        <TabBar
          {...props}
          indicatorStyle={{backgroundColor: theme.ColorTab.TextActive}}
          activeColor={theme.ColorTab.TextActive}
          inactiveColor={theme.ColorTab.TextDefault}
          contentContainerStyle={styles.tabBarContentContainer}
          style={styles.tabBarContainer}
          renderIndicator={TabBarIndicator}
          onTabPress={tabPressProps => {
            if (tabPressProps.route.disabled) {
              tabPressProps.preventDefault();
            }
          }}
        />
      );
    },
    [theme, styles],
  );

  const renderTabLabel = useCallback<Exclude<TabDescriptor<TabViewRoute>['label'], undefined>>(
    props => {
      const focused =
        (props.route.key === SlideTabBarKey.Credits && tabViewIndex === 0) ||
        (props.route.key === SlideTabBarKey.Debits && tabViewIndex === 1);
      return <TabViewLabel {...props} focused={focused} />;
    },
    [tabViewIndex],
  );

  const renderContent = useCallback(() => {
    if (error) {
      return (
        <ScrollView
          style={styles.errorScrollViewContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.errorContentContainer}
          refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}>
          <Block style={styles.errorContainer}>
            <MSBEmptyState
              testID="cm.error-view"
              type={EmptyType.System}
              emptyTitle={translate('error_title')}
              emptySubTitle={translate('error_desc')}
            />
          </Block>
          <BannerCard itemList={listBannerCard} />
        </ScrollView>
      );
    }

    if (isEmpty) {
      return (
        <ScrollView
          testID="cm.list-card.list"
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}
          contentContainerStyle={styles.emptyContentContainer}>
          <ListCardEmpty />
          <BannerCard itemList={listBannerCard} />
        </ScrollView>
      );
    }

    return (
      <TabView
        commonOptions={{
          label: renderTabLabel,
        }}
        navigationState={{
          index: tabViewIndex,
          routes: topTabbar,
        }}
        renderScene={renderScene}
        onIndexChange={setTabViewIndex}
        initialLayout={{height: 0, width: screenDimensions.width}}
        renderTabBar={renderTopTabBar}
        swipeEnabled={false}
      />
    );
  }, [
    styles,
    topTabbar,
    tabViewIndex,
    isEmpty,
    error,
    screenDimensions,
    listBannerCard,
    renderTopTabBar,
    setTabViewIndex,
    onRefresh,
  ]);

  return (
    <MSBPage
      testID="cm.listCardScreen"
      backgroundProps={{nameImage: 'bg_main'}}
      isScrollable={false}
      headerProps={{
        hasBack: true,
        title: translate('slide_card.top_bar_title'),
        rightButtons: [
          {
            icon: 'add-plus-circle-orange',
            title: translate('slide_card.top_bar_action'),
            onPress: onCreateCard,
          },
        ],
        barStyle: true,
      }}>
      <View style={styles.bgContainer}>
        <ListingCardSkeleton isLoading={loading}>{renderContent()}</ListingCardSkeleton>
        <ModuleVersion />
      </View>
    </MSBPage>
  );
};

export default ListCardScreen;
