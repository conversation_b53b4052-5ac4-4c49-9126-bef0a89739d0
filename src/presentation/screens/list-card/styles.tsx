import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorCarousel, ColorGlobal, ColorHeader, SizeGlobal}) => ({
  loadingContainer: {
    padding: 100,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loading: {
    color: ColorAlias.SurfaceBrand,
  },
  errorScrollViewContainer: {
    flex: 1,
  },
  errorContentContainer: {
    gap: 24,
    paddingVertical: 24,
  },
  errorContainer: {
    marginHorizontal: 16,
    borderRadius: 16,
    overflow: 'hidden',
    paddingVertical: 16,
    alignItems: 'center',
    paddingHorizontal: 16,
    justifyContent: 'center',
    backgroundColor: ColorAlias.BackgroundPrimary,
  },
  bgContainer: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
  },
  background: {},
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    width: '100%',
  },
  backButton: {
    width: 24,
    height: 24,
    padding: 8,
    tintColor: ColorHeader.IconDefault,
  },

  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    height: 200,
    padding: 20,
    marginStart: 25,
    marginEnd: 25,
  },
  pagerView: {
    flex: 1,
  },
  page: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  scene: {flex: 1, justifyContent: 'center', alignItems: 'center'},
  paginationContainer: {
    paddingTop: 10,
  },
  activeDot: {
    width: 24,
    height: 8,
    borderRadius: 5,
    backgroundColor: ColorCarousel.DotSelect,
  },
  inactiveDot: {
    width: 8,
    height: 8,
    borderRadius: 5,
    backgroundColor: ColorCarousel.DotDefault,
  },
  scrollView: {paddingBottom: 36},
  emptyContentContainer: {
    gap: 24,
    paddingVertical: 24,
  },
  contentContainerStyle: {paddingBottom: 36, gap: SizeGlobal.Size600},
  tabBarContentContainer: {
    height: SizeGlobal.Size1200,
  },
  tabBarContainer: {backgroundColor: ColorGlobal.NeutralWhite, elevation: 1},
}));
