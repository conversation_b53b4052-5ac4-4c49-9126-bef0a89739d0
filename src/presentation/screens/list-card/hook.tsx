// useListCardViewModel.ts
import {RootStackParamList} from '@app-navigation/types.ts';
import {isLeft} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useListingCardActionSelectors, useTabViewSelectors} from '@store/ListCard';
import {showErrorCodePopup} from '@utils/CommonHandler';
import {useTabViewModel} from '@view-models/useTabViewModel.ts';
import {hostSharedModule} from 'msb-host-shared-module';
import {useLayoutEffect} from 'react';

const useListCardViewModel = () => {
  const {getCardList} = useTabViewModel();
  const {error, loading, tabViewIndex, isEmpty, topTabbar} = useTabViewSelectors();
  const {setTabViewIndex, dispose} = useListingCardActionSelectors();

  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  useLayoutEffect(() => {
    getCardList();

    return dispose;
  }, []);

  const onCreateCard = async () => {
    hostSharedModule.d.domainService.addSpinnerRequest();

    const getInitializeCardOpeningFlowUseCase = DIContainer.getInstance().getInitializeCardOpeningFlowUseCase();
    const result = await getInitializeCardOpeningFlowUseCase.execute();
    hostSharedModule.d.domainService.addSpinnerCompleted();
    if (isLeft(result)) {
      showErrorCodePopup({errorCode: result.error.code});
      return;
    }

    const data = result.data;
    if (data && data.isDropOff) {
      navigation.navigate('CardOpenNoticeDropOffScreen', {
        flowId: 'mock',
      });
    } else {
      navigation.navigate('CardOpenSelectCardScreen', {
        flowId: 'mock',
      });
    }
  };

  const goBackSuperApp = () => {
    navigation.goBack();
  };

  return {
    tabViewIndex,
    topTabbar,
    navigation,
    loading,
    error,
    isEmpty,
    setTabViewIndex,
    goBackSuperApp,
    getCardList,
    onCreateCard,
  };
};

export default useListCardViewModel;
