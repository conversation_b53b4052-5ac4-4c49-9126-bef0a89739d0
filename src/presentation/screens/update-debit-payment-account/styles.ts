import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorGlobal, SizeAlias}) => ({
  headerContentContainer: {
    marginBottom: 24,
    backgroundColor: ColorGlobal.NeutralWhite,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: SizeAlias.Radius3,
    marginHorizontal: 2,
  },

  list: {flex: 1},
  innerList: {height: '100%'},
  headerContainer: {
    paddingTop: 2,
  },
  contentContainerStyle: {
    marginHorizontal: 14,
    marginTop: 22,
    paddingBottom: 24,
    borderTopLeftRadius: SizeAlias.Radius3,
    borderTopRightRadius: SizeAlias.Radius3,
    overflow: 'hidden',
  },
  borderBottom: {borderBottomLeftRadius: SizeAlias.Radius3, borderBottomRightRadius: SizeAlias.Radius3},
  footerContainer: {paddingTop: 12, paddingHorizontal: 16},
  hideShadowEffect: {
    height: 2,
    position: 'absolute',
    top: -1,
    left: 0,
    right: 0,
    backgroundColor: ColorGlobal.NeutralWhite,
  },
  errorContainer: {
    borderBottomLeftRadius: SizeAlias.Radius4,
    borderBottomRightRadius: SizeAlias.Radius4,
    overflow: 'visible',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    marginHorizontal: 2,
  },
}));
