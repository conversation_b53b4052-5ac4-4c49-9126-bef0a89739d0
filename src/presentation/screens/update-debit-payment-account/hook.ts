import {useRoute} from '@react-navigation/native';
import {hostSharedModule} from 'msb-host-shared-module';
import {useCallback, useEffect, useState} from 'react';
import {NativeScrollEvent, NativeSyntheticEvent} from 'react-native';

import {ApplicationScreenProps} from '@app-navigation/types';
import {isLeft} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {CommonStateStatus} from '@entities/base/CommonState';
import {EAnnouncementResultsStatus, EAnnouncementResultsType} from '@entities/card/EAnnouncementResults';
import {waitForFinishingModalAnimation} from '@utils/index';
import useCardViewModel from '@view-models/useCardViewModel';
import {useLinkAccountViewModel} from '@view-models/useLinkAccountViewModel';

export const useUpdateDebitPaymentViewModel = () => {
  const {navigationToResultScreen, updateCardDetails} = useCardViewModel();

  const {
    params: {cardModel},
  } = useRoute<ApplicationScreenProps<'UpdateDebitPaymentAccount'>['route']>();

  const {loadingFirst, loadingMore, linkItems, hasError, getListLinkAccount} = useLinkAccountViewModel(cardModel);

  const [activeRbsNumber, setActiveRbsNumber] = useState(cardModel.rbsNumber);

  useEffect(() => {
    getListLinkAccount();
  }, []);

  const onRefresh = useCallback(() => {
    getListLinkAccount(true);
  }, []);

  const onMomentumScrollEnd = useCallback(
    ({nativeEvent: {layoutMeasurement, contentOffset, contentSize}}: NativeSyntheticEvent<NativeScrollEvent>) => {
      const paddingToBottom = 36;
      const isCanLoadMore = layoutMeasurement?.height + contentOffset?.y >= contentSize?.height - paddingToBottom;

      if (!isCanLoadMore) {
        return;
      }

      getListLinkAccount();
    },
    [],
  );

  const updateDebitPaymentAccount = async () => {
    hostSharedModule.d.domainService.addSpinnerRequest();

    await waitForFinishingModalAnimation();

    const changeCardStatusUseCase = DIContainer.getInstance().getUpdateDebitAccountPaymentUseCase();
    const result = await changeCardStatusUseCase.execute({
      cardId: cardModel.id,
      accountId: activeRbsNumber,
    });

    if (isLeft(result)) {
      hostSharedModule.d.domainService.addSpinnerCompleted();
      const error = result.error;

      navigationToResultScreen(
        cardModel,
        EAnnouncementResultsStatus.fail,
        EAnnouncementResultsType.updateDebitPaymentAccount,
        {
          errorCode: error?.code,
        },
      );
      return;
    }

    const data = result.data;
    if (data?.status === CommonStateStatus.SUCCESS && cardModel?.id) {
      await updateCardDetails(cardModel.id);
      navigationToResultScreen(
        cardModel,
        EAnnouncementResultsStatus.success,
        EAnnouncementResultsType.updateDebitPaymentAccount,
      );
    }
    hostSharedModule.d.domainService.addSpinnerCompleted();
  };

  return {
    cardModel,
    activeRbsNumber,
    linkItems,
    loadingFirst,
    loadingMore,
    hasError,
    setActiveRbsNumber,
    updateDebitPaymentAccount,
    onMomentumScrollEnd,
    onRefresh,
  };
};
