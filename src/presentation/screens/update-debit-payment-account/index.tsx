import {HeaderSection} from '@components/link-account/HeaderSection';
import {LinkAccountItemUI} from '@components/link-account/LinkAccountItemUI';
import {UpdateDebitPaymentAccountSkeleton} from '@components/loading-skeleton/UpdateDebitPaymentAccountSkeleton';
import {CardFeatureType} from '@entities/card/CardFeatureType';
import {LinkAccountItem, LinkAccountItemType} from '@entities/daily/ListLinkAccount';
import {translate} from '@locales';
import {MSBButton, MSBPage, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo} from 'react';
import {FlatList, RefreshControl, View} from 'react-native';
import ListFooterComponent from './components/ListFooterComponent';
import ListHeaderComponent from './components/ListHeaderComponent';
import {useUpdateDebitPaymentViewModel} from './hook';
import {styleSheet} from './styles';

const UpdateDebitPaymentAccount = () => {
  const {styles, theme} = useMSBStyles(styleSheet);

  const {
    cardModel,
    activeRbsNumber,
    linkItems,
    loadingFirst,
    loadingMore,
    hasError,
    setActiveRbsNumber,
    updateDebitPaymentAccount,
    onMomentumScrollEnd,
    onRefresh,
  } = useUpdateDebitPaymentViewModel();

  const renderItem = useCallback(
    (data: {item: LinkAccountItem; index: number}) => {
      if (data.item.type === LinkAccountItemType.HEADER_SECTION) {
        /**
         * Header section
         */
        return <HeaderSection featureType={CardFeatureType.LinkedAccount} />;
      }

      return (
        <LinkAccountItemUI
          featureType={CardFeatureType.LinkedAccount}
          loadingMore={loadingMore}
          isLast={data.index === linkItems.length - 1}
          linking={data.item}
          activeRbsNumber={activeRbsNumber}
          onPress={setActiveRbsNumber}
        />
      );
    },
    [linkItems.length, activeRbsNumber, loadingMore],
  );

  const extraData = useMemo(() => {
    return {
      cardModel,
      activeRbsNumber,
      loadingMore,
      hasError,
    };
  }, [cardModel, activeRbsNumber, loadingMore, hasError]);

  const renderListHeaderComponent = useCallback(() => {
    return <ListHeaderComponent card={cardModel} />;
  }, [cardModel]);

  return (
    <MSBPage
      testID="cm.updateDebitPaymentAccount"
      isScrollable={false}
      headerProps={{
        title: translate('update_debit_payment_account.header_title'),
        hasBack: true,
      }}>
      <UpdateDebitPaymentAccountSkeleton theme={theme} isLoading={loadingFirst} containerStyle={styles.list}>
        <FlatList
          extraData={extraData}
          showsVerticalScrollIndicator={false}
          ListHeaderComponentStyle={styles.headerContainer}
          ListHeaderComponent={renderListHeaderComponent}
          ListFooterComponent={
            <ListFooterComponent loadingMore={loadingMore} hasError={hasError} isEmpty={linkItems.length === 0} />
          }
          data={linkItems}
          style={styles.innerList}
          contentContainerStyle={[styles.contentContainerStyle]}
          renderItem={renderItem}
          keyExtractor={(_, index) => `link-account-${index}`}
          onMomentumScrollEnd={onMomentumScrollEnd}
          refreshControl={<RefreshControl refreshing={loadingFirst} onRefresh={onRefresh} />}
        />
      </UpdateDebitPaymentAccountSkeleton>
      {!loadingFirst && (
        <View style={[styles.footerContainer]}>
          <MSBButton
            testID="cm.update-debit-payment-account.confirm"
            disabled={activeRbsNumber === cardModel.rbsNumber}
            label={translate('update_debit_payment_account.confirm')}
            onPress={updateDebitPaymentAccount}
          />
        </View>
      )}
    </MSBPage>
  );
};

export default UpdateDebitPaymentAccount;
