import {sharedStyleSheet} from '@components/common/styles';
import {LoadLinkAccountSkeleton} from '@components/loading-skeleton/UpdateDebitPaymentAccountSkeleton';
import {translate} from '@locales';
import {EmptyType, MSBEmptyState, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from '../styles';
import {ListFooterComponentProps} from './type';

const ListFooterComponent: React.FC<ListFooterComponentProps> = ({hasError, isEmpty, loadingMore}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  if (loadingMore) {
    return <LoadLinkAccountSkeleton isLoading={true} />;
  }

  if (hasError) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.update-debit-payment-account.empty-state"
          type={EmptyType.System}
          emptyTitle={translate('error_title')}
          emptySubTitle={translate('error_desc')}
        />
        <View style={styles.hideShadowEffect} />
      </View>
    );
  }

  if (isEmpty) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.update-debit-payment-account.empty-state"
          type={EmptyType.Editable}
          emptyTitle={translate('update_debit_payment_account.no_data')}
          emptySubTitle={translate('update_debit_payment_account.no_data_desc')}
        />
        <View style={styles.hideShadowEffect} />
      </View>
    );
  }

  return null;
};

export default ListFooterComponent;
