import {sharedStyleSheet} from '@components/common/styles';
import GeneralInfoCard from '@components/general-info-card-view';
import {HeaderSection} from '@components/link-account/HeaderSection';
import {mapFromCardToGeneralInfo} from '@entities/card/Card';
import {CardFeatureType} from '@entities/card/CardFeatureType';
import {useMSBStyles} from 'msb-shared-component';
import React, {useMemo} from 'react';
import {View} from 'react-native';
import {styleSheet} from '../styles';
import {ListHeaderComponentProps} from './type';

const ListHeaderComponent: React.FC<ListHeaderComponentProps> = ({card}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const generalInfo = useMemo(() => mapFromCardToGeneralInfo(card), [card]);

  return (
    <>
      <View style={[sharedStyles.shadow, styles.headerContentContainer]}>
        <GeneralInfoCard data={generalInfo} />
      </View>
      <HeaderSection featureType={CardFeatureType.LinkedAccount} />
    </>
  );
};

export default ListHeaderComponent;
