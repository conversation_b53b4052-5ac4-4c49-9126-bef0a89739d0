import {SelectionItem} from '@entities/base/SelectionItem';
import {CardInstrument} from '@entities/card/CardInstrument';
import {ReferralInformation} from '@entities/card/ReferralInformation';
import {CardOpenFormData} from '@entities/form';
import {FormAdditionalInformation} from '@entities/form/FormAdditionalInformation';
import {ValidateMessageObject} from '@presentation/hooks/useErrorMessageTranslation';
import {formatMoney} from '@utils/StringFormat';
import {isNil} from 'lodash';
import {z} from 'zod';

export const getDefaultValues = (formData: Partial<CardOpenFormData>): FormAdditionalInformation => {
  return {
    province: formData.province ?? null,
    district: formData.district ?? null,
    ward: formData.ward ?? null,
    referralInformation: formData.referralInformation ?? null,
    defaultSourceAccount: '',
    sourceAccount: formData.sourceAccount ?? null,
    address: formData.address ?? '',
    secretQuestion: formData.secretQuestion ?? '',
    hasSecurityQuestion: true,
    referralCode: formData.referralCode ?? '',
    instrument: formData.instrument ?? CardInstrument.VIRTUAL,
  };
};

export const validation = z
  .object<ZodShape<FormAdditionalInformation>>({
    province: z.custom<SelectionItem>().nullable(),
    district: z.custom<SelectionItem>().nullable(),
    ward: z.custom<SelectionItem>().nullable(),
    referralInformation: z.custom<ReferralInformation>().nullable(),
    defaultSourceAccount: z.string(),
    sourceAccount: z
      .custom<SourceAccountModel>()
      .nullable()
      .refine(data => {
        return !isNil(data);
      })
      .refine(
        data => {
          // @todo so du toi thieu 50000
          return (data?.availableBalance ?? 0) > 50000;
        },
        {
          message: JSON.stringify({
            keyT: 'select_card_to_open.insufficient_minimum_balance',
            options: {
              amount: formatMoney(50000),
            },
          } as ValidateMessageObject),
        },
      ),
    address: z.string().trim(),
    hasSecurityQuestion: z.boolean(),
    secretQuestion: z.string().trim(),
    referralCode: z.string(),
    instrument: z.custom<CardInstrument>(),
  })
  .superRefine((values, ctx) => {
    if (!values.hasSecurityQuestion && values.secretQuestion.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['secretQuestion'],
      });
    }
    if (values.instrument === CardInstrument.VIRTUAL) {
      return;
    }

    if (isNil(values.province?.id)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['province'],
      });
    }
    if (isNil(values.district?.id)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['district'],
      });
    }
    if (isNil(values.ward?.id)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['ward'],
      });
    }
    if (values.address.trim().length === 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['address'],
      });
    }
  });
