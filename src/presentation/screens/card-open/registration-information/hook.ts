// useListCardViewModel.ts
import {ApplicationScreenProps, RootStackParamList} from '@app-navigation/types.ts';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {isLeft, makeLeft, makeRight, ResultState} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {CardInstrument} from '@domain/entities/card/CardInstrument';
import {ReferralInformation} from '@domain/entities/card/ReferralInformation';
import {FormAdditionalInformation} from '@domain/entities/form/FormAdditionalInformation';
import {zodResolver} from '@hookform/resolvers/zod';
import {useCardOpenFlowActions} from '@presentation/store/CardOpenFlow';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {waitForRendering} from '@utils';
import {showErrorCodePopup} from '@utils/CommonHandler';
import {useAdditionalInformationViewModel} from '@view-models/useAdditionalInformationViewModel';
import {useCallback, useLayoutEffect, useMemo, useState} from 'react';
import {useForm} from 'react-hook-form';
import {getDefaultValues, validation} from './form';

const usePreloadRegistrationInformationViewModel = () => {
  const [loading, setLoading] = useState(false);

  useLayoutEffect(() => {
    bootstrap();
  }, []);

  const bootstrap = useCallback(async () => {
    setLoading(true);
    const result = await DIContainer.getInstance().getGetListLinkAccountUseCase().execute({
      from: 0,
    });

    if (isLeft(result)) {
      showErrorCodePopup({errorCode: result.error.code});
      setLoading(false);
      return;
    }

    const listLinkAccount = result.data;
    if (!listLinkAccount) {
      showErrorCodePopup({errorCode: MSBErrorCode.Default});
      setLoading(false);
      return;
    }

    setLoading(false);
  }, []);

  return {
    isLoading: loading,
  };
};

const useCardOpenRegistrationInformationViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {
    params: {flowId},
  } = useRoute<ApplicationScreenProps<'CardOpenRegistrationInformationScreen'>['route']>();

  const {getFormState, setFormState} = useCardOpenFlowActions();
  const defaultValues = useMemo(() => getFormState(flowId), [flowId, getFormState]);
  const {control, getValues, reset, setValue, trigger, setError} = useForm<FormAdditionalInformation>({
    mode: 'onBlur',
    resolver: zodResolver(validation),
    defaultValues: getDefaultValues(defaultValues),
    shouldFocusError: false,
  });

  const {getMetadata} = useAdditionalInformationViewModel();
  const {setLoading} = useCardOpenFlowActions();

  const additionalAddressVisible = useMemo(() => defaultValues.instrument === CardInstrument.PHYSICAL, [defaultValues]);
  const getProvinces = useCallback(() => {
    return getMetadata('province', DIContainer.getInstance().getGetProvincesUseCase().execute());
  }, []);

  const getDistricts = useCallback(() => {
    const provinceId = getValues('province.id').toString();
    return getMetadata(
      `${provinceId}.district`,
      DIContainer.getInstance().getGetDistrictsUseCase().execute({provinceId}),
    );
  }, [getValues, getMetadata]);

  const getWards = useCallback(() => {
    const provinceId = getValues('province.id').toString();
    const districtId = getValues('district.id').toString();
    return getMetadata(
      `${provinceId}.${districtId}.ward`,
      DIContainer.getInstance().getGetWardsUseCase().execute({provinceId, districtId}),
    );
  }, [getValues, getMetadata]);

  const getReferralInformation = useCallback(
    async (referralCode: string): Promise<ResultState<ReferralInformation>> => {
      setLoading(flowId, true);
      await waitForRendering(1000);
      setLoading(flowId, false);
      if (referralCode.toLowerCase() === 'error') {
        return makeLeft({
          message: 'select_card_to_open.referral_code_invalid',
          code: 'INVALID_CODE',
          name: '',
        });
      }

      return makeRight({
        referralCode,
        fullName: referralCode,
        departmentName: 'MSB Test 1',
      });
    },
    [],
  );

  const goToConfirmationInformation = useCallback(() => {
    setFormState(flowId, getValues());
    navigation.navigate('CardOpenConfirmInfoScreen', {
      flowId,
    });
  }, [navigation, flowId, setFormState, getValues]);

  return {
    control,
    flowId,
    additionalAddressVisible,
    getValues,
    reset,
    trigger,
    setValue,
    setError,
    getProvinces,
    getDistricts,
    getWards,
    getReferralInformation,
    goToConfirmationInformation,
  };
};

export {useCardOpenRegistrationInformationViewModel, usePreloadRegistrationInformationViewModel};
