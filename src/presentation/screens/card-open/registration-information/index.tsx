import {Block} from '@components/block';
import {FormAddressSelection, FormAddressSelectionWatch} from '@components/form/FormAddressSelection';
import FormSection from '@components/form/FormSection';
import FormTextArea from '@components/form/FormTextArea';
import {FormAddressSelectionRef} from '@components/form/types';
import {translate} from '@locales';
import {ColorHeader, getSize, MSBPage, MSBScrollView, MSBStepper, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useRef} from 'react';
import {View} from 'react-native';
import FormReferralCode from './components/FormReferralCode';
import FormRegistrationButton from './components/FormRegistrationButton';
import FormSecretQuestion from './components/FormSecretQuestion';
import FormSourceAccount from './components/FormSourceAccount';
import {useCardOpenRegistrationInformationViewModel} from './hook';
import {styleSheet} from './styles';

const CardOpenRegistrationInformationWrapper = () => {
  const {styles} = useMSBStyles(styleSheet);

  const renderContent = useCallback(() => {
    return <CardOpenRegistrationInformationScreen />;
  }, []);

  return (
    <MSBPage
      testID="cm.cardOpenRegistrationInformationWrapper"
      backgroundColor={ColorHeader.SurfaceLevel1}
      isScrollable={false}
      headerProps={{
        title: translate('select_card_to_open.title'),
        hasBack: true,
        barStyle: true,
        childrenBottomHeader: (
          <View style={styles.headerContent}>
            <MSBStepper
              title={translate('select_card_to_open.select_card')}
              totalSteps={3}
              currentStep={2}
              progress={1}
            />
          </View>
        ),
      }}>
      {renderContent()}
    </MSBPage>
  );
};

const CardOpenRegistrationInformationScreen = () => {
  const {styles, theme} = useMSBStyles(styleSheet);
  const {SizeCard} = theme;

  const provinceRef = useRef<FormAddressSelectionRef>(null);
  const districtRef = useRef<FormAddressSelectionRef>(null);
  const wardRef = useRef<FormAddressSelectionRef>(null);

  const {
    control,
    flowId,
    additionalAddressVisible,
    trigger,
    setValue,
    setError,
    getProvinces,
    getDistricts,
    getWards,
    goToConfirmationInformation,
    getReferralInformation,
  } = useCardOpenRegistrationInformationViewModel();

  return (
    <Block flex={1}>
      <MSBScrollView contentContainerStyle={styles.scrollView}>
        <FormSection title={translate('select_card_to_open.registration_info')}>
          <Block paddingVertical={SizeCard.SpacingLeft} paddingHorizontal={SizeCard.SpacingLeft} gap={getSize(16)}>
            <FormSourceAccount control={control} nameTrigger="sourceAccount" trigger={trigger} />
            <FormSecretQuestion control={control} nameTrigger="secretQuestion" watchName="hasSecurityQuestion" />
            <FormReferralCode
              control={control}
              nameTrigger="referralCode"
              flowId={flowId}
              setValue={setValue}
              setError={setError}
              getReferralInformation={getReferralInformation}
            />
          </Block>
        </FormSection>
        {additionalAddressVisible && (
          <FormSection title={translate('select_card_to_open.card_delivery_address')}>
            <Block paddingVertical={SizeCard.SpacingLeft} paddingHorizontal={SizeCard.SpacingLeft} gap={getSize(16)}>
              <FormAddressSelection
                testID="cm.card-open.province"
                ref={provinceRef}
                control={control}
                trigger={trigger}
                nameTrigger="province"
                getItems={getProvinces}
                label={translate('additionalInfo.province_or_city')}
                placeholder={translate('additionalInfo.select_province')}
                placeholderSearch={translate('additionalInfo.province_city_name')}
                onValueChange={(current, previous) => {
                  if (current.id !== previous?.id) {
                    setValue('district', null, {shouldValidate: true});
                    setValue('ward', null, {shouldValidate: true});
                  }
                  districtRef.current?.trigger();
                }}
              />
              <FormAddressSelectionWatch
                testID="cm.card-open.district"
                ref={districtRef}
                control={control}
                nameTrigger="district"
                watchName={'province'}
                getItems={getDistricts}
                trigger={trigger}
                label={translate('additionalInfo.district')}
                placeholder={translate('additionalInfo.select_district')}
                placeholderSearch={translate('additionalInfo.district_name')}
                onValueChange={(current, previous) => {
                  if (current.id !== previous?.id) {
                    setValue('ward', null, {shouldValidate: true});
                  }
                  wardRef.current?.trigger();
                }}
              />
              <FormAddressSelectionWatch
                testID="cm.card-open.ward"
                ref={wardRef}
                control={control}
                trigger={trigger}
                nameTrigger="ward"
                watchName="district"
                getItems={getWards}
                label={translate('additionalInfo.ward')}
                placeholder={translate('additionalInfo.select_ward')}
                placeholderSearch={translate('additionalInfo.ward_name')}
              />
              <FormTextArea
                testID="cm.card-open.address-input"
                control={control}
                nameTrigger="address"
                label={translate('additionalInfo.address')}
                placeholder={translate('additionalInfo.street_address_detail')}
              />
            </Block>
          </FormSection>
        )}
      </MSBScrollView>
      <Block paddingHorizontal={getSize(16)} paddingTop={getSize(12)}>
        <FormRegistrationButton flowId={flowId} control={control} onPress={goToConfirmationInformation} />
      </Block>
    </Block>
  );
};

export default CardOpenRegistrationInformationWrapper;
