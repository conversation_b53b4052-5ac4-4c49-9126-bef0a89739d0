import {Federated} from '@callstack/repack/client';
import {Block} from '@components/block';
import {Text} from '@components/text';
import {FormAdditionalInformation} from '@entities/form/FormAdditionalInformation';
import {useErrorMessageTranslation} from '@hooks/useErrorMessageTranslation';
import {translate} from '@locales';
import {ScreenLoadFailMicroApp} from '@utils/LoadFailMicroApp';
import {orderBy} from 'lodash';
import {ErrorBoundary} from 'msb-host-shared-module';
import {MSBLoadingItemSkeleton} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FieldPath, useController} from 'react-hook-form';
import {FormSourceAccountProps} from './types';

/**
 * Khi ở build time sẽ sử dụng MFv1 nên là không thể sử dụng dynamic import của MFv2
 * => Sử dụng {@link Federated.importModule} để bundle
 *
 * Lưu ý!: không tách biệt điều kiện process.env.NODE_ENV === 'production' ra hàm riêng biệt vì lúc build sẽ làm cho dynamic import() được bundle vào
 * => gây ra lỗi lúc build
 *
 * Không thể sử dụng được dynamic import() từ hàm
 * => !Nên mỗi khi load module thì phải định nghĩa lại như dưới
 */
const SourceAccount = React.lazy(() =>
  process.env.MF_VERSION !== 'v2' || process.env.NODE_ENV === 'production'
    ? Federated.importModule('TransferModule', './SourceAccount')
    : import('TransferModule/SourceAccount'),
);

const FormSourceAccount = React.memo(
  ({control, nameTrigger, trigger}: FormSourceAccountProps<FormAdditionalInformation>) => {
    const {
      field,
      fieldState: {error},
    } = useController({
      control,
      name: nameTrigger as FieldPath<FormAdditionalInformation>,
    });
    const message = useErrorMessageTranslation(error?.message);

    const onSelectAccount = useCallback(
      (account?: SourceAccountModel) => {
        if (!account) {
          return;
        }

        field.onChange(account);
        trigger?.(nameTrigger);
      },
      [field],
    );

    const FallbackComponent = useCallback(() => {
      return <ScreenLoadFailMicroApp id="TransferSourceAccount" />;
    }, []);

    /**
     * Trong list tài khoản hợp lệ → mặc định hiển thị default tài khoản có số dư lớn nhất, các tài khoản còn lại hiển thị theo số dư giảm dần.
     */
    const handleFilterAccounts = useCallback((accounts: SourceAccountModel[]): SourceAccountModel[] => {
      const transformAccounts = orderBy(accounts, (item: SourceAccountModel) => item.availableBalance ?? 0, ['desc']);
      return transformAccounts.map((item, idx) => ({
        ...item,
        isDefault: idx === 0 ? 'Y' : 'N',
      }));
    }, []);

    return (
      <ErrorBoundary FallbackComponent={FallbackComponent}>
        <React.Suspense fallback={<MSBLoadingItemSkeleton loading />}>
          <SourceAccount
            title={translate('select_card_to_open.linked_payment_account')}
            onSelectAccount={onSelectAccount}
            handleFilterAccounts={handleFilterAccounts}
            errorTitle={message}
          />
        </React.Suspense>
      </ErrorBoundary>
    );
  },
);

export default FormSourceAccount;
