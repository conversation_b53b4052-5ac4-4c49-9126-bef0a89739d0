import {ResultState} from '@core/ResultState';
import {ReferralInformation} from '@domain/entities/card/ReferralInformation';
import {FieldPath, FieldValues} from 'react-hook-form';

export interface FormSecretQuestionProps<T extends FieldValues> extends FormControl<T> {
  watchName: FieldPath<T>;
}
export interface FormSourceAccountProps<T extends FieldValues> extends FormControl<T> {}

export interface FormReferralCodeProps<T extends FieldValues> extends FormControl<T> {
  flowId: string;
  getReferralInformation: (referralCode: string) => Promise<ResultState<ReferralInformation>>;
}

export interface FormRegistrationButtonProps<T extends FieldValues> extends Omit<FormControl<T>, 'nameTrigger'> {
  flowId: string;
  onPress?: () => void;
}
