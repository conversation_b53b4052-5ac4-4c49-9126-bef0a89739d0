import {Block} from '@components/block';
import {getFormLayout} from '@components/loading-skeleton/layout';
import {isLeft} from '@core/ResultState';
import {FormAdditionalInformation} from '@domain/entities/form/FormAdditionalInformation';
import {useErrorMessageTranslation} from '@hooks/useErrorMessageTranslation';
import {translate} from '@locales';
import {useCardOpenProcessing} from '@store/CardOpenFlow';
import {getSize, MSBInputBase, MSBLabel} from 'msb-shared-component';
import React, {useCallback, useRef} from 'react';
import {FieldPath, useController, useWatch} from 'react-hook-form';
import Skeleton from 'react-native-reanimated-skeleton';
import {FormReferralCodeProps} from './types';

const FormReferralCode = ({
  control,
  nameTrigger,
  flowId,
  setValue,
  setError,
  getReferralInformation,
}: FormReferralCodeProps<FormAdditionalInformation>) => {
  const {
    field,
    fieldState: {error},
  } = useController({
    control,
    name: nameTrigger as FieldPath<FormAdditionalInformation>,
  });

  const message = useErrorMessageTranslation(error?.message);
  const referralInformation = useWatch({
    control,
    name: 'referralInformation',
  });
  const isProcessing = useCardOpenProcessing(flowId);
  const memo = useRef(field.value);

  const setFieldError = useCallback(
    (errorMessage: string) => {
      setValue?.('referralInformation', null);
      setError?.(field.name as keyof FormAdditionalInformation, {message: errorMessage});
    },
    [setValue, setError, field.name],
  );

  const onBlur = useCallback(async () => {
    field.onBlur();
    const referralCode = field.value as string;
    if (referralCode.length > 0 && memo.current !== referralCode) {
      const result = await getReferralInformation(referralCode);
      memo.current = referralCode;

      if (isLeft(result)) {
        setFieldError(result.error.message);
        return;
      }

      const data = result.data;
      if (data) {
        setValue?.('referralInformation', data);
        return;
      }

      setFieldError(translate('select_card_to_open.referral_code_invalid'));
    }
  }, [field]);

  const renderContent = useCallback(() => {
    if (isProcessing) {
      return <Skeleton isLoading containerStyle={{gap: getSize(16)}} layout={[getFormLayout(), getFormLayout()]} />;
    }

    if (referralInformation) {
      return (
        <Block gap={getSize(16)}>
          <MSBInputBase
            testID="cm.card-open.referral-full-name"
            label={translate('select_card_to_open.referrer')}
            disabled
            value={referralInformation?.fullName ?? ''}
          />
          <MSBInputBase
            testID="cm.card-open.referral-branch"
            label={translate('select_card_to_open.branch_or_transaction_office')}
            disabled
            value={referralInformation?.departmentName ?? ''}
          />
        </Block>
      );
    }

    return null;
  }, [isProcessing, referralInformation]);

  return (
    <>
      <Block testID="cm.card-open.referral-code">
        <MSBInputBase
          testID="cm.card-open.referral-code-input"
          contentLabel={
            <Block marginBottom={getSize(4)}>
              <MSBLabel
                labelName={translate('select_card_to_open.referral_code')}
                optionalInformation={translate('select_card_to_open.optional')}
              />
            </Block>
          }
          placeholder={translate('select_card_to_open.referrer')}
          value={field.value as string}
          onChangeText={field.onChange}
          onBlur={onBlur}
          errorContent={message}
        />
      </Block>
      {renderContent()}
    </>
  );
};

export default FormReferralCode;
