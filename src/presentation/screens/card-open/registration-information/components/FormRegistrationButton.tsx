import FormButton from '@components/form/FormButton';
import {FormAdditionalInformation} from '@entities/form/FormAdditionalInformation';
import {translate} from '@locales';
import {useCardOpenProcessing} from '@presentation/store/CardOpenFlow';
import React from 'react';
import {FormRegistrationButtonProps} from './types';

const FormRegistrationButton = ({control, flowId, onPress}: FormRegistrationButtonProps<FormAdditionalInformation>) => {
  const isProcessing = useCardOpenProcessing(flowId);

  return (
    <FormButton
      testID="cm.card-open.register-information.continueBtn"
      control={control}
      label={translate('select_card_to_open.continue')}
      isProcessing={isProcessing}
      onPress={onPress}
    />
  );
};

export default FormRegistrationButton;
