import {Block} from '@components/block';
import {Text} from '@components/text';
import {BottomSheetView} from '@gorhom/bottom-sheet';
import {translate} from '@locales';
import {hostSharedModule} from 'msb-host-shared-module';
import {getSize, MSBInputBase, MSBLabel, SizeGlobal, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo, useState} from 'react';
import {FieldPath, FieldValues, useController, useWatch} from 'react-hook-form';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {FormSecretQuestionProps} from './types';

export const SecretInformationBottomView = () => {
  const {
    theme: {Typography},
  } = useMSBStyles();
  const {bottom} = useSafeAreaInsets();

  return (
    <BottomSheetView>
      <Block
        paddingHorizontal={SizeGlobal.Size400}
        paddingTop={SizeGlobal.Size400}
        paddingBottom={SizeGlobal.Size400 + bottom}>
        <Text type={Typography?.base_regular}>{translate('select_card_to_open.security_answer_reminder')}</Text>
      </Block>
    </BottomSheetView>
  );
};

const FormSecretQuestion = <T extends FieldValues>({control, nameTrigger, watchName}: FormSecretQuestionProps<T>) => {
  const {
    theme: {Typography, ColorLabelCaption, ColorAlias},
  } = useMSBStyles();

  const {
    field,
    fieldState: {invalid},
  } = useController({
    control,
    name: nameTrigger as FieldPath<T>,
  });
  const hasSecurityQuestion = useWatch({control, name: watchName});

  const [showMask, setShowMask] = useState(true);
  const maskedValue = useMemo(() => {
    if (showMask && (field.value as string).trim().length > 0) {
      return translate('card_open_confirm_info.masked_value');
    }
    return field.value;
  }, [field.value, showMask]);

  const onBlur = useCallback(() => {
    field.onBlur();
    setShowMask(true);
  }, [field.onBlur, setShowMask]);

  const onFocus = useCallback(() => {
    setShowMask(false);
  }, [field.onBlur, setShowMask]);

  const onTooltipPress = useCallback(() => {
    hostSharedModule.d.domainService.showBottomSheet({
      header: translate('select_card_to_open.security_question'),
      children: <SecretInformationBottomView />,
    });
  }, []);

  if (hasSecurityQuestion) {
    return null;
  }

  return (
    <Block gap={getSize(4)}>
      <MSBInputBase
        testID="cm.card-open.security-question-school"
        contentLabel={
          <Block marginBottom={getSize(4)}>
            <MSBLabel
              labelName={translate('select_card_to_open.security_question_school')}
              onTooltipPress={onTooltipPress}
              hasTooltip
            />
          </Block>
        }
        placeholder={translate('select_card_to_open.enter_security_answer')}
        value={maskedValue}
        onChangeText={field.onChange}
        onBlur={onBlur}
        onFocus={onFocus}
      />
      <Text type={Typography?.small_regular} color={invalid ? ColorAlias.TextError : ColorLabelCaption.TextSub}>
        {translate('select_card_to_open.input_alphanumeric_only')}
      </Text>
    </Block>
  );
};

export default FormSecretQuestion;
