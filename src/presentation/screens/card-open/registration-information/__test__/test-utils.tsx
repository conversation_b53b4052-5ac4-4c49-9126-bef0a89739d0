import {translate} from '@locales';
import {fireEvent, screen, within} from '@presentation/__test__/test-utils';
import {formatMoney} from '@utils/StringFormat';
import invalidSourceAccount from 'mocks/service-apis/data-sources/invalid-source-account.json';
import validSourceAccount from 'mocks/service-apis/data-sources/valid-source-account.json';

export const mockValidSourceAccount = async () => {
  const mockSourceAccount = await screen.findByTestId('tm.mock-source-account');
  fireEvent(mockSourceAccount, 'selectAccount', validSourceAccount);

  expect(await within(mockSourceAccount).findByText(validSourceAccount.BBAN)).toBeOnTheScreen();
  expect(await within(mockSourceAccount).findByText(validSourceAccount.bankAlias)).toBeOnTheScreen();
  expect(
    await within(mockSourceAccount).findByText(formatMoney(validSourceAccount.availableBalance)),
  ).toBeOnTheScreen();
};

export const mockInvalidSourceAccount = async () => {
  const mockSourceAccount = await screen.findByTestId('tm.mock-source-account');
  fireEvent(mockSourceAccount, 'selectAccount', invalidSourceAccount);

  expect(
    await within(mockSourceAccount).findByText(
      translate('select_card_to_open.insufficient_minimum_balance', {
        amount: formatMoney(50000),
      }),
    ),
  ).toBeOnTheScreen();
};
