// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`[Card Open] Registration Information Screen should match SecretInformationBottomView snapshot 1`] = `
<View
  style={
    [
      false,
      false,
      undefined,
      undefined,
      [
        {
          "paddingBottom": 16,
        },
        {
          "paddingTop": 16,
        },
        {
          "paddingHorizontal": 16,
        },
      ],
      {},
    ]
  }
>
  <Text
    allowFontScaling={false}
    style={
      [
        {
          "color": "rgb(9,30,66)",
          "fontFamily": "Inter-Regular",
          "fontSize": 16,
          "letterSpacing": -0.4,
          "lineHeight": 24,
        },
        {
          "color": "rgb(9,30,66)",
        },
        [
          [
            [
              undefined,
              [],
            ],
          ],
          {},
        ],
      ]
    }
  >
    i18n:select_card_to_open.security_answer_reminder
  </Text>
</View>
`;
