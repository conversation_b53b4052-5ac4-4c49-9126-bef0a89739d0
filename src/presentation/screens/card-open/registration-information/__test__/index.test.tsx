import {translate} from '@locales';
import {
  changeTextInputValue,
  fireEvent,
  render,
  screen,
  shouldShowBottomSheet,
  userEvent,
  within,
} from '@presentation/__test__/test-utils';
import {RootStackParamList} from '@presentation/navigation/types';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {mockResponseForGetDistricts} from 'mocks/service-apis/get-districts';
import {mockResponseForGetLinkAccount} from 'mocks/service-apis/get-list-link-account';
import {mockResponseForGetProvinces} from 'mocks/service-apis/get-provinces';
import {mockResponseForGetWards} from 'mocks/service-apis/get-wards';
import CardOpenRegistrationInformationWrapper from '..';
import {SecretInformationBottomView} from '../components/FormSecretQuestion';
import {mockInvalidSourceAccount, mockValidSourceAccount} from './test-utils';

jest.mock('../components/FormSourceAccount', () => require('mocks/transfer-module/source-account'));

describe('[Card Open] Registration Information Screen', () => {
  const renderScreen = () => {
    const Stack = createNativeStackNavigator<RootStackParamList>();
    render(
      <NavigationContainer>
        <Stack.Navigator initialRouteName="CardOpenRegistrationInformationScreen" screenOptions={{headerShown: false}}>
          <Stack.Screen
            name="CardOpenRegistrationInformationScreen"
            component={CardOpenRegistrationInformationWrapper}
            options={{headerShown: false}}
            initialParams={{flowId: 'test'}}
          />
        </Stack.Navigator>
      </NavigationContainer>,
    );
  };

  it('should match SecretInformationBottomView snapshot', () => {
    const {toJSON} = render(<SecretInformationBottomView />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('should show error insufficient minimum balance when user selects invalid source account', async () => {
    mockResponseForGetLinkAccount();
    renderScreen();
    await mockInvalidSourceAccount();
  });

  // eslint-disable-next-line jest/no-disabled-tests
  it.skip('should enable submit button when user fill all required fields', async () => {
    mockResponseForGetLinkAccount();
    renderScreen();
    const submitBtn = await screen.findByTestId('cm.card-open.register-information.continueBtn');
    expect(submitBtn).toBeDisabled();

    await mockValidSourceAccount();

    const secretQuestionInput = screen.getByTestId('cm.card-open.security-question-school');
    changeTextInputValue(secretQuestionInput, 'security-question-school');
    expect(secretQuestionInput.props.value).toBe('********************');
    fireEvent(secretQuestionInput, 'focus');
    expect(secretQuestionInput.props.value).toBe('security-question-school');

    const referralCodeContainer = screen.getByTestId('cm.card-open.referral-code');
    const referralCodeInput = screen.getByTestId('cm.card-open.referral-code-input');
    changeTextInputValue(referralCodeInput, 'error');
    expect(
      await within(referralCodeContainer).findByText(translate('select_card_to_open.referral_code_invalid')),
    ).toBeOnTheScreen();
    changeTextInputValue(referralCodeInput, 'valid-code');
    expect((await screen.findByTestId('cm.card-open.referral-full-name')).props.value).toBe('valid-code');
    expect((await screen.findByTestId('cm.card-open.referral-branch')).props.value).toBe('MSB Test 1');

    mockResponseForGetProvinces();
    mockResponseForGetDistricts();
    mockResponseForGetWards();
    const event = userEvent.setup();
    const provinceSelection = screen.getByTestId('cm.card-open.province-input');
    await event.press(provinceSelection);
    const provinceBottomSheet = await shouldShowBottomSheet();
    const haNoiProvinceItem = await within(provinceBottomSheet).findByTestId('cm-list-selection-item-1');
    await event.press(haNoiProvinceItem);

    const districtBottomSheet = await shouldShowBottomSheet();
    const haDongDistrictItem = await within(districtBottomSheet).findByTestId('cm-list-selection-item-268');
    await event.press(haDongDistrictItem);

    const wardBottomSheet = await shouldShowBottomSheet();
    const haCauWardItem = await within(wardBottomSheet).findByTestId('cm-list-selection-item-9556');
    await event.press(haCauWardItem);
    expect(wardBottomSheet).not.toBeOnTheScreen();

    const addressInput = screen.getByTestId('cm.card-open.address-input');
    changeTextInputValue(addressInput, 'enter user address');
    expect(await screen.findByTestId('cm.card-open.register-information.continueBtn')).toBeEnabled();
  });
});
