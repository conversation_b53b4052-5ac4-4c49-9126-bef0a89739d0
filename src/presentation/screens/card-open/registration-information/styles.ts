import {createMSBStyleSheet, getSize} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeGlobal, SizeAlias, ColorStepper}) => ({
  container: {
    flex: 1,
    paddingHorizontal: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size400,
  },
  scrollView: {
    flexGrow: 1,
    backgroundColor: 'transparent',
    paddingTop: getSize(24),
    paddingHorizontal: getSize(16),
    rowGap: SizeAlias.SpacingLarge,
  },
  headerContent: {
    paddingHorizontal: SizeGlobal.Size600,
    backgroundColor: ColorStepper.ColorSurfaceDefault,
    paddingTop: SizeGlobal.Size200,
    paddingBottom: SizeGlobal.Size150,
  },
  accountLoading: {flexDirection: 'column'},
}));
