// useListCardViewModel.ts
import {ApplicationScreenProps, RootStackParamList} from '@app-navigation/types.ts';
import TableViewBottomSheet from '@components/bottom-sheet/card-table-view';
import {FormChooseCardType} from '@entities/form/FormChooseCardType';
import {zodResolver} from '@hookform/resolvers/zod';
import {translate} from '@locales';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useCardOpenFlowActions} from '@store/CardOpenFlow';
import {hostSharedModule} from 'msb-host-shared-module';
import {useCallback} from 'react';
import {useForm} from 'react-hook-form';
import {getDefaultValues, validation} from './form';

const useCardOpenDetailCardViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {
    params: {flowId},
  } = useRoute<ApplicationScreenProps<'CardOpenDetailCardScreen'>['route']>();

  const {getFormState, setFormState} = useCardOpenFlowActions();
  const {control, trigger, getValues} = useForm<FormChooseCardType>({
    mode: 'onChange',
    resolver: zodResolver(validation),
    defaultValues: getDefaultValues(getFormState(flowId)),
    shouldFocusError: false,
  });

  const showTable = useCallback(() => {
    hostSharedModule.d.domainService.showBottomSheet({
      header: translate('select_card_to_open.compare_cards'),
      children: <TableViewBottomSheet />,
    });
  }, []);

  const goToRegistrationInformation = useCallback(() => {
    setFormState(flowId, getValues());
    navigation.navigate('CardOpenRegistrationInformationScreen', {flowId});
  }, [flowId, navigation, getValues, setFormState]);

  return {
    control,
    trigger,
    showTable,
    goToRegistrationInformation,
  };
};

export default useCardOpenDetailCardViewModel;
