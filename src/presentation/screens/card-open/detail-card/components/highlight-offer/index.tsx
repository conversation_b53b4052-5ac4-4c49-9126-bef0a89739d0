import {Block} from '@components/block';
import HorizontalDivider from '@components/line-view/Divider';
import {Text} from '@components/text';
import {translate} from '@locales';
import {ButtonType, getSize, MSBButton, useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo, useState} from 'react';
import {HighlightOfferProps, RenderOfferProps} from './types';

function RenderOffer({offer, index, offset}: RenderOfferProps) {
  const {
    theme: {SizeGlobal, Typography},
  } = useMSBStyles();
  return (
    <Block key={`offer-${index + offset}`} direction="row" gap={SizeGlobal.Size200}>
      <Block
        marginTop={getSize(3)}
        width={getSize(16)}
        height={getSize(16)}
        borderRadius={getSize(8)}
        color={'rgba(235, 237, 240, 1)'}
        justifyContent="center"
        alignItems="center">
        <Text type={Typography?.caption_medium}>{index + offset + 1}</Text>
      </Block>
      <Text flex={1} type={Typography?.small_regular}>
        {offer}
      </Text>
    </Block>
  );
}

const HighlightOffer: React.FC<HighlightOfferProps> = React.memo(({offers}) => {
  const {
    theme: {SizeCard, SizeGlobal},
  } = useMSBStyles();
  const [isOpen, setIsOpen] = useState(false);

  const onToggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const [visualOffers, hiddenOffers] = useMemo(() => {
    return [offers.slice(0, 3), offers.slice(3, offers.length)];
  }, [offers]);
  console.log('isOpem', isOpen);

  return (
    <Block paddingHorizontal={SizeCard.SpacingLeft}>
      <Block paddingVertical={SizeGlobal.Size400} gap={SizeGlobal.Size200}>
        {visualOffers.map((offer, index) => (
          <RenderOffer key={`offer-${index}`} offer={offer} index={index} offset={0} />
        ))}
        {isOpen && (
          <Block>
            {hiddenOffers.map((offer, index) => (
              <RenderOffer
                key={`offer-${index + visualOffers.length}`}
                offer={offer}
                index={index}
                offset={visualOffers.length}
              />
            ))}
          </Block>
        )}
      </Block>
      {offers.length > 3 && (
        <>
          <HorizontalDivider />
          <MSBButton
            testID="cm.card-open.highlight-offer.toggle"
            label={isOpen ? translate('select_card_to_open.collapse') : translate('select_card_to_open.see_more')}
            buttonType={ButtonType.Tertiary}
            onPress={onToggle}
          />
        </>
      )}
    </Block>
  );
});

export default HighlightOffer;
