import {Block} from '@components/block';
import {FormChooseCardType} from '@domain/entities/form/FormChooseCardType';
import {CardInstrument} from '@entities/card/CardInstrument';
import {translate} from '@locales';
import Inform from '@presentation/components/form/Inform';
import {getSize, MSBTouchableBox, TouchableBoxType, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {useController} from 'react-hook-form';
import {styleSheet} from './styles';
import {CardInstrumentProps} from './types';

const FormCardInstrument = React.memo(({control, nameTrigger}: CardInstrumentProps<FormChooseCardType>) => {
  const {
    styles,
    theme: {SizeGlobal},
  } = useMSBStyles(styleSheet);

  const {field} = useController({
    control,
    name: nameTrigger,
  });

  const onPress = useCallback(
    (instrument: CardInstrument) => () => {
      field.onChange(instrument);
    },
    [],
  );

  const activePhysical = field.value === CardInstrument.PHYSICAL;
  return (
    <Block padding={getSize(16)} gap={SizeGlobal.Size300}>
      <Block direction="row">
        <MSBTouchableBox
          testID="cm.card-open.instrument.virtual"
          type={TouchableBoxType.Radio}
          label={translate('select_card_to_open.type_digital')}
          status={!activePhysical}
          style={styles.digital}
          onPress={onPress(CardInstrument.VIRTUAL)}
        />
        <MSBTouchableBox
          testID="cm.card-open.instrument.physical"
          type={TouchableBoxType.Radio}
          label={translate('select_card_to_open.type_physical')}
          status={activePhysical}
          onPress={onPress(CardInstrument.PHYSICAL)}
          style={styles.physical}
        />
      </Block>
      {activePhysical && <Inform content={translate('select_card_to_open.issuing_fee_notice')} />}
    </Block>
  );
});

export default FormCardInstrument;
