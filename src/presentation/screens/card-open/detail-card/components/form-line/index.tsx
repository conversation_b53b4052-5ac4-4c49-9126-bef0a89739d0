import FormLine from '@components/form/FormLine';
import {FormChooseCardType} from '@domain/entities/form/FormChooseCardType';
import {CardInstrument} from '@entities/card/CardInstrument';
import {translate} from '@locales';
import React from 'react';
import {useWatch} from 'react-hook-form';

const FormLineWatch = ({control}: Omit<FormControl<FormChooseCardType>, 'nameTrigger'>) => {
  const cardInstrument = useWatch({control, name: 'instrument'});

  if (cardInstrument === CardInstrument.PHYSICAL) {
    return (
      <FormLine
        left={translate('select_card_to_open.delivery_fee')}
        right={translate('select_card_to_open.delivery_location_note')}
      />
    );
  }
  return null;
};

export default FormLineWatch;
