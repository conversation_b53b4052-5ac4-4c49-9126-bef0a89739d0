import images from '@assets/images';
import {Block} from '@components/block';
import FormLine from '@components/form/FormLine';
import FormSection from '@components/form/FormSection';
import {translate} from '@locales';
import MSBFastImage from '@presentation/components/fast-image';
import {getSize, MSBButton, MSBPage, MSBScrollView, MSBStepper, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import FormCardInstrument from './components/card-instrument';
import FormLineWatch from './components/form-line';
import HighlightOffer from './components/highlight-offer';
import useCardOpenDetailCardViewModel from './hook';
import {styleSheet} from './styles';

const CardOpenDetailCardScreen = () => {
  const {
    styles,
    theme: {ColorHeader, SizeDataView},
  } = useMSBStyles(styleSheet);
  const {control, trigger, showTable, goToRegistrationInformation} = useCardOpenDetailCardViewModel();

  return (
    <MSBPage
      testID="cm.cardOpenDetailCardScreen"
      backgroundColor={ColorHeader.SurfaceLevel1}
      isScrollable={false}
      headerProps={{
        title: translate('select_card_to_open.title'),
        hasBack: true,
        barStyle: true,
        childrenBottomHeader: (
          <View style={styles.headerContent}>
            <MSBStepper
              title={translate('select_card_to_open.select_card')}
              totalSteps={3}
              currentStep={1}
              progress={1}
            />
          </View>
        ),
      }}>
      <MSBScrollView contentContainerStyle={styles.scrollView}>
        <FormSection title="MSB Visa Debit Classic">
          <Block padding={getSize(16)}>
            <MSBFastImage
              style={styles.cardImage}
              source={{
                uri: 'https://minio-uat.msb.com.vn/ibadmin-public/card/debit/visa/classic/debit-mc-classic.png',
              }}
              defaultSource={images.card_default}
              resizeMode={'contain'}
            />
          </Block>
        </FormSection>
        <FormSection title={translate('select_card_to_open.select_card')} onPressInfo={showTable}>
          <FormCardInstrument nameTrigger="instrument" control={control} trigger={trigger} />
        </FormSection>
        <FormSection title={translate('select_card_to_open.fee')}>
          <Block
            padding={getSize(16)}
            gap={SizeDataView.SpacingHorizontal}
            testID="cm.card-open.detail-card.fee-container">
            <FormLine
              left={translate('select_card_to_open.issuing_fee')}
              right={translate('select_card_to_open.free')}
            />
            <FormLine
              left={translate('select_card_to_open.annual_fee')}
              right={translate('select_card_to_open.free')}
            />
            <FormLineWatch control={control} />
          </Block>
        </FormSection>
        <FormSection title={translate('select_card_to_open.highlight_offer')}>
          <HighlightOffer offers={mockOffers} />
        </FormSection>
      </MSBScrollView>
      <Block paddingHorizontal={getSize(16)} paddingTop={getSize(12)}>
        <MSBButton
          label={translate('select_card_to_open.continue')}
          style={styles.button}
          onPress={goToRegistrationInformation}
        />
      </Block>
    </MSBPage>
  );
};

export default CardOpenDetailCardScreen;

const mockOffers = [
  'Giao dịch thuận tiện tại các điểm thanh toán trong nước và nước ngoài',
  'Chạm & thanh toán siêu nhanh khi tích hợp Ví Apple Pay, Google Pay',
  'Biến động tài khoản mọi lúc mọi nơi được gửi SMS tới khách hàng với dịch vụ SMS Banking',
  'Ưu đãi tới 50% tại hơn 300 cửa hàng trong Thế giới ưu đãi JOY',
  'Công nghệ bảo mật hàng đầu thế giới được áp dụng để xác thực cho các giao dịch online - 3D Secure',
];
