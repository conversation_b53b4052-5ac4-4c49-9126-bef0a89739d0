import {CardInstrument} from '@entities/card/CardInstrument';
import {CardOpenFormData} from '@entities/form';
import {FormChooseCardType} from '@entities/form/FormChooseCardType';
import {z} from 'zod';

export const getDefaultValues = (formData: Partial<CardOpenFormData>): FormChooseCardType => {
  return {
    instrument: formData.instrument ?? CardInstrument.VIRTUAL,
  };
};

export const validation = z.object<ZodShape<FormChooseCardType>>({
  instrument: z.custom<CardInstrument>(),
});
