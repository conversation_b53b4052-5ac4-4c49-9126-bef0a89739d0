import {translate} from '@locales';
import {render, screen, shouldShowBottomSheet, userEvent, within} from '@presentation/__test__/test-utils';
import {RootStackParamList} from '@presentation/navigation/types';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {ReactTestInstance} from 'react-test-renderer';
import CardOpenDetailCardScreen from '..';
import FormLineWatch from '../components/form-line';

describe('[Card Open] Detail Card Screen', () => {
  const renderScreen = () => {
    const Stack = createNativeStackNavigator<RootStackParamList>();
    render(
      <NavigationContainer>
        <Stack.Navigator initialRouteName="CardOpenDetailCardScreen" screenOptions={{headerShown: false}}>
          <Stack.Screen
            name="CardOpenDetailCardScreen"
            component={CardOpenDetailCardScreen}
            options={{headerShown: false}}
            initialParams={{flowId: ''}}
          />
        </Stack.Navigator>
      </NavigationContainer>,
    );
  };

  it('should display the card comparison table when user opens bottom sheet', async () => {
    renderScreen();

    const showComparisonBtn = await screen.findByTestId('cm.form-section.tooltip');
    const event = userEvent.setup();
    await event.press(showComparisonBtn);

    await shouldShowBottomSheet();
  });

  it('should display a fee notice when user selects a physical card', async () => {
    renderScreen();

    const event = userEvent.setup();
    await event.press(await screen.findByTestId('cm.card-open.instrument.physical'));

    const informContent = await screen.findByText(translate('select_card_to_open.issuing_fee_notice'));
    expect(informContent).toBeOnTheScreen();

    const feeGroup = screen.getByTestId('cm.card-open.detail-card.fee-container');
    const formFeeLine = feeGroup.children[2] as ReactTestInstance;
    expect(formFeeLine.type).toBe(FormLineWatch);

    expect(formFeeLine.children.length).toBe(1);
    expect(within(feeGroup).getByText(translate('select_card_to_open.delivery_location_note'))).toBeOnTheScreen();

    await event.press(screen.getByTestId('cm.card-open.instrument.virtual'));
    expect(informContent).not.toBeOnTheScreen();
    expect(formFeeLine.children.length).toBe(0);
  });
});
