import {translate} from '@locales';
import {advanceTimersByTime, render, screen, userEvent} from '@presentation/__test__/test-utils';
import React from 'react';
import HighlightOffer from '../components/highlight-offer';

describe('HighlightOffer', () => {
  const mockOffers = ['Offer 1', 'Offer 2', 'Offer 3', 'Offer 4', 'Offer 5'];

  it('renders only 3 offers by default', async () => {
    render(<HighlightOffer offers={mockOffers} />);
    expect(await screen.findByText('Offer 1')).toBeTruthy();
    expect(screen.queryByText('Offer 2')).toBeTruthy();
    expect(screen.queryByText('Offer 3')).toBeTruthy();
    expect(screen.queryByText('Offer 4')).toBeNull();
    expect(screen.queryByText('Offer 5')).toBeNull();
  });

  it('shows more offers after clicking "See More"', async () => {
    render(<HighlightOffer offers={mockOffers} />);
    const seeMoreButton = await screen.findByText(translate('select_card_to_open.see_more'));
    const event = userEvent.setup();
    await event.press(seeMoreButton);
    expect(screen.queryByText('Offer 4')).toBeTruthy();
    expect(screen.queryByText('Offer 5')).toBeTruthy();
    expect(screen.getByText(translate('select_card_to_open.collapse'))).toBeTruthy();
  });

  it('collapses offers after clicking "Collapse"', async () => {
    render(<HighlightOffer offers={mockOffers} />);
    const seeMoreButton = await screen.findByText(translate('select_card_to_open.see_more'));
    const event = userEvent.setup();
    await event.press(seeMoreButton);

    const offer4 = await screen.findByText('Offer 4');
    const offer5 = await screen.findByText('Offer 5');
    expect(offer4).toBeOnTheScreen();
    expect(offer5).toBeOnTheScreen();

    await advanceTimersByTime(1000);
    await event.press(seeMoreButton);

    expect(await screen.findByText(translate('select_card_to_open.see_more'))).toBeOnTheScreen();
    expect(offer4).not.toBeOnTheScreen();
    expect(offer5).not.toBeOnTheScreen();
  });

  it('hides button when offers <= 3', () => {
    const shortOffers = ['Offer A', 'Offer B', 'Offer C'];
    render(<HighlightOffer offers={shortOffers} />);
    expect(screen.queryByText(translate('select_card_to_open.see_more'))).toBeNull();
  });
});
