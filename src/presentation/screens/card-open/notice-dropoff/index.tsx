import CardOpenCardImage from '@components/card-open-card-image';
import {sharedStyleSheet} from '@components/common/styles';
import HorizontalDivider from '@components/line-view/Divider';
import {Text} from '@components/text';
import {mapToCard} from '@data/mappers/card/CardMapper';
import {translate} from '@locales';
import {ButtonType, MSBButton, MSBFolderImage, MSBPage, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {MSBTimelineItem} from './components/time-line-item';
import {MSBTimelineItemType, MSBTimelineStatus} from './components/time-line-item/type';
import {styleSheet} from './styles';

const mock = mapToCard({
  productCode: 'VISA123',
  name: 'MSB MasterCard Debit FCB',
  physicalAvailable: 0,
  virtualAvailable: 0,
  cardVisual: {
    images: [
      {
        imageId: 'CARD_521976_03_FCB_PHYSICAL',
        imageURL: 'https://minio-uat.msb.com.vn/ibadmin-public/card/debit/master/platinum/debit-mc-fcb.png',
        type: 'FRONT',
        version: '1',
      },
    ],
  },
  benefits: [
    {
      description: 'Giao dịch thuận tiện tại các điểm thanh toán trong nước và nước ngoài',
    },
    {
      description: 'Biến động tài khoản mọi lúc mọi nơi được gửi với dịch vụ SMS Banking',
    },
    {
      description: 'Ưu đãi tới 50% tại hơn 300 cửa hàng trong Thế giới ưu đãi JOY',
    },
  ],
});

const CardOpenNoticeDropOffScreen = () => {
  const {
    styles,
    theme: {Typography, ColorHeader},
  } = useMSBStyles(styleSheet);
  const {styles: shareStyles} = useMSBStyles(sharedStyleSheet);
  return (
    <MSBPage
      testID="cm.cardOpenNoticeDropOffScreen"
      isScrollable={false}
      backgroundColor={ColorHeader.SurfaceLevel1}
      headerProps={{
        title: translate('card_open_notice_drop_off.screen_title'),
        hasBack: true,
      }}>
      <View style={styles.scrollView}>
        <View style={[styles.cardInfoContainer, shareStyles.shadow]}>
          <CardOpenCardImage imageWidth={115} cardVisual={mock.cardVisual} />
          <View style={styles.cardInfoView}>
            <Text type={Typography?.base_medium} style={styles.cardName}>
              {mock.name}
            </Text>
            <Text type={Typography?.small_medium} style={styles.cardInstrumente}>
              {mock.instrument}
            </Text>
          </View>
        </View>
        <View style={styles.checkBoxWrap}>
          <Text
            text={translate('card_open_notice_drop_off.incomplete_flow_warning')}
            type={Typography?.base_semiBold}
          />
          <HorizontalDivider />
          <Text text={translate('card_open_notice_drop_off.final_step_description')} type={Typography?.base_regular} />
          <View>
            <MSBTimelineItem
              type={MSBTimelineItemType.Fist}
              status={MSBTimelineStatus.Completed}
              data={{
                imageName: 'tone-id-card',
                folder: MSBFolderImage.ICON_SVG,
                title: translate('card_open_notice_drop_off.timeline_step_1'),
              }}
            />
            <MSBTimelineItem
              type={MSBTimelineItemType.None}
              status={MSBTimelineStatus.Completed}
              data={{
                imageName: 'tone-id-card',
                folder: MSBFolderImage.ICON_SVG,
                title: translate('card_open_notice_drop_off.timeline_step_2'),
              }}
            />
            <MSBTimelineItem
              type={MSBTimelineItemType.Last}
              status={MSBTimelineStatus.Current}
              data={{
                imageName: 'tone-support-manage',
                folder: MSBFolderImage.ICON_SVG,
                title: translate('card_open_notice_drop_off.timeline_step_3'),
              }}
            />
          </View>
        </View>
      </View>
      <View style={styles.buttons}>
        <MSBButton
          style={styles.flex}
          buttonType={ButtonType.Secondary}
          label={translate('card_open_notice_drop_off.button_exit')}
        />
        <MSBButton
          // onPress={hostSharedModule.d.domainService.hideBottomSheet}
          style={styles.flex}
          buttonType={ButtonType.Primary}
          label={translate('card_open_notice_drop_off.button_continue')}
        />
      </View>
    </MSBPage>
  );
};

export default CardOpenNoticeDropOffScreen;
