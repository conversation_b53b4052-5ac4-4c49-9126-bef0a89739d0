import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorCard, SizeAlias, ColorDataView, SizeGlobal, ColorStepper}) => ({
  container: {
    flex: 1,
    paddingHorizontal: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size400,
  },
  scrollView: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingTop: SizeGlobal.Size400,
    rowGap: SizeGlobal.Size400,
    marginHorizontal: SizeGlobal.Size400,
  },
  childrenBottomHeader: {
    paddingHorizontal: SizeGlobal.Size600,
    paddingTop: SizeGlobal.Size200,
    backgroundColor: ColorStepper.ColorSurfaceDefault,
    paddingBottom: SizeGlobal.Size150,
  },
  cardInfoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 1)',
    borderRadius: SizeAlias.Radius3,
    padding: SizeGlobal.Size400,
    flexDirection: 'row',
    alignItems: 'center',
    gap: SizeGlobal.Size300,
  },
  checkBoxWrap: {
    backgroundColor: ColorCard.SurfaceDefault,
    padding: SizeGlobal.Size400,
    borderRadius: SizeAlias.Radius3,
    rowGap: SizeGlobal.Size400,
  },
  cardInfoView: {
    flex: 1,
    justifyContent: 'center',
    rowGap: SizeGlobal.Size100,
  },
  cardInstrumente: {
    color: ColorDataView.TextSub,
  },
  cardName: {
    color: ColorDataView.TextMain,
  },
  buttons: {
    flexDirection: 'row',
    columnGap: SizeGlobal.Size400,
    paddingHorizontal: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size300,
    alignItems: 'center',
  },
  flex: {
    flex: 1,
  },
}));
