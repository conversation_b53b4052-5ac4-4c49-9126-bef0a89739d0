import {ColorAlias, ColorLabelCaption, MSBFastImage, MSBFolderImage} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';

import {Text} from '@components/text';
import {useMSBStyles} from 'msb-shared-component';
import {makeStyles} from './styles';
import {MSBTimelineItemType, MSBTimelineProps, MSBTimelineStatus} from './type';

export const MSBTimelineItem = ({status, type, data, previosStatus}: MSBTimelineProps) => {
  const {styles} = useMSBStyles(makeStyles);
  return (
    <View style={styles.stepContainer}>
      {type !== MSBTimelineItemType.Fist && (
        <View
          style={[
            styles.lineStyle,
            {
              backgroundColor:
                status !== MSBTimelineStatus.None || previosStatus !== MSBTimelineStatus.None
                  ? ColorLabelCaption.IconSuccess
                  : ColorAlias.BorderDisable,
            },
          ]}
        />
      )}
      <View style={styles.stepItemContainer}>
        {status === MSBTimelineStatus.Completed ? (
          <MSBFastImage folder={MSBFolderImage.ICON_SVG} nameImage={'check-circle'} style={styles.iconCheckCricle} />
        ) : (
          <MSBFastImage folder={data?.folder} nameImage={data?.imageName} style={styles.stepItemImage} />
        )}

        <Text
          style={[
            styles.stepItemTitle,
            {color: status === MSBTimelineStatus.Completed ? ColorLabelCaption.IconSuccess : ColorAlias.TextPrimary},
          ]}>
          {data?.title}
        </Text>
      </View>
      {type !== MSBTimelineItemType.Last && (
        <View
          style={[
            styles.lineStyle,
            {
              backgroundColor:
                status === MSBTimelineStatus.Completed ? ColorLabelCaption.IconSuccess : ColorAlias.BorderDisable,
            },
          ]}
        />
      )}
    </View>
  );
};
