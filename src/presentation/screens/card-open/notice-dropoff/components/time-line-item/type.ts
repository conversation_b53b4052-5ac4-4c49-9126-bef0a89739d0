import {MSBFolderImage} from 'msb-shared-component';

export type MSBTimelineProps = {
  status?: MSBTimelineStatus;
  type: MSBTimelineItemType;
  data?: MSBTimelineData;
  previosStatus?: MSBTimelineStatus;
};

export enum MSBTimelineStatus {
  Current = 'current',
  Completed = 'completed',
  None = 'none',
}

export enum MSBTimelineItemType {
  Fist = 'first',
  Last = 'last',
  None = 'none',
}

export interface MSBTimelineData {
  imageName?: string;
  folder?: MSBFolderImage;
  title?: string;
}
