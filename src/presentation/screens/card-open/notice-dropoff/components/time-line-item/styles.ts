import {ColorAlias, createMSBStyleSheet} from 'msb-shared-component';

export const makeStyles = createMSBStyleSheet(({SizeAlias, SizeItem, SizeGlobal, Typography}) => {
  return {
    lineStyle: {
      backgroundColor: ColorAlias.BorderDisable,
      height: SizeAlias.SpacingMedium,
      marginHorizontal: SizeAlias.SpacingSmall,
      width: SizeGlobal.Size50,
    },
    stepContainer: {flexDirection: 'column'},
    stepItemContainer: {alignItems: 'center', flexDirection: 'row'},
    stepItemImage: {height: SizeItem.ItemDefault, width: SizeItem.ItemDefault},
    stepItemTitle: {...Typography?.base_medium, paddingLeft: SizeAlias.Spacing2xSmall},
    iconCheckCricle: {width: SizeGlobal.Size800, height: SizeGlobal.Size800},
  };
});
