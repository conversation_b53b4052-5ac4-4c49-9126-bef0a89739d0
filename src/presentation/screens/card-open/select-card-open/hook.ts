import {showCardOpenDontMissDealBottomSheet} from '@components/bottom-sheet/do-not-miss-deal';
import {mapToCards} from '@data/mappers/card/CardMapper';
import {Card} from '@domain/entities/card/Card';
import {RootStackParamList} from '@presentation/navigation/types';
import {useCardOpenFlowActions} from '@presentation/store/CardOpenFlow';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {uniqueId} from 'lodash';
import mockGetAllowedCardProductsResponse from 'mocks/service-apis/data-sources/list-allowed-card-products.json';
import {hostSharedModule} from 'msb-host-shared-module';
import {useCallback} from 'react';

const useSelectCreateCard = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {setFormState} = useCardOpenFlowActions();

  const goToEntryPoint = useCallback(() => {
    navigation.goBack();
    hostSharedModule.d.domainService.hideBottomSheet();
  }, [navigation.goBack]);

  const onGoBack = useCallback(() => {
    showCardOpenDontMissDealBottomSheet(goToEntryPoint);
  }, [goToEntryPoint]);

  const onSelectCard = useCallback(
    (selected: Card) => {
      const flowId = uniqueId();
      setFormState(flowId, {
        card: selected,
      });
      navigation.navigate('CardOpenDetailCardScreen', {
        flowId,
      });
    },
    [navigation, setFormState],
  );

  return {list: mapToCards(mockGetAllowedCardProductsResponse.cards), onGoBack, onSelectCard};
};

export default useSelectCreateCard;
