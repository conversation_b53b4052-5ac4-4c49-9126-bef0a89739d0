import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeGlobal, ColorTag, SizeTag}) => ({
  container: {
    padding: SizeGlobal.Size400,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    columnGap: SizeGlobal.Size300,
  },
  content: {
    flex: 1,
    rowGap: SizeGlobal.Size200,
  },
  tagView: {
    flexDirection: 'row',
    backgroundColor: ColorTag.SurfaceBlue,
    paddingHorizontal: SizeTag.SpacingHorizontal,
    paddingVertical: SizeTag.SpacingVertical,
    borderRadius: SizeTag.BorderRadius,
    alignSelf: 'flex-start',
  },
  tag: {
    color: ColorTag.TextBlue,
    height: 'auto',
  },
}));
