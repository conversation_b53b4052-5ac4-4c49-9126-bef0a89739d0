import CardOpenCardImage from '@components/card-open-card-image';
import {Text} from '@components/text';
import {Card} from '@entities/card/Card';
import {translate} from '@locales';
import {MSBIcon, MSBIcons, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';

interface Props {
  data: Card;
  onPress?: () => void;
  isPopular?: boolean;
}
const CardToSelectItem: React.FC<Props> = ({data, onPress, isPopular}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  return (
    <MSBTouchable
      testID={`cm.bottom-sheet.cards-to-select.${data.productCode}`}
      onPress={onPress}
      style={styles.container}>
      <CardOpenCardImage cardVisual={data.cardVisual} />
      <View style={styles.content}>
        <Text type={Typography?.base_semiBold} style={{}}>
          {data.name}
        </Text>
        {!!isPopular && (
          <View style={styles.tagView}>
            <Text style={styles.tag}>⭐️ {translate('select_card_to_open.to_love')}</Text>
          </View>
        )}
      </View>
      <MSBIcon icon={MSBIcons.IconRight} />
    </MSBTouchable>
  );
};

export default CardToSelectItem;
