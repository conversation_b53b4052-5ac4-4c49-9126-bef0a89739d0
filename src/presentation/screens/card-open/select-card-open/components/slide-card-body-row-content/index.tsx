import {Text} from '@presentation/components/text';
import {useMSBStyles} from 'msb-shared-component';
import {View} from 'react-native';
import {styleSheet} from './styles';

const CardBodyViewContentRow: React.FC<{title?: string}> = () => {
  const {styles} = useMSBStyles(styleSheet);
  return (
    <View style={styles.container}>
      <View style={styles.dot} />
      <Text style={styles.title}>Giao dịch thuận tiện tại các điểm thanh toán trong nước và nước ngoài</Text>
    </View>
  );
};

export default CardBodyViewContentRow;
