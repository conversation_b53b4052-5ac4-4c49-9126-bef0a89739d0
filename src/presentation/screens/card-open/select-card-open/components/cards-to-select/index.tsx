import {Card} from '@entities/card/Card';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import HorizontalDivider from '@presentation/components/line-view/Divider';
import {hostSharedModule} from 'msb-host-shared-module';
import React from 'react';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import CardToSelectItem from '../card-to-select-item';

interface Props {
  cards: Card[];
  onSelectCard?: (card: Card) => void;
}

const CardsToSelect: React.FC<Props> = ({cards, onSelectCard}) => {
  const {bottom} = useSafeAreaInsets();

  return (
    <BottomSheetFlatList
      testID={'cm.bottom-sheet.cards-to-select'}
      data={cards}
      contentContainerStyle={[{paddingBottom: bottom + 16}]}
      keyExtractor={item => item.productCode}
      renderItem={(data: {item: Card; index: number}) => {
        return (
          <CardToSelectItem isPopular={data.index === 0} data={data.item} onPress={() => onSelectCard?.(data.item)} />
        );
      }}
      ItemSeparatorComponent={HorizontalDivider}
    />
  );
};

export const showCardsToSelectBottomSheet = (header: string, cards: Card[], onSelectCard?: (card: Card) => void) => {
  hostSharedModule.d.domainService.showBottomSheet({
    header,
    children: <CardsToSelect cards={cards} onSelectCard={onSelectCard} />,
  });
};
