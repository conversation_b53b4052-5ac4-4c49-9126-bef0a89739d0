import CardOpenCardImage from '@components/card-open-card-image/index.tsx';
import {sharedStyleSheet} from '@components/common/styles.ts';
import HorizontalDivider from '@components/line-view/Divider.tsx';
import {Text} from '@components/text/index.tsx';
import {translate} from '@locales';
import {MSBButton, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import CardBodyViewContentRow from '../slide-card-body-row-content/index.tsx';
import {styleSheet} from './styles.tsx';
import {CardBodyViewProps} from './type.ts';

const CardBodyView: React.FC<CardBodyViewProps> = props => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  return (
    <MSBTouchable onPress={props.onPress} activeOpacity={1}>
      <View style={[styles.cardContainer, sharedStyles.shadowSecondary]}>
        <View style={styles.imageContainer}>
          <CardOpenCardImage cardVisual={props.item.cardVisual} />
          <Text type={Typography?.base_semiBold} style={styles.cardName}>
            {props.item.name}
          </Text>
        </View>
        <View style={styles.content}>
          <CardBodyViewContentRow />
          <CardBodyViewContentRow />
          <CardBodyViewContentRow />
        </View>
        <View style={styles.horizontalLine}>
          <HorizontalDivider />
        </View>
        <View style={styles.buttons}>
          <View style={styles.tagView}>
            <Text style={styles.tag}>⭐️ {translate('select_card_to_open.to_love')}</Text>
          </View>
          <MSBButton
            onPress={props.onPress}
            testID={`cm.select-card-open.slide-card-body.${props.item?.id}`}
            buttonSize="Small"
            label={translate('select_card_to_open.register')}
            buttonType="Secondary"
          />
        </View>
      </View>
    </MSBTouchable>
  );
};

export default CardBodyView;
