import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeTag, SizeGlobal, ColorGlobal, SizeAlias, ColorTag}) => ({
  cardContainer: {
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeAlias.Radius3,
    borderWidth: 1,
    borderColor: 'rgba(228, 228, 228, 1)',
    width: '100%',
    rowGap: 16,
    paddingVertical: 16,
  },
  imageContainer: {
    borderRadius: 8,
    paddingHorizontal: 16,
    overflow: 'hidden',
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    gap: SizeGlobal.Size300,
  },
  cardName: {
    flex: 1,
  },
  content: {
    rowGap: SizeGlobal.Size200,
    paddingHorizontal: 12,
  },
  horizontalLine: {
    paddingHorizontal: SizeGlobal.Size400,
  },
  buttons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SizeGlobal.Size400,
  },
  tagView: {
    flex: 0,
    backgroundColor: ColorTag.SurfaceBlue,
    paddingHorizontal: SizeTag.SpacingHorizontal,
    paddingVertical: SizeTag.SpacingVertical,
    borderRadius: SizeTag.BorderRadius,
  },
  tag: {
    color: ColorTag.TextBlue,
    height: 'auto',
  },
}));
