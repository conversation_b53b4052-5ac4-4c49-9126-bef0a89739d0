import {Block} from '@components/block';
import CustomCarouselDots from '@components/dot-carousel';
import {DecreasingDot} from '@components/dot-carousel/type';
import {Card} from '@entities/card/Card.ts';
import {sharedStyleSheet} from '@presentation/components/common/styles.ts';
import {isJest} from '@utils/PlatformChecker';
import {useMSBStyles} from 'msb-shared-component';
import React, {useCallback, useMemo, useRef} from 'react';
import {FlatList, View} from 'react-native';
import Animated, {useAnimatedScrollHandler, useSharedValue} from 'react-native-reanimated';
import {ITEM_GAP, ITEM_WIDTH} from '../../constants.ts';
import CardSliderHeader from '../card-slider-header/index.tsx';
import {showCardsToSelectBottomSheet} from '../cards-to-select/index.tsx';
import CardBodyView from '../slide-card-body/index.tsx';
import {styleSheet} from './styles.ts';
import {CardSliderProps} from './type.ts';

const WrapperFlatList = isJest() ? FlatList : Animated.FlatList;

export const CardSlider: React.FC<CardSliderProps> = props => {
  const {
    styles,
    theme: {ColorCarousel, ColorGlobal},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  const progress = useSharedValue(0);
  const activeIndex = useSharedValue(0);
  const flatListRef = useRef<Animated.FlatList<any>>(null);
  const decreasingDots: DecreasingDot[] = useMemo(
    () => [
      {
        config: {
          color: ColorCarousel.DotDefault,
          margin: 4,
          opacity: 0.5,
          height: 8,
          width: 8,
          borderRadius: 8,
        },
        quantity: 1,
      },
    ],
    [],
  );

  let renderItem = useCallback(
    (item: Card, _: number) => {
      const onPress = () => props.onSelectCard?.(item);
      return (
        <Block width={ITEM_WIDTH} paddingHorizontal={ITEM_GAP / 2}>
          <CardBodyView item={item} onPress={onPress} />
        </Block>
      );
    },
    [props.onSelectCard],
  );

  const snapOffsets = useMemo(() => props.itemList.map((_, index) => index * ITEM_WIDTH), [props.itemList]);

  /**
   * Allows skipping the measurement of dynamic content if you know the size (height or width) of items ahead of time.
   */
  const getItemLayout: (
    data: Nullish<ArrayLike<Card>>,
    index: number,
  ) => {length: number; offset: number; index: number} = useCallback(
    (_, index) => ({
      length: ITEM_WIDTH,
      offset: ITEM_WIDTH * index,
      index,
    }),
    [],
  );

  const scrollHandler = useAnimatedScrollHandler(event => {
    progress.value = event.contentOffset.x / ITEM_WIDTH;
    activeIndex.value = Math.round(progress.value);
  });

  const onPressViewAll = useCallback(() => {
    showCardsToSelectBottomSheet(props.title, props.itemList, props.onSelectCard);
  }, [props.title, props.itemList, props.onSelectCard]);

  if (!props.itemList?.length) {
    return <View />;
  }

  return (
    <View style={[styles.carouselContainer, sharedStyles.shadow]}>
      <CardSliderHeader title={props.title} onPress={onPressViewAll} />
      <WrapperFlatList
        testID={'cm.select-card-open.slider'}
        ref={flatListRef}
        data={props.itemList}
        horizontal
        snapToOffsets={snapOffsets}
        bounces={false}
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        decelerationRate={'fast'}
        scrollEventThrottle={16}
        windowSize={11}
        initialNumToRender={11}
        maxToRenderPerBatch={11}
        keyExtractor={item => `card-slider-${item.productCode}`}
        onScroll={scrollHandler}
        getItemLayout={getItemLayout}
        renderItem={({index, item}) => renderItem(item, index)}
        contentContainerStyle={styles.contentContainer}
        style={styles.carousel}
      />

      <Block middle>
        <CustomCarouselDots
          length={props.itemList.length}
          currentIndex={activeIndex}
          progress={progress}
          maxIndicators={6}
          decreasingDots={decreasingDots}
          inactiveIndicatorConfig={styles.inactiveIndicatorConfig}
          activeIndicatorConfig={styles.activeIndicatorConfig}
          scrollableDotsConfig={{
            setIndex: activeIndex,
            onNewIndex: newIndex => {
              flatListRef?.current?.scrollToOffset?.({
                offset: newIndex * ITEM_WIDTH,
                animated: true,
              });
            },
            container: styles.scrollableDotsContainer,
            containerBackgroundColor: ColorGlobal.NeutralWhite,
          }}
        />
      </Block>
    </View>
  );
};
