import {ColorCard, createMSBStyleSheet} from 'msb-shared-component';
import {ITEM_WIDTH, SCREEN_WIDTH} from '../../constants';

export const styleSheet = createMSBStyleSheet(({ColorCarousel, SizeGlobal}) => ({
  contentContainer: {
    paddingHorizontal: (SCREEN_WIDTH - SizeGlobal.Size400 * 4 - ITEM_WIDTH) / 2,
    paddingBottom: 4,
  },
  carouselContainer: {
    flexDirection: 'column',
    width: SCREEN_WIDTH - SizeGlobal.Size400 * 2,
    borderRadius: 16,
    marginHorizontal: 16,

    paddingBottom: SizeGlobal.Size200,
    backgroundColor: ColorCard.SurfaceDefault,
  },
  carousel: {},
  inactiveIndicatorConfig: {
    color: ColorCarousel.DotDefault,
    margin: 4,
    opacity: 0.5,
    height: 8,
    width: 8,
    borderRadius: 8,
  },
  activeIndicatorConfig: {
    color: ColorCarousel.DotSelect,
    margin: 4,
    opacity: 1,
    height: 8,
    width: 24,
    borderRadius: 8,
  },
  scrollableDotsContainer: {
    alignItems: 'center',
    borderRadius: 12,
    height: 24,
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
}));
