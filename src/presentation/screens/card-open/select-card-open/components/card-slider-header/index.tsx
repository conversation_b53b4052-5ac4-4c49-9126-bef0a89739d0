import {Text} from '@components/text';
import {MSBIcon, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';

interface Props {
  title: string;
  onPress?(): void;
}

const CardSliderHeader: React.FC<Props> = ({title, onPress}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  return (
    <View style={styles.container}>
      <Text type={Typography?.base_semiBold} style={styles.title}>
        {title}
      </Text>
      <MSBTouchable testID={'cm.select-card-open.slider.header'} style={styles.iconRightView} onPress={onPress}>
        <>
          <Text type={Typography?.small_medium} style={styles.txtAll}>
            Xem tất cả
          </Text>
          <View style={styles.iconRight}>
            <MSBIcon icon="right" disable />
          </View>
        </>
      </MSBTouchable>
    </View>
  );
};

export default CardSliderHeader;
