import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorButton, SizeGlobal}) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  title: {
    flex: 1,
  },
  iconRightView: {
    columnGap: SizeGlobal.Size100,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconRight: {
    width: 20,
    height: 20,
  },
  txtAll: {
    color: ColorButton.TextTertiaryDefault,
  },
}));
