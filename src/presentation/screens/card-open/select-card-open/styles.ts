import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeGlobal, ColorStepper}) => ({
  container: {
    flex: 1,
    paddingHorizontal: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size400,
  },
  scrollView: {
    flexGrow: 1,
    backgroundColor: 'transparent',
    paddingTop: SizeGlobal.Size400,
    rowGap: SizeGlobal.Size400,
  },
  childrenBottomHeader: {
    paddingHorizontal: SizeGlobal.Size600,
    backgroundColor: ColorStepper.ColorSurfaceDefault,
    paddingTop: SizeGlobal.Size200,
    paddingBottom: SizeGlobal.Size150,
  },
}));
