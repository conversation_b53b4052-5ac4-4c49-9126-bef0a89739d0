import {translate} from '@locales';
import {render, screen, userEvent, within} from '@presentation/__test__/test-utils';
import {RootStackParamList} from '@presentation/navigation/types';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  BOTTOM_SHEET_CONTAINER_TEST_ID,
  BOTTOM_SHEET_FOOTER_CLOSE_BTN_TEST_ID,
  BOTTOM_SHEET_FOOTER_CONFIRM_BTN_TEST_ID,
} from 'mocks/bottom-sheet/MSBBottomSheet';
import {mockGetAllowedCardProductsResponse} from 'mocks/service-apis/get-allowed-card-products';
import CardOpenSelectCardScreen from '..';

const mockGoBack = jest.fn();
jest.mock('@react-navigation/native', () => {
  return {
    ...jest.requireActual('@react-navigation/native'),
    useNavigation: () => ({
      goBack: mockGoBack,
    }),
  };
});

describe('Do not miss deal bottom sheet', () => {
  const showCardOpenDontMissDealBottomSheet = async () => {
    const Stack = createNativeStackNavigator<RootStackParamList>();
    render(
      <NavigationContainer>
        <Stack.Navigator initialRouteName="CardOpenSelectCardScreen" screenOptions={{headerShown: false}}>
          <Stack.Screen
            name="CardOpenSelectCardScreen"
            component={CardOpenSelectCardScreen}
            options={{headerShown: false}}
          />
        </Stack.Navigator>
      </NavigationContainer>,
    );

    const event = userEvent.setup();
    const btnBack = await screen.findByTestId('action-back');
    await event.press(btnBack);

    const dealBottomSheet = await screen.findByTestId(BOTTOM_SHEET_CONTAINER_TEST_ID);
    expect(dealBottomSheet).toBeOnTheScreen();
    expect(within(dealBottomSheet).getByText(translate('select_card_to_open.dont_miss_deal_title'))).toBeOnTheScreen();
    expect(within(dealBottomSheet).getByText(translate('select_card_to_open.benefit_1'))).toBeOnTheScreen();
    expect(within(dealBottomSheet).getByText(translate('select_card_to_open.benefit_2'))).toBeOnTheScreen();
    expect(within(dealBottomSheet).getByText(translate('select_card_to_open.benefit_3'))).toBeOnTheScreen();
  };

  it('should close the bottom sheet and return to the previous screen when user clicks the close button', async () => {
    await showCardOpenDontMissDealBottomSheet();

    const dealBottomSheet = screen.getByTestId(BOTTOM_SHEET_CONTAINER_TEST_ID);
    const bsCloseBtn = within(dealBottomSheet).getByTestId(BOTTOM_SHEET_FOOTER_CLOSE_BTN_TEST_ID);
    const event = userEvent.setup();
    await event.press(bsCloseBtn);
    expect(mockGoBack).toHaveBeenCalledTimes(1);
  });

  it('should close the bottom sheet and stay on the current screen when user clicks the confirm button', async () => {
    await showCardOpenDontMissDealBottomSheet();

    const dealBottomSheet = screen.getByTestId(BOTTOM_SHEET_CONTAINER_TEST_ID);
    const bsConfirmBtn = within(dealBottomSheet).getByTestId(BOTTOM_SHEET_FOOTER_CONFIRM_BTN_TEST_ID);
    const event = userEvent.setup();
    await event.press(bsConfirmBtn);
    expect(dealBottomSheet).not.toBeOnTheScreen();
  });
});

describe('List card product with bottom sheet', () => {
  it('should show the list of card products in bottom sheet when user presses view all in card section', async () => {
    const Stack = createNativeStackNavigator<RootStackParamList>();
    render(
      <NavigationContainer>
        <Stack.Navigator initialRouteName="CardOpenSelectCardScreen" screenOptions={{headerShown: false}}>
          <Stack.Screen
            name="CardOpenSelectCardScreen"
            component={CardOpenSelectCardScreen}
            options={{headerShown: false}}
          />
        </Stack.Navigator>
      </NavigationContainer>,
    );

    const viewAllBtn = await screen.findByTestId('cm.select-card-open.slider.header');
    const event = userEvent.setup();
    await event.press(viewAllBtn);

    const listCardInBottomSheet = await screen.findByTestId('cm.bottom-sheet.cards-to-select');
    expect(listCardInBottomSheet.props.data.length).toBe(mockGetAllowedCardProductsResponse.totalRecord);
  });
});
