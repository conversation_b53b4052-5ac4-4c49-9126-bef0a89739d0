import {translate} from '@locales';
import {MSBPage, MSBStepper, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {CardSlider} from './components/card-slider';
import useSelectCreateCard from './hook';
import {styleSheet} from './styles';

const CardOpenSelectCardScreen = () => {
  const {styles, theme} = useMSBStyles(styleSheet);
  const {list, onGoBack, onSelectCard} = useSelectCreateCard();

  return (
    <MSBPage
      testID="cm.card-open.select-card"
      backgroundColor={theme.ColorHeader.SurfaceLevel1}
      headerProps={{
        onGoBack,
        title: translate('select_card_to_open.title'),
        hasBack: true,
        barStyle: true,
        childrenBottomHeader: (
          <View style={styles.childrenBottomHeader}>
            <MSBStepper
              title={translate('select_card_to_open.select_card')}
              totalSteps={3}
              currentStep={1}
              progress={0.5}
            />
          </View>
        ),
      }}>
      <View style={styles.scrollView}>
        <CardSlider title="Thẻ ghi nợ" itemList={list} onSelectCard={onSelectCard} />
      </View>
    </MSBPage>
  );
};

export default CardOpenSelectCardScreen;
