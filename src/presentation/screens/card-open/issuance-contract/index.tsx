import {translate} from '@locales';
import {sharedStyleSheet} from '@presentation/components/common/styles';
import {
  ButtonSize,
  MSBButton,
  MSBIcon,
  MSBIconSize,
  MSBPage,
  MSBTextBase,
  MSBTouchableBox,
  TouchableBoxType,
  useMSBStyles,
} from 'msb-shared-component';
import React, {useRef, useState} from 'react';
import {TouchableWithoutFeedback, View} from 'react-native';
import Pdf from 'react-native-pdf';
import userCardOpenIssuanceContract from './hook';
import {styleSheet} from './styles';

const CardOpenIssuanceContractScreen = () => {
  const {} = userCardOpenIssuanceContract();

  const {styles, theme} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const [scrollToBottom, setScrollToBottom] = useState(false);
  const [checkBoxStateReadIllegalBehavior, setCheckBoxStateReadIllegalBehavior] = useState(true);
  const [numberOfPages, setNumberOfPages] = useState(0);
  const [isReadIllegalBehavior, setIsReadIllegalBehavior] = useState(false);
  const [loadComplete, setLoadComplete] = useState(false);

  const pdfRef = useRef<any>(null);

  const toastComponent = !scrollToBottom ? (
    <TouchableWithoutFeedback
      onPress={() => {
        setScrollToBottom(true);
        pdfRef.current.setPage(numberOfPages);
      }}>
      <View style={styles.toastContainer}>
        <MSBTextBase
          content={translate('card_open_issuance_contract.toast_read_prompt')}
          type={theme.Typography?.base_medium}
          style={{
            color: theme.ColorToast.TextDefault,
          }}
        />
        <MSBIcon icon="down-straight" iconSize={MSBIconSize.SIZE_20} />
      </View>
    </TouchableWithoutFeedback>
  ) : null;

  return (
    <MSBPage
      testID="cm.cardOpenIssuanceContractScreen"
      isScrollable={false}
      headerProps={{
        title: translate('card_open_issuance_contract.screen_title'),
        hasBack: true,
      }}>
      <View style={[styles.topView, sharedStyles.shadow]} pointerEvents="box-none">
        <MSBTextBase
          content={translate('card_open_issuance_contract.contract_title')}
          type={theme.Typography?.title_medium}
          style={styles.title}
        />
        <View style={styles.container}>
          <Pdf
            ref={pdfRef}
            source={{
              uri: 'https://www.aeee.in/wp-content/uploads/2020/08/Sample-pdf.pdf',
              cacheFileName: 'true',
            }}
            style={styles.container}
            enableDoubleTapZoom={false}
            enableAnnotationRendering={false}
            enableAntialiasing={false}
            onPageChanged={(page, total) => {
              setLoadComplete(true);
              setNumberOfPages(total);
              if (page === total) {
                setScrollToBottom(true);
                setCheckBoxStateReadIllegalBehavior(false);
              }
            }}
            onError={error => console.log('PDF Error:', error)}
            trustAllCerts={false}
          />
          {loadComplete && toastComponent}
        </View>
        <View style={styles.shadowView} />
        <MSBTouchableBox
          label={translate('card_open_issuance_contract.checkbox_label')}
          style={styles.checkBoxBehavior}
          onPress={() => {
            setIsReadIllegalBehavior(v => !v);
          }}
          disabled={checkBoxStateReadIllegalBehavior}
          type={TouchableBoxType.CheckBox}
          // testID={'obd.termAndConditionScreen.rule1Cb'}
        />
      </View>

      <MSBButton
        // style={commonStyles.button}
        buttonSize={ButtonSize.Medium}
        label={translate('card_open_issuance_contract.button_continue')}
        disabled={!isReadIllegalBehavior}
        style={styles.btnConfirm}
        // onPress={confirmationTnc}
        // testID={'obd.termAndConditionScreen.continueBtn'}
      />
    </MSBPage>
  );
};

export default CardOpenIssuanceContractScreen;
