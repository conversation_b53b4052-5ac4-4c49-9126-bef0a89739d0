import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(
  ({ColorAlias, SizeAlias, ColorGlobal, ColorToast, SizeGlobal, SizeToast}) => {
    return {
      checkBox: {
        marginRight: SizeAlias.SpacingSmall,
        paddingHorizontal: SizeAlias.SpacingSmall,
        paddingVertical: SizeAlias.SpacingSmall,
      },
      checkBoxBehavior: {
        alignItems: 'flex-start',
        marginRight: SizeAlias.SpacingSmall,
        paddingHorizontal: SizeAlias.SpacingSmall,
        paddingVertical: SizeAlias.SpacingSmall,
      },
      confirmButton: {
        paddingBottom: SizeAlias.SpacingSmall,
        paddingHorizontal: SizeAlias.SpacingSmall,
      },
      container: {
        flex: 1,
      },
      title: {
        borderTopLeftRadius: SizeAlias.Radius4,
        borderTopRightRadius: SizeAlias.Radius4,
        paddingHorizontal: SizeAlias.SpacingSmall,
        paddingVertical: SizeAlias.SpacingSmall,
        backgroundColor: ColorAlias.BackgroundPrimary,
      },
      separator: {
        backgroundColor: ColorGlobal.Neutral100,
        height: 1,
      },
      toastContainer: {
        alignItems: 'center',
        alignSelf: 'center',
        backgroundColor: ColorToast.SurfaceDefault,
        flexDirection: 'row',
        justifyContent: 'center',
        position: 'absolute',
        bottom: SizeGlobal.Size600,
        borderRadius: SizeToast.BorderRadius,
        zIndex: 999,
        columnGap: SizeGlobal.Size200,
        paddingVertical: SizeToast.SpacingVertical,
        paddingHorizontal: SizeToast.SpacingHorizontal,
      },
      topView: {
        backgroundColor: ColorAlias.BackgroundPrimary,
        borderColor: ColorGlobal.Neutral100,
        borderRadius: SizeAlias.Radius4,
        flex: 1,
        marginHorizontal: SizeAlias.SpacingSmall,
        marginTop: SizeAlias.SpacingLarge,
      },
      iconDown: {
        width: SizeGlobal.Size500,
        height: SizeGlobal.Size600,
      },
      shadowView: {
        backgroundColor: ColorAlias.BorderDefault,
        height: 1,
        shadowColor: ColorGlobal.Neutral300,
        shadowOffset: {
          width: 2,
          height: -2,
        },
        shadowOpacity: 0.8,
      },
      btnConfirm: {
        marginVertical: SizeGlobal.Size300,
        marginHorizontal: SizeGlobal.Size400,
      },
    };
  },
);
