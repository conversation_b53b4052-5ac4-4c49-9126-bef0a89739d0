import {ColorButton, createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(
  ({Typography, ColorAlias, ColorCard, SizeAlias, SizeGlobal, ColorStepper, ColorDataView}) => ({
    flex: {
      flex: 1,
      color: ColorAlias.TextPrimary,
    },
    container: {
      flex: 1,
      paddingHorizontal: SizeGlobal.Size400,
      paddingTop: SizeGlobal.Size400,
    },
    scrollView: {
      flexGrow: 1,
      backgroundColor: 'transparent',
      paddingTop: SizeGlobal.Size600,
      rowGap: SizeGlobal.Size400,
      marginHorizontal: SizeGlobal.Size400,
    },
    childrenBottomHeader: {
      paddingHorizontal: SizeGlobal.Size600,
      backgroundColor: ColorStepper.ColorSurfaceDefault,
      paddingTop: SizeGlobal.Size200,
      paddingBottom: SizeGlobal.Size150,
    },
    cardInfoContainer: {
      borderRadius: <PERSON>zeAlias.Radius3,
      padding: SizeGlobal.Size400,
      flexDirection: 'row',
      alignItems: 'center',
      gap: SizeGlobal.Size300,
      backgroundColor: 'rgba(255, 255, 255, 1)',
    },
    cardInfoView: {
      flex: 1,
      justifyContent: 'center',
      rowGap: SizeGlobal.Size100,
    },
    cardName: {
      color: ColorDataView.TextMain,
    },
    cardInstrumente: {
      color: ColorDataView.TextSub,
    },
    touchableBox: {
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: SizeGlobal.Size300,
    },
    checkBoxBehavior: {
      width: 24,
      height: 24,
    },
    checkBoxWrap: {
      backgroundColor: ColorCard.SurfaceDefault,
      padding: SizeGlobal.Size400,
      borderRadius: SizeAlias.Radius3,
      rowGap: SizeGlobal.Size500,
    },
    desBold: {
      ...Typography?.small_semiBold,
      color: ColorButton.TextNeutralDefault,
    },
    btnContinue: {
      marginVertical: SizeGlobal.Size300,
    },
  }),
);
