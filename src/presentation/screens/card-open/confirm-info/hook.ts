import {ApplicationScreenProps, RootStackParamList} from '@presentation/navigation/types';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useMemo, useState} from 'react';

const useCardOpenConfirmInfo = () => {
  const {navigate} = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {params} = useRoute<ApplicationScreenProps<'CardOpenConfirmInfoScreen'>['route']>();

  const [isShowAddress, setIsShowAddress] = useState(false);
  const [isAcceptIssuanceContract] = useState(false);
  const [isAcceptCardOpenNoteCardUsage] = useState(false);

  const onNavCardOpenIssuanceContract = () => {
    navigate('CardOpenIssuanceContractScreen', params);
  };

  const onNavCardOpenNoteCardUsage = () => {
    navigate('CardOpenNoteCardUsageScreen', params);
  };

  const isDisabledContinue = useMemo(() => {
    return !isAcceptIssuanceContract && !isAcceptCardOpenNoteCardUsage;
  }, [isAcceptIssuanceContract, isAcceptCardOpenNoteCardUsage]);

  return {
    onNavCardOpenIssuanceContract,
    isAcceptIssuanceContract,
    isShowAddress,
    setIsShowAddress,
    isAcceptCardOpenNoteCardUsage,
    onNavCardOpenNoteCardUsage,
    isDisabledContinue,
  };
};

export default useCardOpenConfirmInfo;
