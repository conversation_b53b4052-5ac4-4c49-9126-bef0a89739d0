import {Text} from '@components/text';
import {MSBIcon, MSBIconProps, MSBIconSize, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {TextStyle, View, ViewStyle} from 'react-native';
import {styleSheet} from './styles';
interface Props {
  title: string;
  value: string;
  prefix?: string;
  iconName?: MSBIconProps['icon'];
  leftStyle?: TextStyle;
  rightStyle?: ViewStyle;
  iconOnPress?: () => void;
}
const CardOpenConfirmInfoRowItem: React.FC<Props> = ({
  title,
  value,
  prefix,
  iconName,
  leftStyle,
  rightStyle,
  iconOnPress,
}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  return (
    <View style={styles.container}>
      <Text style={[styles.leftStyle, leftStyle]} type={Typography?.small_regular}>
        {title}
      </Text>
      <View style={[styles.rightStyle, rightStyle]}>
        <Text style={styles.value} type={Typography?.small_medium}>
          {value}
        </Text>
        {!!prefix && (
          <Text style={styles.prefix} type={Typography?.small_regular}>
            {prefix}
          </Text>
        )}
        {!!iconName && <MSBIcon onIconClick={iconOnPress} icon={iconName} iconSize={MSBIconSize.SIZE_20} />}
      </View>
    </View>
  );
};

export default CardOpenConfirmInfoRowItem;
