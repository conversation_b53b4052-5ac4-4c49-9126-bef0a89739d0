import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorDataView, SizeGlobal}) => ({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    columnGap: SizeGlobal.Size400,
  },
  leftStyle: {
    flex: 1,
    color: ColorDataView.TextSub,
  },
  rightStyle: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-end',
    columnGap: SizeGlobal.Size100,
  },
  value: {
    color: ColorDataView.TextMain,
    textAlign: 'right',
  },
  prefix: {
    color: ColorDataView.TextCurrency,
    textAlign: 'right',
  },
}));
