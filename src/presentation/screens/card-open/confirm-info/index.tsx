import {Block} from '@components/block';
import CardOpenCardImage from '@components/card-open-card-image';
import {sharedStyleSheet} from '@components/common/styles';
import FormSection from '@components/form/FormSection';
import HorizontalDivider from '@components/line-view/Divider';
import {Text} from '@components/text';
import {mapToCard} from '@data/mappers/card/CardMapper';
import {translate} from '@locales';
import {formatMoney, highlightText} from '@utils/StringFormat';
import {
  MSBButton,
  MSBIcons,
  MSBPage,
  MSBStepper,
  MSBTouchable,
  MSBTouchableBox,
  TouchableBoxType,
  useMSBStyles,
} from 'msb-shared-component';
import React, {useMemo} from 'react';
import {View} from 'react-native';
import CardOpenConfirmInfoRowItem from './components/card-open-confirm-info-row-item';
import useCardOpenConfirmInfo from './hook';
import {styleSheet} from './styles';

const mock = mapToCard({
  productCode: 'VISA123',
  name: 'MSB MasterCard Debit FCB',
  physicalAvailable: 0,
  virtualAvailable: 0,
  cardVisual: {
    images: [
      {
        imageId: 'CARD_521976_03_FCB_PHYSICAL',
        imageURL: 'https://minio-uat.msb.com.vn/ibadmin-public/card/debit/master/platinum/debit-mc-fcb.png',
        type: 'FRONT',
        version: '1',
      },
    ],
  },
  benefits: [
    {
      description: 'Giao dịch thuận tiện tại các điểm thanh toán trong nước và nước ngoài',
    },
    {
      description: 'Biến động tài khoản mọi lúc mọi nơi được gửi với dịch vụ SMS Banking',
    },
    {
      description: 'Ưu đãi tới 50% tại hơn 300 cửa hàng trong Thế giới ưu đãi JOY',
    },
  ],
});

const CardIFeeMock = [
  {
    title: translate('card_open_confirm_info.fee_issue'),
    value: formatMoney(55000),
    prefix: translate('card_open_confirm_info.currency_vnd'),
  },
  {
    title: translate('card_open_confirm_info.fee_annual'),
    value: translate('card_open_confirm_info.fee_free'),
  },
  {
    title: translate('card_open_confirm_info.fee_delivery'),
    value: translate('card_open_confirm_info.fee_location_based'),
  },
];

const CardOpenConfirmInfoScreen = () => {
  const {
    onNavCardOpenIssuanceContract,
    isAcceptIssuanceContract,
    isAcceptCardOpenNoteCardUsage,
    onNavCardOpenNoteCardUsage,
    isShowAddress,
    setIsShowAddress,
    isDisabledContinue,
  } = useCardOpenConfirmInfo();
  const {styles, theme} = useMSBStyles(styleSheet);
  const {styles: shareStyles} = useMSBStyles(sharedStyleSheet);

  const CardInfoMock = useMemo(
    () => [
      {
        title: translate('card_open_confirm_info.info_linked_account'),
        value: '**********',
        rightStyle: {flex: 0},
      },
      {
        title: translate('card_open_confirm_info.info_name_on_card'),
        value: 'TRAN TRUNG HIEU',
      },
      {
        title: translate('card_open_confirm_info.info_first_school_question'),
        value: isShowAddress ? 'Trường trung học phổ thông Đống Đa' : translate('card_open_confirm_info.masked_value'),
        iconName: isShowAddress ? MSBIcons.IconEyeOff : MSBIcons.IconEyeOn,
        iconOnPress: () => setIsShowAddress(v => !v),
      },
      {
        title: translate('card_open_confirm_info.info_delivery_address'),
        value: '54a Nguyễn Chí Thanh, Láng thượng, Đống Đa, Hà Nội',
      },
      {
        title: translate('card_open_confirm_info.info_referral_code'),
        value: '456345\nTRAN HAI ANH',
      },
    ],
    [isShowAddress],
  );

  return (
    <MSBPage
      testID="cm.cardOpenConfirmInfoScreen"
      backgroundColor={theme.ColorHeader.SurfaceLevel1}
      headerProps={{
        title: translate('card_open_confirm_info.screen_title'),
        hasBack: true,
        barStyle: true,
        childrenBottomHeader: (
          <View style={styles.childrenBottomHeader}>
            <MSBStepper
              title={translate('card_open_confirm_info.step_title')}
              totalSteps={3}
              currentStep={3}
              progress={1}
            />
          </View>
        ),
      }}>
      <View style={styles.scrollView}>
        <View style={[styles.cardInfoContainer, shareStyles.shadow]}>
          <CardOpenCardImage imageWidth={115} cardVisual={mock.cardVisual} />
          <View style={styles.cardInfoView}>
            <Text type={theme.Typography?.base_medium} style={styles.cardName}>
              {mock.name}
            </Text>
            <Text type={theme.Typography?.small_medium} style={styles.cardInstrumente}>
              {/* @todo */}
              {/* {mock.instrument} */}
              {translate('detail_card.instrument_title')}
            </Text>
          </View>
        </View>
        <FormSection title={translate('card_open_confirm_info.section_card_info')}>
          <Block padding={theme.SizeGlobal.Size400} gap={theme.SizeGlobal.Size400}>
            {CardInfoMock.map(i => {
              return (
                <CardOpenConfirmInfoRowItem
                  key={i.title}
                  title={i.title}
                  value={i.value}
                  iconName={i.iconName}
                  iconOnPress={i.iconOnPress}
                  rightStyle={i?.rightStyle || undefined}
                />
              );
            })}
          </Block>
        </FormSection>
        <FormSection title={translate('card_open_confirm_info.section_fee')}>
          <Block padding={theme.SizeGlobal.Size400} gap={theme.SizeGlobal.Size400}>
            {CardIFeeMock.map(i => {
              return <CardOpenConfirmInfoRowItem key={i.title} title={i.title} value={i.value} prefix={i.prefix} />;
            })}
          </Block>
        </FormSection>
        <View style={[styles.checkBoxWrap, shareStyles.shadow]}>
          <MSBTouchable style={styles.touchableBox} onPress={onNavCardOpenIssuanceContract}>
            <MSBTouchableBox
              style={styles.checkBoxBehavior}
              status={isAcceptIssuanceContract}
              // onPress={onNavCardOpenIssuanceContract}
              type={TouchableBoxType.CheckBox}
              isNotActionOnPress={true}
              // testID={'obd.termAndConditionScreen.rule1Cb'}
            />
            <Text type={theme.Typography?.small_regular} style={styles.flex}>
              {highlightText(
                translate('card_open_confirm_info.agreement_product_contract'),
                translate('card_open_confirm_info.agreement_product_contract_bold'),
                styles.desBold,
                {},
              )}
            </Text>
          </MSBTouchable>
          <HorizontalDivider />
          <MSBTouchable style={styles.touchableBox} onPress={onNavCardOpenNoteCardUsage}>
            <MSBTouchableBox
              status={isAcceptCardOpenNoteCardUsage}
              style={styles.checkBoxBehavior}
              type={TouchableBoxType.CheckBox}
              isNotActionOnPress={true}
              // testID={'obd.termAndConditionScreen.rule1Cb'}
            />
            <Text type={theme.Typography?.small_regular} style={styles.flex}>
              {highlightText(
                translate('card_open_confirm_info.agreement_note_and_terms'),
                [
                  translate('card_open_confirm_info.agreement_note_and_terms_bold1'),
                  translate('card_open_confirm_info.agreement_note_and_terms_bold2'),
                ],
                styles.desBold,
                {},
              )}
            </Text>
          </MSBTouchable>
        </View>
        <MSBButton
          disabled={isDisabledContinue}
          containerStyle={styles.btnContinue}
          label={translate('card_open_confirm_info.button_continue')}
        />
      </View>
    </MSBPage>
  );
};

export default CardOpenConfirmInfoScreen;
