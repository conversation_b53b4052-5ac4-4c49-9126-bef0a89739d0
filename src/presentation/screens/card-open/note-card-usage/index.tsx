import {Federated} from '@callstack/repack/client';
import {translate} from '@locales';
import {ScreenLoadFailMicroApp} from '@utils/LoadFailMicroApp';
import {ErrorBoundary} from 'msb-host-shared-module';
import {MSBCardModuleSkeleton, MSBPage} from 'msb-shared-component';
import React, {useCallback} from 'react';
import CardOpenNoteCardUsage from './hook';

const TermAndCondition = React.lazy(() =>
  process.env.MF_VERSION !== 'v2' || process.env.NODE_ENV === 'production'
    ? Federated.importModule('HelperModule', './TermAndCondition')
    : import('HelperModule/TermAndCondition'),
);

const CardOpenNoteCardUsageScreen = () => {
  const {} = CardOpenNoteCardUsage();

  const FallbackComponent = useCallback(() => {
    return <ScreenLoadFailMicroApp />;
  }, []);

  return (
    <MSBPage
      testID="cm.cardOpenNoteCardUsageScreen"
      isScrollable={false}
      headerProps={{
        title: translate('card_open_note_card_usage.screen_title'),
        hasBack: true,
      }}>
      <ErrorBoundary FallbackComponent={FallbackComponent}>
        <React.Suspense fallback={<MSBCardModuleSkeleton />}>
          <TermAndCondition
            type="ONBOARDING"
            requireReadCondition={true}
            onPressHightlightText={() => {}}
            onConfirmationTncSuccess={() => {}}
          />
        </React.Suspense>
      </ErrorBoundary>
      {/* <View style={[styles.topView, sharedStyles.shadow]} pointerEvents="box-none">
        <MSBTextBase
          content={translate('card_open_note_card_usage.note_title')}
          type={theme.Typography?.title_medium}
          style={styles.title}
        />
        <HorizontalDivider />
        <View style={styles.container}>
          <Pdf
            ref={pdfRef}
            source={{
              uri: 'https://www.aeee.in/wp-content/uploads/2020/08/Sample-pdf.pdf',
              cacheFileName: 'true',
            }}
            style={styles.container}
            enableDoubleTapZoom={false}
            enableAnnotationRendering={false}
            enableAntialiasing={false}
            onPageChanged={(page, total) => {
              setLoadComplete(true);
              setNumberOfPages(total);
              if (page === total) {
                setScrollToBottom(true);
                setCheckBoxStateReadIllegalBehavior(false);
              }
            }}
            onError={error => console.log('PDF Error:', error)}
            trustAllCerts={false}
          />
          {isLoadComplete && toastComponent}
        </View>
        <View style={styles.shadowView} />
        <MSBTouchable style={styles.touchableBox} onPress={() => setIsReadIllegalBehavior(v => !v)}>
          <MSBTouchableBox
            status={isReadIllegalBehavior}
            style={styles.checkBoxBehavior}
            type={TouchableBoxType.CheckBox}
            isNotActionOnPress={true}
            disabled={checkBoxStateReadIllegalBehavior}
            // testID={'obd.termAndConditionScreen.rule1Cb'}
          />

          <Text type={theme.Typography?.small_regular} style={styles.flex}>
            {highlightTextOnPress(
              translate('card_open_note_card_usage.confirmation_text'),
              [
                {
                  text: translate('card_open_note_card_usage.terms_and_conditions'),
                  onPress: showTermsConditionsBottomSheet,
                },
              ],
              styles.desBold,
              {},
            )}
          </Text>
        </MSBTouchable>
      </View>

      <MSBButton
        // style={commonStyles.button}
        buttonSize={ButtonSize.Medium}
        label={translate('card_open_note_card_usage.continue_button')}
        disabled={!isReadIllegalBehavior}
        style={styles.btnConfirm}
        // onPress={confirmationTnc}
        // testID={'obd.termAndConditionScreen.continueBtn'}
      /> */}
    </MSBPage>
  );
};

export default CardOpenNoteCardUsageScreen;
