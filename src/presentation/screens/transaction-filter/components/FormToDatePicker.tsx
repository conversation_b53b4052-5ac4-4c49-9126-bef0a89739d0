import {FormTransactionFilter} from '@domain/entities/form/FormTransactionFilter';
import FormDatePicker from '@presentation/components/form/FormDatePicker';
import {FormDatePickerProps} from '@presentation/components/form/types';
import {isNil} from 'lodash';
import React from 'react';
import {useWatch} from 'react-hook-form';

const FormToDatePicker = (props: FormDatePickerProps<FormTransactionFilter>) => {
  const fromDate = useWatch({
    control: props.control,
    name: 'fromDate',
  });

  return <FormDatePicker {...props} disabled={isNil(fromDate)} minDate={isNil(fromDate) ? undefined : fromDate} />;
};

export default FormToDatePicker;
