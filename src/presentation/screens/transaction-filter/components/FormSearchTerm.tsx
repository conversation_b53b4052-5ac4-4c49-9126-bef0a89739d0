import {Block} from '@components/block';
import {Text} from '@components/text';
import {FormTransactionFilter} from '@domain/entities/form/FormTransactionFilter';
import {translate} from '@locales';
import {getSize, MSBIcon, MSBSearchInput, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {FieldPath, useController} from 'react-hook-form';
import {FormSearchTermProps} from './types';

const FormSearchTerm = ({control, nameTrigger, testID}: FormSearchTermProps<FormTransactionFilter>) => {
  const {
    theme: {Typography, SizeGlobal, ColorLabelCaption},
  } = useMSBStyles();
  const {field} = useController({
    control,
    name: nameTrigger as FieldPath<FormTransactionFilter>,
  });
  return (
    <Block gap={getSize(12)}>
      <MSBSearchInput
        testID={testID}
        value={field.value as string}
        onChangeText={field.onChange}
        onBlur={field.onBlur}
        placeholder={translate('transaction.content_placeholder')}
      />
      <Block direction="row" gap={SizeGlobal.Size200}>
        <MSBIcon icon="info-tooltip-circle" iconSize={20} />
        <Text flex={1} type={Typography?.small_regular} color={ColorLabelCaption.TextSub}>
          {translate('transaction.search_minimum')}
        </Text>
      </Block>
    </Block>
  );
};

export default FormSearchTerm;
