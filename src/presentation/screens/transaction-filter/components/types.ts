import {FormChipProps} from '@presentation/components/form/types';
import {FieldValues} from 'react-hook-form';

export interface FormSearchTermProps<T extends FieldValues> extends FormControl<T> {}

export interface FormButtonGroupProps<T extends FieldValues> extends Omit<FormControl<T>, 'nameTrigger'> {
  onFilter?: () => void;
  onClear?: () => void;
}

export interface FormFilterChipProps<T extends FieldValues> extends FormChipProps<T> {}
