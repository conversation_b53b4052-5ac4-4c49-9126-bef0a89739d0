import {FormTransactionFilter} from '@domain/entities/form/FormTransactionFilter';
import {translate} from '@locales';
import {MSBGroupButton} from 'msb-shared-component';
import React from 'react';
import {useFormState} from 'react-hook-form';
import {FormButtonGroupProps} from './types';

const FormButtonGroup = ({control, onClear, onFilter}: FormButtonGroupProps<FormTransactionFilter>) => {
  const {isValid} = useFormState({control});
  return (
    <MSBGroupButton
      testIDConfirm="cm.filter-transaction.confirm-btn"
      testIDClose="cm.filter-transaction.close-btn"
      disabledConfirm={!isValid}
      cancelBtnText={translate('transaction.clear_filter')}
      confirmBtnText={translate('transaction.search')}
      onClose={onClear}
      onConfirm={onFilter}
    />
  );
};

export default FormButtonGroup;
