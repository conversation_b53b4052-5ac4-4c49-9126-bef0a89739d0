import {FormTransactionFilter, TransactionDateRange} from '@domain/entities/form/FormTransactionFilter';
import {FormDatePickerProps} from '@presentation/components/form/types';
import {isNil} from 'lodash';
import {MSBDatePicker} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FieldPath, useController} from 'react-hook-form';
import {getTransactionDateRangeOptions} from '../form';

const FormFromDatePicker = ({
  control,
  nameTrigger,
  trigger,
  setValue,
  getValues,
  ...props
}: FormDatePickerProps<FormTransactionFilter>) => {
  const {field} = useController({
    control,
    name: nameTrigger as FieldPath<FormTransactionFilter>,
  });

  const onChangeDate = useCallback(
    (date: Date) => {
      field.onChange(date);

      const toDate = getValues?.('toDate');
      if (isNil(toDate)) {
        setValue?.('toDate', date);
      }

      const dateRangeType = getValues?.('transactionDateRange.id');
      if (dateRangeType !== TransactionDateRange.OTHER) {
        setValue?.('transactionDateRange', getTransactionDateRangeOptions()[2]);
      }
      trigger?.(field.name);
      trigger?.('toDate');
    },
    [field],
  );

  return <MSBDatePicker {...props} onChangeDate={onChangeDate} value={field.value as Date | undefined} />;
};

export default FormFromDatePicker;
