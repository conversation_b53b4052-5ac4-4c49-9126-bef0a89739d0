import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {FormTransactionFilter, TransactionDateRange} from '@domain/entities/form/FormTransactionFilter';
import FormChip from '@presentation/components/form/FormChip';
import {getDateRange} from '@utils';
import React, {useCallback} from 'react';
import {FormFilterChipProps} from './types';

const FormFilterChip = (props: FormFilterChipProps<FormTransactionFilter>) => {
  const interceptor = useCallback(
    (previous: SelectionItem | null, next: SelectionItem) => {
      if (previous?.id === next.id) {
        return;
      }

      switch (next.id) {
        case TransactionDateRange.OTHER:
          props.setValue?.('fromDate', null);
          props.setValue?.('toDate', null);

          props.trigger?.('fromDate');
          props.trigger?.('toDate');
          break;

        default:
          const {startDate, endDate} = getDateRange(next.id);
          props.setValue?.('fromDate', startDate.toDate());
          props.setValue?.('toDate', endDate.toDate());
          break;
      }
    },
    [props.setValue, props.trigger],
  );
  return <FormChip {...props} interceptor={interceptor} />;
};

export default FormFilterChip;
