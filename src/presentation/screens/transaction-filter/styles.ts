import {createMSBStyleSheet, getSize} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeAlias, SizeGlobal, ColorStepper}) => ({
  scrollView: {
    flexGrow: 1,
    paddingTop: getSize(24),
    paddingHorizontal: getSize(16),
    backgroundColor: 'transparent',
    rowGap: SizeAlias.SpacingLarge,
  },
  container: {
    flex: 1,
    paddingTop: SizeGlobal.Size400,
    paddingHorizontal: SizeGlobal.Size400,
  },
  accountLoading: {flexDirection: 'column'},
  headerContent: {
    paddingHorizontal: SizeGlobal.Size600,
    paddingTop: SizeGlobal.Size200,
    paddingBottom: SizeGlobal.Size150,
    backgroundColor: ColorStepper.ColorSurfaceDefault,
  },
}));
