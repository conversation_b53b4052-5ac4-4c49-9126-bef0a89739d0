import {FormTransactionFilter} from '@domain/entities/form/FormTransactionFilter';
import {zodResolver} from '@hookform/resolvers/zod';
import {ApplicationScreenProps, RootStackParamList} from '@presentation/navigation/types';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useTransactionActions} from '@store/Transaction';
import {useCallback} from 'react';
import {useForm} from 'react-hook-form';
import {getDefaultValues, validation} from './form';

export const useTransactionFilterViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {
    params: {cardId},
  } = useRoute<ApplicationScreenProps<'TransactionFilterScreen'>['route']>();

  const {setFormFilterTransaction, getFormFilterTransaction} = useTransactionActions();
  const {control, getValues, reset, setValue, trigger, setError} = useForm<FormTransactionFilter>({
    mode: 'onBlur',
    resolver: zodResolver(validation),
    defaultValues: getDefaultValues(getFormFilterTransaction(cardId)),
    shouldFocusError: false,
  });

  const onFilter = useCallback(() => {
    const values = getValues();
    setFormFilterTransaction(cardId, values);
    navigation.replace('ListHistoryScreen', {
      cardId,
      filterOptions: values,
    });
  }, [getValues]);

  const onClear = useCallback(() => {
    reset(getDefaultValues());
  }, []);

  return {
    control,
    getValues,
    reset,
    setValue,
    trigger,
    setError,
    onClear,
    onFilter,
  };
};
