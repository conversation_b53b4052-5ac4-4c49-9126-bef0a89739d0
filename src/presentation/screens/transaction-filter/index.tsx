import {Block} from '@components/block';
import FormAmountInput from '@components/form/FormAmountInput';
import FormRadio from '@components/form/FormRadio';
import {translate} from '@locales';
import FormAmountSuggest from '@presentation/components/form/FormAmountSuggest';
import moment from 'moment';
import {getSize, MSBPage, MSBScrollView, useMSBStyles} from 'msb-shared-component';
import React, {useMemo} from 'react';
import FormButtonGroup from './components/FormButtonGroup';
import FormFilterChip from './components/FormFilterChip';
import FormFromDatePicker from './components/FormFromDatePicker';
import FormSearchTerm from './components/FormSearchTerm';
import FormToDatePicker from './components/FormToDatePicker';
import {getTransactionDateRangeOptions, getTransactionTypeOptions} from './form';
import {useTransactionFilterViewModel} from './hook';
import {styleSheet} from './styles';

const TransactionFilterScreen = () => {
  const {
    styles,
    theme: {SizeGlobal, ColorGlobal, SizeAlias},
  } = useMSBStyles(styleSheet);
  const {control, trigger, setValue, getValues, onClear, onFilter} = useTransactionFilterViewModel();

  const maxDate = useMemo(() => new Date(), []);
  const minDate = useMemo(() => moment(maxDate).subtract(90, 'days').toDate(), [maxDate]);

  const transactionTypeOptions = useMemo(() => getTransactionTypeOptions(), []);
  const transactionDateRangeOptions = useMemo(() => getTransactionDateRangeOptions(), []);

  return (
    <MSBPage
      testID="cm.transaction-filter"
      isScrollable={false}
      headerProps={{
        title: translate('transaction.search_title'),
        hasBack: true,
      }}>
      <MSBScrollView testID="cm.transaction-filter.scroll-view" contentContainerStyle={styles.scrollView}>
        <Block
          padding={SizeGlobal.Size400}
          gap={SizeGlobal.Size400}
          color={ColorGlobal.NeutralWhite}
          borderRadius={SizeAlias.Radius3}
          shadow>
          <FormSearchTerm testID="cm.filter-transaction.search-team" control={control} nameTrigger="searchTerm" />
          <FormRadio
            testID="cm.filter-transaction.transaction-types"
            control={control}
            nameTrigger="transactionType"
            options={transactionTypeOptions}
            label={translate('transaction.type')}
            trigger={trigger}
          />
          <FormFilterChip
            testID="cm.filter-transaction.transaction-date-ranges"
            control={control}
            nameTrigger="transactionDateRange"
            setValue={setValue}
            trigger={trigger}
            options={transactionDateRangeOptions}
            label={translate('transaction.time')}
          />
          <FormFromDatePicker
            testID="cm.filter-transaction.from-date"
            control={control}
            nameTrigger="fromDate"
            setValue={setValue}
            getValues={getValues}
            trigger={trigger}
            label={translate('transaction.from_date')}
            maxDate={maxDate}
            minDate={minDate}
          />
          <FormToDatePicker
            testID="cm.filter-transaction.to-date"
            control={control}
            nameTrigger="toDate"
            trigger={trigger}
            label={translate('transaction.to_date')}
            maxDate={maxDate}
            minDate={minDate}
          />
          <FormAmountInput
            testID="cm.filter-transaction.from-currency"
            control={control}
            nameTrigger="fromCurrency"
            trigger={trigger}
            label={translate('transaction.amount_from')}
            placeholder={translate('transaction.enter_amount')}
          />
          <FormAmountInput
            testID="cm.filter-transaction.to-currency"
            control={control}
            nameTrigger="toCurrency"
            trigger={trigger}
            label={translate('transaction.amount_to')}
            placeholder={translate('transaction.enter_amount')}
          />
        </Block>
      </MSBScrollView>
      <Block paddingHorizontal={getSize(16)} paddingTop={getSize(12)}>
        <FormButtonGroup control={control} onClear={onClear} onFilter={onFilter} />
      </Block>
      <FormAmountSuggest setValue={setValue} />
    </MSBPage>
  );
};

export default TransactionFilterScreen;
