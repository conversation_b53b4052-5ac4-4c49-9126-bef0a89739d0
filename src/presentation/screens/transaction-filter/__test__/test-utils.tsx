import {DISPLAY_DATE_FORMAT} from '@constants';
import {TransactionDateRange, TransactionType} from '@entities/form/FormTransactionFilter';
import {fireEvent, screen, userEvent, within} from '@presentation/__test__/test-utils';
import {goToDetailScreen} from '@presentation/screens/detail-card/__test__/test-utils';
import {getDateRange} from '@utils';
import {ColorChip} from 'msb-shared-component';
import {ReactTestInstance} from 'react-test-renderer';

export const goToFilterTransactionScreen = async () => {
  const testCardId = '246244460';
  await goToDetailScreen({testCardId});

  const userSetup = userEvent.setup();
  const filterBtn = await screen.findByTestId('cm.history-card.search-btn');
  await userSetup.press(filterBtn);

  const searchTouchable = await screen.findByTestId('cm.filter-transaction.confirm-btn');
  // Only disable enable search button if user inputs only 1 character
  expect(searchTouchable).toBeEnabled();
  const searchTermInput = screen.getByTestId('cm.filter-transaction.search-team');
  fireEvent.changeText(searchTermInput, 'T');
  searchTermInput.props.onBlur();
  // should disable search button if user inputs 1 character in search field
  expect(await screen.findByTestId('cm.filter-transaction.confirm-btn')).toBeDisabled();
  fireEvent.changeText(searchTermInput, 'Test');

  const incomeRadio = within(screen.getByTestId('cm.filter-transaction.transaction-types')).getByTestId(
    `cm.form-radio.${TransactionType.INCOME}`,
  );
  await userSetup.press(incomeRadio);

  expect(
    within(await screen.findByTestId(`cm.form-radio.${TransactionType.INCOME}`)).getByTestId('checked-icon'),
  ).toBeTruthy();

  const days7Chip = within(screen.getByTestId('cm.filter-transaction.transaction-date-ranges')).getByTestId(
    `cm.form-chip.${TransactionDateRange['7_DAYS']}`,
  );

  await userSetup.press(days7Chip);
  const {startDate, endDate} = getDateRange(TransactionDateRange['7_DAYS']);
  expect(
    within(await screen.findByTestId('cm.filter-transaction.from-date')).getByText(
      startDate.format(DISPLAY_DATE_FORMAT),
    ),
  ).toBeOnTheScreen();
  expect(
    within(screen.getByTestId('cm.filter-transaction.to-date')).getByText(endDate.format(DISPLAY_DATE_FORMAT)),
  ).toBeOnTheScreen();
  expect(searchTouchable).toBeEnabled();

  const amountFromInput = screen.getByTestId('cm.filter-transaction.from-currency');
  fireEvent.changeText(amountFromInput, '10000');
  expect(amountFromInput).toHaveProp('value', '10,000');
  const amountToInput = screen.getByTestId('cm.filter-transaction.to-currency');
  fireEvent.changeText(amountToInput, '500000');
  expect(amountToInput).toHaveProp('value', '500,000');
};

export const hexToRgb = (hex: string) => {
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null;
};

export const expectChipIsActive = (chip: ReactTestInstance) => {
  const {r, g, b} = hexToRgb(ColorChip.SurfaceActive)!;
  const expectedRgb = `rgb(${r},${g},${b})`;

  const styles = Array.isArray(chip.props.style) ? chip.props.style : [chip.props.style];

  const hasActiveColor = styles.some(
    style => typeof style?.backgroundColor === 'string' && style.backgroundColor.includes(expectedRgb),
  );

  expect(hasActiveColor).toBe(true);
};
