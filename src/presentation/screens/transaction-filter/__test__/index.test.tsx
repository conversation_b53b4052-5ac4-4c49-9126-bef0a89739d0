import {DISPLAY_DATE_FORMAT} from '@constants';
import {TransactionDateRange, TransactionType} from '@entities/form/FormTransactionFilter';
import {act, screen, userEvent, within} from '@presentation/__test__/test-utils';
import {goToDetailScreen} from '@presentation/screens/detail-card/__test__/test-utils';
import {getDateRange} from '@utils';
import moment from 'moment';
import {ReactTestInstance} from 'react-test-renderer';
import {expectChipIsActive, goToFilterTransactionScreen} from './test-utils';

describe('Transaction Filter Screen', () => {
  it('should enable search button if user filled required fields in form', async () => {
    await goToFilterTransactionScreen();
  });

  it('should clear form fields when user clicks clear filter', async () => {
    await goToFilterTransactionScreen();

    const userSetup = userEvent.setup();
    const clearBtn = screen.getByTestId('cm.filter-transaction.close-btn');
    await userSetup.press(clearBtn);

    const searchTermInput = screen.getByTestId('cm.filter-transaction.search-team');
    expect(searchTermInput).toHaveProp('value', '');

    expect(within(screen.getByTestId(`cm.form-radio.${TransactionType.ALL}`)).getByTestId('checked-icon')).toBeTruthy();

    const amountFromInput = screen.getByTestId('cm.filter-transaction.from-currency');
    expect(amountFromInput).toHaveProp('value', '');
    const amountToInput = screen.getByTestId('cm.filter-transaction.to-currency');
    expect(amountToInput).toHaveProp('value', '');
  });

  it('should activate other date range and set from date as default to date when user selects from date', async () => {
    const testCardId = '246244460';
    await goToDetailScreen({testCardId});

    const userSetup = userEvent.setup();
    const filterBtn = await screen.findByTestId('cm.history-card.search-btn');
    await userSetup.press(filterBtn);
    const prevDateFromSelector = await screen.findByTestId('cm.filter-transaction.from-date');
    const dateModalPicker = (
      prevDateFromSelector.children[prevDateFromSelector.children.length - 1] as ReactTestInstance
    ).props;

    const testDate = new Date();
    act(() => {
      dateModalPicker.onConfirm(testDate);
    });
    expect(
      within(await screen.findByTestId('cm.filter-transaction.from-date')).getByText(
        moment(testDate).format(DISPLAY_DATE_FORMAT),
      ),
    ).toBeOnTheScreen();
    expect(
      within(screen.getByTestId('cm.filter-transaction.to-date')).getByText(
        moment(testDate).format(DISPLAY_DATE_FORMAT),
      ),
    ).toBeOnTheScreen();
    const otherDateRange = within(
      await screen.findByTestId('cm.filter-transaction.transaction-date-ranges'),
    ).getByTestId(`cm.form-chip.${TransactionDateRange.OTHER}.inner`);

    expectChipIsActive(otherDateRange);
  });

  it('should clear from and to dates when switching from 7 or 15 days to a custom range', async () => {
    const testCardId = '246244460';
    await goToDetailScreen({testCardId});

    const userSetup = userEvent.setup();
    const filterBtn = await screen.findByTestId('cm.history-card.search-btn');
    await userSetup.press(filterBtn);

    const days7Chip = within(screen.getByTestId('cm.filter-transaction.transaction-date-ranges')).getByTestId(
      `cm.form-chip.${TransactionDateRange['7_DAYS']}`,
    );

    await userSetup.press(days7Chip);
    const searchTouchable = await screen.findByTestId('cm.filter-transaction.confirm-btn');
    expect(searchTouchable).toBeEnabled();

    const {startDate, endDate} = getDateRange(TransactionDateRange['7_DAYS']);
    const fromDatePicker = await screen.findByTestId('cm.filter-transaction.from-date');
    expect(within(fromDatePicker).getByText(startDate.format(DISPLAY_DATE_FORMAT))).toBeOnTheScreen();

    const toDatePicker = screen.getByTestId('cm.filter-transaction.to-date');
    expect(within(toDatePicker).getByText(endDate.format(DISPLAY_DATE_FORMAT))).toBeOnTheScreen();

    const otherDateRangeChip = within(screen.getByTestId('cm.filter-transaction.transaction-date-ranges')).getByTestId(
      `cm.form-chip.${TransactionDateRange.OTHER}`,
    );
    await userSetup.press(otherDateRangeChip);
    expect(searchTouchable).toBeDisabled();

    expect(
      within(await screen.findByTestId('cm.filter-transaction.from-date')).getByText('dd/mm/yyyy'),
    ).toBeOnTheScreen();
    expect(within(screen.getByTestId('cm.filter-transaction.to-date')).getByText('dd/mm/yyyy')).toBeOnTheScreen();
  });
});
