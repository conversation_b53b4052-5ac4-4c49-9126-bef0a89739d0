import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {
  FormTransactionFilter,
  TransactionDateRange,
  TransactionType,
} from '@domain/entities/form/FormTransactionFilter';
import {translate} from '@locales';
import {ValidateMessageObject} from '@presentation/hooks/useErrorMessageTranslation';
import {parseCurrencyToNumber} from '@utils/StringFormat';
import {isNil} from 'lodash';
import {z} from 'zod';

export const getTransactionTypeOptions = (): SelectionItem[] => [
  {
    id: TransactionType.ALL,
    name: translate('transaction.type_all'),
  },
  {
    id: TransactionType.INCOME,
    name: translate('transaction.type_in'),
  },
  {
    id: TransactionType.OUTCOME,
    name: translate('transaction.type_out'),
  },
];

export const getTransactionDateRangeOptions = (): SelectionItem[] => [
  {
    id: TransactionDateRange['7_DAYS'],
    name: translate('transaction.time_n_days', {
      n: TransactionDateRange['7_DAYS'],
    }),
  },
  {
    id: TransactionDateRange['15_DAYS'],
    name: translate('transaction.time_n_days', {
      n: TransactionDateRange['15_DAYS'],
    }),
  },
  {
    id: TransactionDateRange.OTHER,
    name: translate('transaction.other_time'),
  },
];

export const getDefaultValues = (options?: Partial<FormTransactionFilter>): FormTransactionFilter => {
  return {
    searchTerm: options?.searchTerm ?? '',
    transactionType: options?.transactionType ?? getTransactionTypeOptions()[0],
    transactionDateRange: options?.transactionDateRange ?? null,
    fromDate: options?.fromDate ?? null,
    toDate: options?.toDate ?? null,
    fromCurrency: options?.fromCurrency ?? '',
    toCurrency: options?.toCurrency ?? '',
  };
};

export const validation = z
  .object<ZodShape<FormTransactionFilter>>({
    searchTerm: z
      .string()
      .trim()
      .refine(term => {
        const value = term.trim();
        if (value.length > 0 && value.length < 2) {
          return false;
        }
        return true;
      }),
    transactionType: z.custom<SelectionItem>(),
    transactionDateRange: z.custom<SelectionItem>().nullable(),
    fromDate: z.custom<Date>().nullable(),
    toDate: z.custom<Date>().nullable(),
    fromCurrency: z.string(),
    toCurrency: z.string(),
  })
  .superRefine((values, ctx) => {
    if (values.fromCurrency.trim().length > 0 && values.toCurrency.trim().length > 0) {
      const fromCurrency = parseCurrencyToNumber(values.fromCurrency);
      const toCurrency = parseCurrencyToNumber(values.toCurrency);

      if (toCurrency < fromCurrency) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['toCurrency'],
          message: JSON.stringify({
            keyT: 'transaction.amount_range_invalid',
          } as ValidateMessageObject),
        });
      }
    }

    if (values.transactionDateRange?.id === TransactionDateRange.OTHER) {
      if (isNil(values.fromDate) || isNil(values.toDate)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['transactionDateRange'],
        });
      }
    }
  });
