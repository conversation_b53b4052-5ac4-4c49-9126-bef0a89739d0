import {RootStackParamList} from '@app-navigation/types.ts';
import RoundedBackgroundView from '@components/background-custom-view';
import {translate} from '@locales';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {MSBFastImage, MSBFolderImage, MSBOTPInput, MSBPage, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React, {useEffect} from 'react';
import {ScrollView, View} from 'react-native';
import useReInputPinViewModel from './hook.tsx';
import {styleSheet} from './styles.tsx';

const ReInputPinScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'ReInputPinScreen'>> = ({navigation}) => {
  const {error, otpLength, handleValidInput, isDebitCard, pinInputRef, isInit} = useReInputPinViewModel();
  const {styles} = useMSBStyles(styleSheet);

  useEffect(() => {
    const unsubscribe = navigation.addListener('transitionEnd', () => {
      if (!isInit.current) {
        pinInputRef.current?.focus();
        isInit.current = true;
      }
    });
    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <MSBPage
      testID="cm.reInputPinScreen"
      style={styles.container}
      isScrollable={false}
      headerProps={{
        title: translate('re_input_pin.title'),
        hasBack: true,
      }}>
      <ScrollView style={styles.flexGrow} scrollEnabled={false}>
        <RoundedBackgroundView style={styles.bg}>
          <MSBFastImage
            folder={MSBFolderImage.IMAGES}
            nameImage={isDebitCard ? 'otp' : 'otp-numbers'}
            style={isDebitCard ? styles.keyIcon : styles.keyIcon4}
          />

          <MSBTextBase style={styles.label}>{translate('re_input_pin.please_setup_pin')}</MSBTextBase>
          <View style={styles.otpInputContainer}>
            <MSBOTPInput
              ref={pinInputRef}
              isEnable
              // isReset={isReset}
              otpLength={otpLength}
              autoFocus={false}
              secureTextEntry
              onComplete={handleValidInput}
              containerStyle={isDebitCard ? styles.otpInput : styles.otpInputLeft}
            />
            {error && <MSBTextBase style={styles.errorText}>{error}</MSBTextBase>}
          </View>
        </RoundedBackgroundView>
      </ScrollView>
    </MSBPage>
  );
};

export default ReInputPinScreen;
