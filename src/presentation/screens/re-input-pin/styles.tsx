import {screenWidthToDesignRatio} from '@constants/sizes';
import {createMSBStyleSheet, SizeGlobal} from 'msb-shared-component';

const IMAGE_W_6 = 245;
const IMAGE_W_4 = 186.94737243652344;
const IMAGE_H = 144;

export const styleSheet = createMSBStyleSheet(({ColorGlobal, ColorAlias, Typography}) => ({
  flexGrow: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    marginHorizontal: SizeGlobal.Size400,
  },
  label: {
    ...Typography?.base_regular,
    color: ColorGlobal.Neutral800,
    width: '100%',
    textAlign: 'left',
    alignItems: 'flex-start',
  },
  pinContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  bg: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: SizeGlobal.Size400,
    marginTop: SizeGlobal.Size600,
    rowGap: SizeGlobal.Size600,
  },
  otpInputContainer: {
    width: '100%',
  },
  otpInput: {
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  otpInputLeft: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    columnGap: SizeGlobal.Size300,
  },
  keyIcon: {
    width: screenWidthToDesignRatio * IMAGE_W_6,
    height: screenWidthToDesignRatio * IMAGE_H,
    alignItems: 'center',
  },
  keyIcon4: {
    width: screenWidthToDesignRatio * IMAGE_W_4,
    height: screenWidthToDesignRatio * IMAGE_H,
    alignItems: 'center',
  },
  errorText: {
    ...Typography?.small_regular,
    marginTop: SizeGlobal.Size200,
    color: ColorAlias.TextError,
    textAlign: 'left',
  },
}));
