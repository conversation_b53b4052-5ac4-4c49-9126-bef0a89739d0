import {RootStackParamList} from '@app-navigation/types.ts';
import {MAX_ENTER_PIN} from '@constants';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {isLeft} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {CommonStateStatus} from '@entities/base/CommonState';
import {CardType} from '@entities/card/CardType.ts';
import {EAnnouncementResultsStatus, EAnnouncementResultsType} from '@entities/card/EAnnouncementResults.ts';
import {translate} from '@locales';
import {RouteProp, useRoute} from '@react-navigation/native';
import {debounceTimes, showPopup} from '@utils';
import useCardViewModel from '@view-models/useCardViewModel';
import {hostSharedModule} from 'msb-host-shared-module';
import {OtpInputRef} from 'msb-shared-component';
import {useCallback, useMemo, useRef, useState} from 'react';

const useReInputPinViewModel = () => {
  const {navigation, updateCardDetails, navigationToResultScreen} = useCardViewModel();

  const {params} = useRoute<RouteProp<RootStackParamList, 'ReInputPinScreen'>>();
  const {cardData, pin: prevPin} = params;

  const {isDebitCard, otpLength} = useMemo(() => {
    return {
      isDebitCard: cardData?.type === CardType.Debit,
      otpLength: cardData?.type === CardType.Debit ? 6 : 4,
    };
  }, [cardData?.type]);

  const isInit = useRef(false);
  const [error, setError] = useState<string | null>(null);
  const [countValid, setCountValid] = useState(1);

  const pinInputRef = useRef<OtpInputRef>(null);

  //Wait popup close completely
  const onGoBackDebounceTimes = useCallback(debounceTimes(navigation.goBack, 150), []);
  //Wait clear otp completely
  const onClearDebounceTimes = useCallback(
    debounceTimes(() => pinInputRef?.current?.clear(), 50),
    [],
  );

  const onFocusDebounceTimes = useCallback(
    debounceTimes(() => pinInputRef?.current?.focus(), 150),
    [],
  );

  let onConfirm = () => {
    hostSharedModule?.d?.domainService?.hidePopup();
    onGoBackDebounceTimes();
  };

  const handleValidInput = (pinStr: string) => {
    if (pinStr.length === otpLength && pinStr === prevPin) {
      setError(null);
      onCreateNewPin(pinStr);
    } else {
      const remainingAttempts = MAX_ENTER_PIN - countValid;

      if (remainingAttempts === 0) {
        let props = {
          confirmBtnText: translate('re_input_pin.close'),
          title: translate('re_input_pin.re_pin_max_error_title', {remainingAttempts: MAX_ENTER_PIN}),
          content: translate('re_input_pin.re_pin_max_error'),
          errorCode: 'RDB.CA.0010',
          onConfirm: onConfirm,
          // isKeepPopupWhenConfirm: true,
        };
        showPopup(props);
        return;
      }

      onClearDebounceTimes();
      setCountValid(countValid + 1);
      setError(translate('re_input_pin.re_pin_error', {remainingAttempts}));
      onFocusDebounceTimes();
    }
  };

  const onCreateNewPin = async (pinStr: string) => {
    hostSharedModule.d.domainService?.addSpinnerRequest();
    const createPinUseCase = DIContainer.getInstance().getCreatePinUseCase();
    const result = await createPinUseCase.execute({
      cardId: cardData.id,
      newPin: pinStr,
    });
    if (isLeft(result)) {
      const rsError = result.error;
      hostSharedModule.d.domainService.addSpinnerCompleted();

      if (rsError.code === MSBErrorCode.Unauthorized) {
        return;
      }
      navigationToResultScreen(cardData, EAnnouncementResultsStatus.fail, EAnnouncementResultsType.createNewPin, {
        errorCode: rsError.code,
      });
      return;
    }

    const data = result.data;

    if (data?.status === CommonStateStatus.SUCCESS && cardData?.id) {
      await updateCardDetails(cardData.id);
      navigationToResultScreen(cardData, EAnnouncementResultsStatus.success, EAnnouncementResultsType.createNewPin);
    }
    if (data?.status === CommonStateStatus.BACK) {
      pinInputRef?.current?.clear();
      onFocusDebounceTimes();
    }
    hostSharedModule.d.domainService.addSpinnerCompleted();
  };

  const value = useMemo(() => {
    return {cardData, isDebitCard, handleValidInput, error, otpLength, pinInputRef, isInit};
  }, [isDebitCard, otpLength, error, cardData]);

  return value;
};

export default useReInputPinViewModel;
