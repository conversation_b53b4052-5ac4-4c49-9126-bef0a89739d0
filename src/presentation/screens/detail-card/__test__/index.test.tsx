import CardModuleMain from '@/CardModuleMain';
import {TIME_24H_FORMAT} from '@constants';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {CardFeatureType} from '@domain/entities/card/CardFeatureType';
import {CardType} from '@domain/entities/card/CardType';
import {PaymentLevelSelector} from '@domain/entities/card/PaymentLevelSelector';
import {I18nKeys, translate} from '@locales';
import {
  advanceTimersByTime,
  asyncExpectElementOnScreenByTestId,
  expectElementOnScreenByTestId,
  fireEvent,
  getConfirmPopupButton,
  render,
  screen,
  scrollToCardFromList,
  shouldBeActiveTab,
  shouldShowPopup,
  shouldShowToast,
  tapToDebit,
  userEvent,
  within,
} from '@presentation/__test__/test-utils';
import {NavigationContainer} from '@react-navigation/native';
import {UserEventInstance} from '@testing-library/react-native/build/user-event/setup';
import {convertDateTimeTo} from '@utils';
import {MSB_POPUP_CANCEL_TEST_ID, MSB_POPUP_CONFIRM_TEST_ID} from 'mocks/popup/MSBPopup';
import {
  mockRDBForCheckTransactionStatus,
  mockResponseForCheckTransactionStatus,
} from 'mocks/service-apis/check-transaction-status';
import {mockResponseForCreateNewPin} from 'mocks/service-apis/create-new-pin';
import {
  getMaskedNumberFromId,
  mockCardDetailResponse,
  mockResponseForGetCardDetail,
  mockServerFailureForGetDetailCard,
} from 'mocks/service-apis/get-card-details';
import {mockResponseForGetListCard} from 'mocks/service-apis/get-list-cards';
import {mockListLinkAccountResponse, mockResponseForGetLinkAccount} from 'mocks/service-apis/get-list-link-account';
import {
  mock2ndPageFailedForGetListHistory,
  mockEmptyResponseForGetListHistory,
  mockInvalidResponseForGetListHistory,
  mockListTransactionHistoriesResponse,
  mockPaginationResponseForGetListHistory,
} from 'mocks/service-apis/get-list-transaction';
import {mockTransactionSigningSuccess} from 'mocks/transaction-signing';
import React from 'react';
import {goToDetailScreen, load2ndPageListHistory} from './test-utils';

describe('Detail Card Screen', () => {
  it('render correctly', async () => {
    mockResponseForGetListCard();
    mockResponseForGetCardDetail();
    mockPaginationResponseForGetListHistory({fixedPageSize: 3});

    render(
      <NavigationContainer>
        <CardModuleMain />
      </NavigationContainer>,
    );

    // check first card is visible
    await shouldBeActiveTab(CardType.Credit);

    const userSetup = userEvent.setup();
    const testCardId = '*********';
    const cardTouchable = screen.getByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
    expect(cardTouchable).toBeOnTheScreen();

    await userSetup.press(cardTouchable);
    expect(screen.getByTestId('cm.detail-card.list')).toBeOnTheScreen();
    expect(screen.getByText('MSB MasterCard mDigi')).toHaveProp('testID', 'cm.general-info-card.name');

    const firstTimeSection = convertDateTimeTo('2025-03-20T17:00:00Z', TIME_24H_FORMAT);
    expect(screen.findByText(firstTimeSection)).toBeTruthy();

    const showInstructionsBtn = expectElementOnScreenByTestId('cm.infor-credits-card.show-info-btn');
    await userSetup.press(showInstructionsBtn);

    expect(await screen.findByText(translate('detail_card.instrument_description'))).toBeOnTheScreen();
  });

  describe('active sub card from detail screen', () => {
    it('should display announcement successfully if active sub card successfully', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();
      mockPaginationResponseForGetListHistory({fixedPageSize: 3});
      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
        {},
      );

      // check first card is visible
      await shouldBeActiveTab(CardType.Credit);

      const userSetup = userEvent.setup();
      const testCardId = '*********';
      const testSubCardId = '246416830';

      await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
      const cardTouchable = await screen.findByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
      expect(cardTouchable).toBeOnTheScreen();

      await userSetup.press(cardTouchable);

      expect(await screen.findByText(getMaskedNumberFromId(testCardId))).toBeOnTheScreen();

      const showSubCardBottomSheetBtn = screen.getByTestId('cm.sub-card-owner.open-sub-card-detail-btn');
      await userSetup.press(showSubCardBottomSheetBtn);

      const subCardInBottomSheet1 = await screen.findByTestId(`cm.bottom-sheet.sub-card.246416830`);
      expect(subCardInBottomSheet1).toBeOnTheScreen();
      await userSetup.press(subCardInBottomSheet1);

      expect(subCardInBottomSheet1).not.toBeOnTheScreen();
      // Cancel active
      const closeModalBtn = within(await shouldShowPopup()).getByTestId(MSB_POPUP_CANCEL_TEST_ID);
      expect(closeModalBtn).toBeOnTheScreen();
      await userSetup.press(closeModalBtn);
      expect(closeModalBtn).not.toBeOnTheScreen();

      // Active sub-card again
      expect(showSubCardBottomSheetBtn).toBeOnTheScreen();

      // Pass default debounce for MSBTouchable
      await advanceTimersByTime(1500);
      await userSetup.press(await screen.findByTestId('cm.sub-card-owner.open-sub-card-detail-btn'));
      const subCardInBottomSheet2 = await screen.findByTestId(`cm.bottom-sheet.sub-card.${testSubCardId}`);
      expect(subCardInBottomSheet2).toBeOnTheScreen();
      await userSetup.press(subCardInBottomSheet2);
      const confirmModalBtn = getConfirmPopupButton(await shouldShowPopup());
      await userSetup.press(confirmModalBtn);

      // Announcement successful screen
      expect(await screen.findByText(translate('announcement_results.active_card_success_content'))).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testSubCardId));

      const goToDetailBtn = expectElementOnScreenByTestId('active_result_detail_button');
      await userSetup.press(goToDetailBtn);

      expect(goToDetailBtn).not.toBeOnTheScreen();

      // Current routes: [DetailScreen-{testCardId}, DetailScreen-{testSubCardId}]
      const subCardMaskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(subCardMaskedNumber.children).toContain(getMaskedNumberFromId(testSubCardId));

      const backIcon = expectElementOnScreenByTestId('action-back');
      await userSetup.press(backIcon);

      /**
       * Current routes: [DetailScreen-{testCardId}]
       * TH1.2: User đi entry point 2 - Bottom sheet thẻ phụ → KH click icon →  Back về màn chi tiết thẻ chính trước đó khách hàng đang xem.
       */
      expect(subCardMaskedNumber).not.toBeOnTheScreen();
      expect(await screen.findByText(getMaskedNumberFromId(testCardId))).toBeOnTheScreen();
      const cardDetailList = await screen.findByTestId('cm.detail-card.list');

      const backIconInMainCard = expectElementOnScreenByTestId('action-back');
      await userSetup.press(backIconInMainCard);

      /**
       * When user back from details screen
       * Go to list card screen
       */
      expect(cardDetailList).not.toBeOnTheScreen();
      expect(goToDetailBtn).not.toBeOnTheScreen();
      expect(await screen.findByTestId('cm.list-card.list')).toBeOnTheScreen();
    });

    it('should display announcement failed if active sub card failed', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();
      mockPaginationResponseForGetListHistory({fixedPageSize: 3});
      mockTransactionSigningSuccess();
      mockRDBForCheckTransactionStatus();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
        {},
      );

      // check first card is visible
      await shouldBeActiveTab(CardType.Credit);

      const userSetup = userEvent.setup();
      const testCardId = '*********';
      const testSubCardId = '246416830';

      await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
      const cardTouchable = await asyncExpectElementOnScreenByTestId(`cm.list-card.card-detail-btn.${testCardId}`);

      await userSetup.press(cardTouchable);

      expect(await screen.findByText('**** 1559')).toBeOnTheScreen();

      const showSubCardBottomSheetBtn = screen.getByTestId('cm.sub-card-owner.open-sub-card-detail-btn');
      await userSetup.press(showSubCardBottomSheetBtn);

      const subCardInBottomSheet1 = await asyncExpectElementOnScreenByTestId(
        `cm.bottom-sheet.sub-card.${testSubCardId}`,
      );
      await userSetup.press(subCardInBottomSheet1);

      expect(subCardInBottomSheet1).not.toBeOnTheScreen();
      const popup = await shouldShowPopup();

      const confirmModalBtn = within(popup).getByTestId(MSB_POPUP_CONFIRM_TEST_ID);
      await userSetup.press(confirmModalBtn);

      // Announcement failed screen
      expect(
        await screen.findByText(translate(`errors.${MSBErrorCode['RDB.CA.0018']}.description` as I18nKeys)),
      ).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testSubCardId));
      expect(screen.getByText('RDB.CA.0018')).toBeTruthy();
    });
  });

  describe('set auto debit payment', () => {
    const getSetPaymentAccountBtn = async (event: UserEventInstance, testCardId: string) => {
      const viewMoreBtn = screen.getByTestId('cm.feature-view.view-more-btn');
      await event.press(viewMoreBtn);

      const setAutoDebitPaymentBtn = await asyncExpectElementOnScreenByTestId(
        `cm.feature-view.${CardFeatureType.AutoDebit}`,
      );

      mockResponseForGetLinkAccount();
      await event.press(setAutoDebitPaymentBtn);

      const setPaymentAccountBtn = await asyncExpectElementOnScreenByTestId('cm.auto-debit-payment.confirm');
      expect(setPaymentAccountBtn).toBeDisabled();

      const detailCard = mockCardDetailResponse?.[testCardId as keyof typeof mockCardDetailResponse];
      const unSelectedPaymentLevel = expectElementOnScreenByTestId(
        `cm.select-payment-level.${PaymentLevelSelector.Minpayment}`,
      );

      // Check tagline for selected payment level
      if (detailCard?.paymentRate === PaymentLevelSelector.Minpayment) {
        expect(
          within(unSelectedPaymentLevel).getByText(translate('automatic_debit_payment.selecting')),
        ).toBeOnTheScreen();
      }
      await event.press(unSelectedPaymentLevel);

      // Check tagline for selected account payment
      if (detailCard?.rbsNumber) {
        expect(
          within(screen.getByTestId(`cm.account-payment.${detailCard.rbsNumber}`)).getByText(
            translate('update_debit_payment_account.linking'),
          ),
        ).toBeOnTheScreen();
      }
      const unSelectedAccount = expectElementOnScreenByTestId(
        `cm.account-payment.${mockListLinkAccountResponse.data[0].BBAN}`,
      );
      await event.press(unSelectedAccount);
      expect(setPaymentAccountBtn).toBeEnabled();

      return setPaymentAccountBtn;
    };
    it('should update auto debit payment successfully if everything is correct', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();
      mockPaginationResponseForGetListHistory({fixedPageSize: 3});

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
        {},
      );

      await shouldBeActiveTab(CardType.Credit);

      const userSetup = userEvent.setup();
      const testCardId = '*********';

      await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
      const cardTouchable = await asyncExpectElementOnScreenByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
      await userSetup.press(cardTouchable);

      expect(await screen.findByText(mockCardDetailResponse[testCardId].maskedNumber)).toBeOnTheScreen();

      const setPaymentAccountBtn = await getSetPaymentAccountBtn(userSetup, testCardId);

      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();

      await userSetup.press(setPaymentAccountBtn);
      // Announcement successful screen
      expect(screen.getByText(translate('announcement_results.auto_debit_payment_success'))).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));

      const goToDetailBtn = expectElementOnScreenByTestId('active_result_detail_button');
      await userSetup.press(goToDetailBtn);

      expect(goToDetailBtn).not.toBeOnTheScreen();
    });

    it('should update auto debit payment failed if check transaction status has error', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();
      mockPaginationResponseForGetListHistory({fixedPageSize: 3});

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
        {},
      );

      await shouldBeActiveTab(CardType.Credit);

      const userSetup = userEvent.setup();
      const testCardId = '*********';

      await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
      const cardTouchable = await asyncExpectElementOnScreenByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
      await userSetup.press(cardTouchable);

      const setPaymentAccountBtn = await getSetPaymentAccountBtn(userSetup, testCardId);

      mockTransactionSigningSuccess();
      mockRDBForCheckTransactionStatus();

      await userSetup.press(setPaymentAccountBtn);
      // Announcement failed screen
      expect(screen.getByText(translate('announcement_results.auto_debit_payment_error'))).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(mockCardDetailResponse[testCardId].maskedNumber);
      expect(screen.getByText('RDB.CA.0018')).toBeTruthy();

      const closeBtn = expectElementOnScreenByTestId('active_result_close_button');
      await userSetup.press(closeBtn);

      expect(closeBtn).not.toBeOnTheScreen();
    });
  });

  describe('update payment account', () => {
    it('should update payment account successfully account if everything is correct', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();
      mockPaginationResponseForGetListHistory({fixedPageSize: 3});

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
        {},
      );

      await shouldBeActiveTab(CardType.Credit);

      const userSetup = userEvent.setup();
      await tapToDebit(userSetup);

      // check debit tab is active
      await shouldBeActiveTab(CardType.Debit);

      const testCardId = '*********';

      await scrollToCardFromList(userSetup, testCardId, CardType.Debit);
      const cardTouchable = await asyncExpectElementOnScreenByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
      await userSetup.press(cardTouchable);

      expect(await screen.findByText(mockCardDetailResponse[testCardId].maskedNumber)).toBeOnTheScreen();

      /**
       *  Only 2 feature is available for this card
       *  Don't have show more button
       **/
      const viewMoreBtn = screen.queryByTestId('cm.feature-view.view-more-btn');
      expect(viewMoreBtn).toBeFalsy();

      const setLinkAccountBtn = await asyncExpectElementOnScreenByTestId(
        `cm.feature-view.${CardFeatureType.LinkedAccount}`,
      );

      mockResponseForGetLinkAccount();
      await userSetup.press(setLinkAccountBtn);

      const setPaymentAccountBtn = await asyncExpectElementOnScreenByTestId('cm.update-debit-payment-account.confirm');
      expect(setPaymentAccountBtn).toBeDisabled();

      const unSelectedAccount = expectElementOnScreenByTestId(
        `cm.account-payment.${mockListLinkAccountResponse.data[0].BBAN}`,
      );
      await userSetup.press(unSelectedAccount);
      expect(setPaymentAccountBtn).toBeEnabled();

      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();

      await userSetup.press(setPaymentAccountBtn);
      // Announcement successful screen
      expect(
        screen.getByText(translate('announcement_results.update_debit_payment_account_success')),
      ).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));

      const goToDetailBtn = expectElementOnScreenByTestId('active_result_detail_button');
      await userSetup.press(goToDetailBtn);

      expect(goToDetailBtn).not.toBeOnTheScreen();
      expect(await screen.findByText(mockCardDetailResponse[testCardId].maskedNumber)).toBeOnTheScreen();
    });

    it('should update payment account failed if check transaction status has error', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();
      mockPaginationResponseForGetListHistory({fixedPageSize: 3});

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
        {},
      );

      await shouldBeActiveTab(CardType.Credit);

      const userSetup = userEvent.setup();
      await tapToDebit(userSetup);

      // check debit tab is active
      await shouldBeActiveTab(CardType.Debit);

      const testCardId = '*********';

      await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
      const cardTouchable = await asyncExpectElementOnScreenByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
      await userSetup.press(cardTouchable);

      const setLinkAccountBtn = await asyncExpectElementOnScreenByTestId(
        `cm.feature-view.${CardFeatureType.LinkedAccount}`,
      );

      mockResponseForGetLinkAccount();
      await userSetup.press(setLinkAccountBtn);

      const setPaymentAccountBtn = await asyncExpectElementOnScreenByTestId('cm.update-debit-payment-account.confirm');
      expect(setPaymentAccountBtn).toBeDisabled();

      const unSelectedAccount = expectElementOnScreenByTestId(
        `cm.account-payment.${mockListLinkAccountResponse.data[0].BBAN}`,
      );
      await userSetup.press(unSelectedAccount);
      expect(setPaymentAccountBtn).toBeEnabled();

      mockTransactionSigningSuccess();
      mockRDBForCheckTransactionStatus();

      await userSetup.press(setPaymentAccountBtn);
      // Announcement failed screen
      expect(
        screen.getByText(translate(`errors.${MSBErrorCode['RDB.CA.0018']}.description` as I18nKeys)),
      ).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(mockCardDetailResponse[testCardId].maskedNumber);
      expect(screen.getByText('RDB.CA.0018')).toBeTruthy();

      const closeBtn = expectElementOnScreenByTestId('active_result_close_button');
      await userSetup.press(closeBtn);

      expect(closeBtn).not.toBeOnTheScreen();
    });
  });

  describe('create new pin', () => {
    it('should create new pin successfully if everything is correct', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
      );

      const userSetup = userEvent.setup();
      await shouldBeActiveTab(CardType.Credit);

      const testCardId = '*********';
      const cardTouchable = await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
      await userSetup.press(cardTouchable);

      expect(await screen.findByText(mockCardDetailResponse[testCardId].maskedNumber)).toBeOnTheScreen();
      const viewMoreBtn = screen.getByTestId('cm.feature-view.view-more-btn');
      await userSetup.press(viewMoreBtn);

      const bottomSheet = await screen.findByTestId('cm.bottom-sheet.features-view-host');
      const createNewPinBtn = within(bottomSheet).getByTestId(`cm.feature-view.${CardFeatureType.NewPin}`);
      await userSetup.press(createNewPinBtn);

      const createPinTitle = await screen.findByText(translate('create_pin.create_new_pin'));
      expect(createPinTitle).toBeOnTheScreen();
      const otpInputInCreateScreen = screen.getByTestId('otp-input-hidden');
      expect(otpInputInCreateScreen).toBeOnTheScreen();
      fireEvent.changeText(otpInputInCreateScreen, '1221');

      const reInputPinTitle = await screen.findByText(translate('re_input_pin.title'));
      expect(reInputPinTitle).toBeOnTheScreen();
      const otpInputInReInputScreen = screen.getByTestId('otp-input-hidden');
      expect(otpInputInReInputScreen).toBeOnTheScreen();

      fireEvent.changeText(otpInputInReInputScreen, '1221');

      mockTransactionSigningSuccess();
      mockResponseForCheckTransactionStatus();
      mockResponseForCreateNewPin();

      const dupOtpInputInReInputScreen = screen.getByTestId('otp-input-hidden');
      expect(dupOtpInputInReInputScreen).toBeOnTheScreen();
      fireEvent.changeText(dupOtpInputInReInputScreen, '123123');

      // Announcement successful screen
      const successContent = await screen.findByText(translate('announcement_results.create_new_pin_success'));
      expect(successContent).toBeOnTheScreen();
      const maskedNumber = screen.getByTestId('cm.general-info-card.marked-number');
      expect(maskedNumber.children).toContain(getMaskedNumberFromId(testCardId));

      const backIcon = expectElementOnScreenByTestId('active_result_detail_button');
      await userSetup.press(backIcon);
      /**
       * When user back from announcement failure screen
       * Go to detail card screen
       */
      const cardDetailList = await screen.findByTestId('cm.detail-card.list');
      expect(cardDetailList).toBeOnTheScreen();
    });

    it('should show announcement failed screen if create new pin failed and go back to details after closing', async () => {
      mockResponseForGetListCard();
      mockResponseForGetCardDetail();
      mockTransactionSigningSuccess();
      mockRDBForCheckTransactionStatus();

      render(
        <NavigationContainer>
          <CardModuleMain />
        </NavigationContainer>,
      );

      const userSetup = userEvent.setup();
      await shouldBeActiveTab(CardType.Credit);

      const testCardId = '246202580';
      const cardTouchable = await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
      await userSetup.press(cardTouchable);

      const viewMoreBtn = await screen.findByTestId('cm.feature-view.view-more-btn');
      await userSetup.press(viewMoreBtn);

      const bottomSheet = await screen.findByTestId('cm.bottom-sheet.features-view-host');
      const createNewPinBtn = within(bottomSheet).getByTestId(`cm.feature-view.${CardFeatureType.NewPin}`);
      await userSetup.press(createNewPinBtn);

      const otpInputInCreateScreen = await screen.findByTestId('otp-input-hidden');
      expect(otpInputInCreateScreen).toBeOnTheScreen();

      fireEvent.changeText(otpInputInCreateScreen, '1221');

      const reInputPinTitle = await screen.findByText(translate('re_input_pin.title'));
      expect(reInputPinTitle).toBeOnTheScreen();
      const otpInputInReInputScreen = screen.getByTestId('otp-input-hidden');
      expect(otpInputInReInputScreen).toBeOnTheScreen();

      fireEvent.changeText(otpInputInReInputScreen, '1221');

      // Announcement successful screen
      const errorContent = await screen.findByText(
        translate(`errors.${MSBErrorCode['RDB.CA.0018']}.description` as I18nKeys),
      );
      expect(errorContent).toBeOnTheScreen();

      const backIcon = expectElementOnScreenByTestId('active_result_close_button');
      await userSetup.press(backIcon);
      /**
       * When user back from announcement failure screen
       * Go to detail card screen
       */
      const cardDetailList = await screen.findByTestId('cm.detail-card.list');
      expect(cardDetailList).toBeOnTheScreen();
    });
  });

  describe('pagination', () => {
    it('loading page 2 when user scroll to bottom', async () => {
      await load2ndPageListHistory();

      const scrollableView = screen.getByTestId('cm.detail-card.list');
      fireEvent(scrollableView, 'endReached', {
        distanceFromEnd: 100,
      });

      // expect render last transaction in 2nd page
      expect(scrollableView.props.data.length - 2).toEqual(
        mockListTransactionHistoriesResponse.transactionHistories.length,
      );
    });

    it('should refresh data when user pull to refresh', async () => {
      await load2ndPageListHistory();

      const prevScrollableView = screen.getByTestId('cm.detail-card.list');
      const refreshControl = prevScrollableView.props.refreshControl;
      refreshControl.props.onRefresh();

      const scrollableView = await screen.findByTestId('cm.detail-card.list');
      expect(scrollableView.props.data.length - 2).toEqual(3);
    });

    it('should show error toast when 2nd page loading failed', async () => {
      const testCardId = '246244460';
      await goToDetailScreen({testCardId, mockFn: mock2ndPageFailedForGetListHistory});

      const scrollableView = await screen.findByTestId('cm.detail-card.list');
      expect(scrollableView).toBeOnTheScreen();

      fireEvent(scrollableView, 'endReached', {
        distanceFromEnd: 100,
      });
      const toast = await shouldShowToast();
      expect(within(toast).getByText(translate('error_desc'))).toBeOnTheScreen();
    });

    it('should show empty view if not have transaction history', async () => {
      const testCardId = '246244460';
      await goToDetailScreen({testCardId, mockFn: mockEmptyResponseForGetListHistory});

      const scrollableView = await screen.findByTestId('cm.detail-card.list');
      const emptyState = within(scrollableView).getByTestId('cm.history-card.empty-state');
      expect(within(emptyState).getByText(translate('history_card.no_data'))).toBeOnTheScreen();
    });

    it('should show error view if not have transaction history', async () => {
      const testCardId = '246244460';
      await goToDetailScreen({testCardId, mockFn: mockInvalidResponseForGetListHistory});

      const scrollableView = await screen.findByTestId('cm.detail-card.list');
      const emptyState = within(scrollableView).getByTestId('cm.history-card.empty-state');
      expect(within(emptyState).getByText(translate('error_title'))).toBeOnTheScreen();
    });

    it('should show error view if get details failed', async () => {
      const testCardId = '246244460';
      await goToDetailScreen({
        testCardId,
        mockFn: () => {
          mockServerFailureForGetDetailCard();
          mockPaginationResponseForGetListHistory();
        },
      });

      const scrollableView = await screen.findByTestId('cm.detail-card.list');
      const emptyState = within(scrollableView).getByTestId('cm.history-card.empty-state');
      expect(within(emptyState).getByText(translate('error_title'))).toBeOnTheScreen();

      // Retry successfully
      mockResponseForGetCardDetail();
      const refreshControl = scrollableView.props.refreshControl;
      refreshControl.props.onRefresh();
      const subCardMaskedNumber = await screen.findByTestId('cm.general-info-card.marked-number');
      expect(subCardMaskedNumber.children).toContain(getMaskedNumberFromId(testCardId));
    });
  });
});
