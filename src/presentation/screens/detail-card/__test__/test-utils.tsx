import CardModuleMain from '@/CardModuleMain';
import {CardType} from '@entities/card/CardType';
import {
  fireEvent,
  render,
  screen,
  scrollToCardFromList,
  shouldBeActiveTab,
  userEvent,
  within,
} from '@presentation/__test__/test-utils';
import {NavigationContainer} from '@react-navigation/native';
import {mockResponseForGetCardDetail} from 'mocks/service-apis/get-card-details';
import {mockResponseForGetListCard} from 'mocks/service-apis/get-list-cards';
import {
  mockListTransactionHistoriesResponse,
  mockPaginationResponseForGetListHistory,
} from 'mocks/service-apis/get-list-transaction';

export const goToDetailScreen = async ({testCardId, mockFn}: {testCardId: string; mockFn?: () => void}) => {
  mockResponseForGetListCard();
  mockResponseForGetCardDetail();
  (mockFn ?? mockPaginationResponseForGetListHistory)({fixedPageSize: 3});

  render(
    <NavigationContainer>
      <CardModuleMain />
    </NavigationContainer>,
    {},
  );

  // check first card is visible
  await shouldBeActiveTab(CardType.Credit);

  const userSetup = userEvent.setup();

  await scrollToCardFromList(userSetup, testCardId, CardType.Credit);
  const cardTouchable = await screen.findByTestId(`cm.list-card.card-detail-btn.${testCardId}`);
  expect(cardTouchable).toBeOnTheScreen();

  await userSetup.press(cardTouchable);
};

export const load2ndPageListHistory = async () => {
  const testCardId = '246244460';

  await goToDetailScreen({testCardId});

  const scrollableView = await screen.findByTestId('cm.detail-card.list');
  const refreshControl = scrollableView.props.refreshControl;
  expect(refreshControl).toBeTruthy();

  fireEvent(scrollableView, 'endReached', {
    distanceFromEnd: 100,
  });

  const lastTransaction = await within(screen.getByTestId('cm.detail-card.list')).findByTestId(
    `cm.history-card.history-item.${mockListTransactionHistoriesResponse.transactionHistories[5].rrn}`,
  );
  expect(lastTransaction).toBeOnTheScreen();
  /**
   * 2 header section dd/mm/yyyy
   */
  expect(scrollableView.props.data.length - 2).toEqual(
    mockListTransactionHistoriesResponse.transactionHistories.length,
  );
};
