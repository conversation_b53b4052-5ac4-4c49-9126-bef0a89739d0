import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(
  ({ColorAlias, ColorCard, ColorDataView, ColorField, ColorGlobal, ColorHeader, SizeGlobal, Typography}) => ({
    bgContainer: {
      flex: 1,
      alignItems: 'flex-start',
      justifyContent: 'flex-start',
    },
    loadingContainer: {
      padding: 100,
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    loading: {
      color: ColorAlias.SurfaceBrand,
    },
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    historyContainer: {
      marginVertical: 24,
    },
    buttonStyle: {
      margin: 8,
      padding: 8,
      backgroundColor: 'purple',
      borderRadius: 8,
      borderWidth: 1,
    },
    buttonTextStyle: {
      ...Typography?.base_semiBold,
      color: 'white',
    },
    headerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      paddingVertical: 8,
    },
    backButton: {
      width: 24,
      height: 24,
      padding: 8,
      tintColor: ColorHeader.IconDefault,
    },
    title: {
      ...Typography?.title_semiBold,
      color: '#091E42',
    },
    leftButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-start',
    },
    rowFeaturesContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      padding: 8,
    },
    card: {
      width: '100%',
      backgroundColor: ColorGlobal.NeutralWhite,
      padding: 16,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: ColorField.BorderDefault, // Light gray border
      marginTop: 4,
      alignSelf: 'center',
      flexDirection: 'row',
      alignItems: 'center',
    },
    scrollContainer: {
      width: '100%',
      flexGrow: 1,
      flexDirection: 'column',
      paddingHorizontal: 16,
      paddingVertical: 24,
    },
    iconBackButton: {
      width: 36,
      height: 36,
    },
    instrumentDescTxt: {
      ...Typography?.base_regular,
      color: ColorCard.TextContent,
    },
    arrowImage: {
      width: 24,
      height: 24,
    },
    contentContainerStyle: {
      paddingBottom: 36,
    },
    rowCardInfoContainer: {
      width: '100%',
      marginTop: 4,
      flexDirection: 'row',
      alignContent: 'center',
    },
    numberCardValue: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
    },
    subNameContainer: {flexDirection: 'column', flex: 1},
    subNameRow: {flexDirection: 'row'},
    subNameTitle: {
      ...Typography?.small_regular,
      color: ColorDataView.TextSub,
      marginEnd: 16,
    },
    subNameValue: {
      ...Typography?.small_medium,
      color: ColorDataView.TextMain,
      flex: 1,
      textAlign: 'right',
    },
    instrumentDescriptionContainer: {
      paddingTop: SizeGlobal.Size400,
      paddingHorizontal: SizeGlobal.Size400,
    },
    creditCardInfo: {flexDirection: 'column'},
  }),
);

export const infoCreditStyleSheet = createMSBStyleSheet(({ColorDataView, Typography}) => ({
  container: {
    flexDirection: 'column',
    width: '100%',
    gap: 12,
  },
  instrumentTitle: {
    ...Typography?.small_regular,
    color: ColorDataView.TextSub,
    paddingRight: 4,
  },
  horizontalLine: {
    marginVertical: 16,
  },
  valueContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  valueTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoTooltipImage: {
    width: 16,
    height: 16,
  },
  moneyView: {
    flex: 1,
    justifyContent: 'flex-end',
  },
}));

export const featureBlockStyleSheet = createMSBStyleSheet(({SizeAlias, Typography}) => ({
  container: {marginTop: 24, borderRadius: SizeAlias.Radius4, borderWidth: 0},
  viewMoreContainer: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  gridMoreIcon: {
    width: 24,
    height: 24,
  },
  moreTxt: {
    ...Typography?.caption_medium,
    paddingStart: 8,
    color: '#091E42',
  },
  horizontalLine: {
    marginTop: 8,
  },
}));

export const subCardContainerStyleSheet = createMSBStyleSheet(({SizeAlias, ColorGlobal, Typography}) => ({
  container: {
    flexDirection: 'row',
    borderRadius: SizeAlias.Radius3,
    backgroundColor: ColorGlobal.Blue50,
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 16,
  },
  countSubCardContainer: {flexDirection: 'row', alignItems: 'center', flex: 1},
  image: {width: 32, height: 32},
  text: {...Typography?.small_medium, color: '#091E42', marginStart: 12},
}));
