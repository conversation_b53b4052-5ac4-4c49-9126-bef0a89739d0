import {ApplicationScreenProps, RootStackParamList} from '@app-navigation/types.ts';
import {HISTORY_PAGE_SIZE, SERVER_DATE_FORMAT} from '@constants';
import {isLeft} from '@core/ResultState.ts';
import {DIContainer} from '@di/DIContainer.ts';
import {CardFeature} from '@entities/card/CardFeature.ts';
import {History, HistoryItem, HistoryItemType, ListHistory} from '@entities/card/History.ts';
import {translate} from '@locales';
import {useTransactionActions} from '@presentation/store/Transaction';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useCardSelectors} from '@store/Card';
import {createLazyLoadingManager} from '@utils';
import useCardViewModel from '@view-models/useCardViewModel';
import moment from 'moment';
import {hostSharedModule, ToastType} from 'msb-host-shared-module';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';

const DEFAULT_METADATA = {
  totalCount: Number.POSITIVE_INFINITY,
  currentCount: 0,
  processing: false,
  lastSection: undefined,
};

const useDetailCardViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const route = useRoute<ApplicationScreenProps<'DetailCardScreen'>['route']>();
  const {id} = route.params;

  const {handleCardFeatureAction} = useCardViewModel();

  /**
   * Lịch sử giao dịch sẽ không cần global state nên sẽ sử dụng state mặc định
   */
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  const [loadingHistories, setLoadingHistories] = useState(true);
  const [loadingMoreHistories, setLoadingMoreHistories] = useState(false);
  const [hasError, setHasError] = useState(false);

  const loadingManager = useMemo(() => createLazyLoadingManager(setLoadingHistories, setLoadingMoreHistories), []);

  const metadata = useRef<{
    totalCount: number;
    currentCount: number;
    processing: boolean;
    lastSection: string | undefined;
  }>({...DEFAULT_METADATA});

  // Access the store and initialize for this card ID
  const {
    refreshing,
    loading,
    error,
    cardModel,
    setCardModel,
    setLoading,
    setError,
    setRefreshing,
    initializeCard,
    resetCard,
  } = useCardSelectors(id);
  const {resetFormFilterTransaction} = useTransactionActions();

  useEffect(() => {
    initializeCard(id);

    getDetailCard();
    getHistories(true);

    return () => {
      resetCard(id);
      resetFormFilterTransaction(id);
    };
  }, []);

  const showLoading = useCallback(() => {
    if (refreshing) {
      return;
    }
    setLoading(id, true);
  }, [refreshing, id, setLoading]);

  const hideLoading = useCallback(() => {
    setLoading(id, false);
    setRefreshing(id, false);
  }, [id, setLoading, setRefreshing]);

  const getDetailCard = useCallback(async () => {
    showLoading();
    setError(id, false);
    const getDetailsUseCase = DIContainer.getInstance().getGetDetailCardUseCase();

    const result = await getDetailsUseCase.execute(id);

    hideLoading();
    if (isLeft(result)) {
      const error_ = result.error;
      console.error('getDetailCard', error_);
      setError(id, true);
      setCardModel(id, undefined);
      return;
    }

    const data = result.data;
    if (data) {
      setCardModel(id, data);
    }
  }, [id, showLoading, hideLoading, setCardModel, setError]);

  const handleCardFunction = useCallback(
    (feature?: CardFeature) => {
      if (!cardModel || !feature) {
        return;
      }

      hostSharedModule.d.domainService.hideBottomSheet();
      handleCardFeatureAction(feature, cardModel);
    },
    [cardModel, handleCardFeatureAction],
  );

  const resetHistoryState = useCallback(() => {
    metadata.current = {...DEFAULT_METADATA};
    if (historyItems.length !== 0) {
      setHistoryItems([]);
    }
    if (hasError) {
      setHasError(false);
    }
  }, [historyItems, hasError]);

  const shouldFetchMoreHistories = useCallback(() => {
    return metadata.current.currentCount < metadata.current.totalCount && !metadata.current.processing;
  }, []);

  const fetchHistoryData = useCallback(async () => {
    const toDate = moment();
    const fromDate = moment().add(-90, 'days');
    const page = Math.ceil(metadata.current.currentCount / HISTORY_PAGE_SIZE) + 1;

    return await DIContainer.getInstance()
      .getGetListHistoryUseCase()
      .execute({
        cardId: id,
        page,
        size: HISTORY_PAGE_SIZE,
        from: fromDate.format(SERVER_DATE_FORMAT),
        to: toDate.format(SERVER_DATE_FORMAT),
      });
  }, [id]);

  const handleHistoryError = useCallback(
    (isLoadingFirst: boolean) => {
      const page = Math.ceil(metadata.current.currentCount / HISTORY_PAGE_SIZE) + 1;
      if (page > 1) {
        hostSharedModule.d.domainService.showToast({
          message: translate('error_desc'),
          type: ToastType.ERROR,
        });
      } else {
        setHasError(true);
      }
      loadingManager.hideLoading(isLoadingFirst);
      metadata.current.processing = false;
    },
    [loadingManager],
  );

  const updateHistoryMetadata = useCallback((listHistory: ListHistory) => {
    metadata.current.currentCount += listHistory.data.length;
    metadata.current.totalCount = listHistory.metadata.totalItems;
  }, []);

  const updateHistoryItems = useCallback(
    (listHistory: ListHistory, isLoadingFirst: boolean) => {
      const newHistoryItems = isLoadingFirst ? [] : [...historyItems];

      for (const history of listHistory.data) {
        const isNewSection = !metadata.current.lastSection || metadata.current.lastSection !== history.transDay;
        if (isNewSection) {
          newHistoryItems.push({
            type: HistoryItemType.HEADER_SECTION,
            headerTitle: history.transDay,
          });
          metadata.current.lastSection = history.transDay;
        }

        newHistoryItems.push({
          type: HistoryItemType.ITEM,
          data: history,
          isFirstSection: isNewSection,
        });
      }

      setHistoryItems(newHistoryItems);
    },
    [historyItems],
  );

  const getHistories = useCallback(
    async (forceRefresh = false) => {
      if (forceRefresh) {
        resetHistoryState();
      }

      if (!shouldFetchMoreHistories()) {
        return;
      }

      metadata.current.processing = true;
      const isLoadingFirst = metadata.current.totalCount === Number.POSITIVE_INFINITY;
      loadingManager.showLoading(isLoadingFirst);

      const result = await fetchHistoryData();

      if (isLeft(result)) {
        handleHistoryError(isLoadingFirst);
        return;
      }

      const listHistory =
        result.data ??
        ({
          data: [],
          metadata: {
            totalItems: metadata.current.totalCount ?? 0,
          },
        } as ListHistory);

      updateHistoryMetadata(listHistory);
      updateHistoryItems(listHistory, isLoadingFirst);

      loadingManager.hideLoading(isLoadingFirst);
      metadata.current.processing = false;
    },
    [
      fetchHistoryData,
      handleHistoryError,
      loadingManager,
      resetHistoryState,
      shouldFetchMoreHistories,
      updateHistoryItems,
      updateHistoryMetadata,
    ],
  );

  const onRefresh = useCallback(() => {
    if (refreshing) {
      return;
    }

    setRefreshing(id, true);
    getDetailCard();
    getHistories(true);
  }, [refreshing, id, getDetailCard, getHistories]);

  const onEndReached = useCallback(() => {
    if (metadata.current.currentCount < HISTORY_PAGE_SIZE) {
      return;
    }
    getHistories();
  }, [getHistories]);

  const onNavToDetailScreen = useCallback(
    (history?: History) => {
      if (!history) {
        return;
      }
      navigation.navigate('HistoryDetailScreen', {history});
    },
    [navigation],
  );

  return {
    cardId: id,
    error,
    loading,
    cardModel,
    navigation,
    refreshing,
    historyItems,
    loadingHistories,
    loadingMoreHistories,
    hasError,
    onRefresh,
    handleCardFunction,
    handleCardFeatureAction,
    onEndReached,
    resetFormFilterTransaction,
    onNavToDetailScreen,
  };
};

export default useDetailCardViewModel;
