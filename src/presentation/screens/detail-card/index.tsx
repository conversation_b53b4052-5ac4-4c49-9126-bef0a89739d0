import DetailCardSkeleton from '@components/loading-skeleton/DetailCardSkeleton';
import HistoryCardHeader from '@components/transaction-history/HistoryCardHeader';
import HistoryCardItem from '@components/transaction-history/HistoryCardItem';
import HistoryFooter from '@components/transaction-history/HistoryFooter';
import {HistoryItem, HistoryItemType} from '@entities/card/History';
import {translate} from '@locales';
import {MSBPage, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FlatList, RefreshControl} from 'react-native';
import HistoryHeaderComponent from './components/transaction-history/HistoryHeaderComponent';
import useDetailCardViewModel from './hook';
import {styleSheet} from './styles';

const DetailCardScreen = () => {
  const {styles} = useMSBStyles(styleSheet);

  const {
    error,
    loading,
    cardModel,
    navigation,
    refreshing,
    historyItems,
    loadingHistories,
    loadingMoreHistories,
    hasError,
    onRefresh,
    handleCardFunction,
    handleCardFeatureAction,
    onEndReached,
    resetFormFilterTransaction,
    onNavToDetailScreen,
  } = useDetailCardViewModel();

  const onSearchTransaction = useCallback(() => {
    if (!cardModel?.id) {
      return;
    }
    resetFormFilterTransaction(cardModel.id);
    navigation.navigate('TransactionFilterScreen', {cardId: cardModel?.id});
  }, [cardModel?.id]);

  const renderItem = useCallback(
    (data: {item: HistoryItem; index: number}) => {
      if (data.item.type === HistoryItemType.HEADER_SECTION) {
        return <HistoryCardHeader title={data.item.headerTitle} />;
      }

      return (
        <HistoryCardItem
          testID={`cm.history-card.history-item.${data.item.data.rrn}`}
          isLast={data.index === historyItems.length - 1}
          loading={loadingMoreHistories}
          historyItem={data.item}
          onPress={() => onNavToDetailScreen(data.item.data)}
        />
      );
    },
    [loadingMoreHistories, historyItems.length, onNavToDetailScreen],
  );

  return (
    <MSBPage
      testID="cm.detailCardScreen"
      headerProps={{title: translate('detail_card.detail_title'), hasBack: true}}
      isScrollable={false}>
      <DetailCardSkeleton isLoading={loading}>
        <FlatList
          testID="cm.detail-card.list"
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
          style={styles.scrollContainer}
          contentContainerStyle={styles.contentContainerStyle}
          showsVerticalScrollIndicator={false}
          data={historyItems}
          extraData={[loadingHistories, loadingMoreHistories, cardModel, hasError, error]}
          ListHeaderComponent={
            <HistoryHeaderComponent
              hasError={error}
              isEmpty={historyItems.length === 0}
              cardModel={cardModel}
              handleCardFunction={handleCardFunction}
              handleCardFeatureAction={handleCardFeatureAction}
              navigation={navigation}
              onSearchTransaction={onSearchTransaction}
            />
          }
          ListFooterComponent={
            <HistoryFooter
              hasError={hasError}
              isEmpty={historyItems.length === 0}
              loading={loadingHistories}
              loadingMore={loadingMoreHistories}
            />
          }
          keyExtractor={(item, index) => `history-${item.type}-${index}`}
          renderItem={renderItem}
          onEndReachedThreshold={0.25}
          onEndReached={onEndReached}
        />
      </DetailCardSkeleton>
    </MSBPage>
  );
};

export default DetailCardScreen;
