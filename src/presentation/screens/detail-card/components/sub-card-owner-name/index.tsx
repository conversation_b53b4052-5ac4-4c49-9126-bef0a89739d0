import {Block} from '@components/block';
import HorizontalDivider from '@components/line-view/Divider';
import {Text} from '@components/text';
import {translate} from '@locales';
import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from '../../styles';
import {SubCardOwnerNameProps} from './type';

export const SubCardOwnerName: React.FC<SubCardOwnerNameProps> = ({ownerName}) => {
  const {styles} = useMSBStyles(styleSheet);

  return (
    <Block gap={16}>
      <HorizontalDivider marginTop={16} />
      <View style={styles.subNameRow}>
        <Text testID="cm.sub-card-owner.title" style={styles.subNameTitle}>
          {translate('detail_card.owner_card_name_title')}
        </Text>
        <Text testID="cm.sub-card-owner.owner-name" style={styles.subNameValue}>
          {ownerName.toUpperCase()}
        </Text>
      </View>
    </Block>
  );
};
