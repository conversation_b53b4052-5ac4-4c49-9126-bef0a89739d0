import {Card} from '@domain/entities/card/Card';
import {CardFeature} from '@domain/entities/card/CardFeature';
import {SubCard} from '@entities/card/SubCard';
import {RootStackParamList} from '@presentation/navigation/types';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

export type SubCardBlockViewProps = {
  subCards: SubCard[];
  navigation: NativeStackNavigationProp<RootStackParamList>;
  handleCardFeatureAction: (feature: CardFeature, card: Card) => void;
};
