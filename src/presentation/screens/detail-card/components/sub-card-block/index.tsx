import showSubCardBottomSheet from '@app-screens/sub-card-bottom-sheet';
import {Text} from '@components/text';
import {mapSubCardToCard} from '@data/mappers/card/CardMapper';
import {isNeedActiveCard} from '@domain/entities/card/CardDomainStatus';
import {CardFeatureType} from '@domain/entities/card/CardFeatureType';
import {translate} from '@locales';
import {hostSharedModule} from 'msb-host-shared-module';
import {MSBIcon, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {subCardContainerStyleSheet} from '../../styles';
import {SubCardBlockViewProps} from './type';

export const SubCardBlockView: React.FC<SubCardBlockViewProps> = ({subCards, navigation, handleCardFeatureAction}) => {
  const {styles: subCardContainerStyle} = useMSBStyles(subCardContainerStyleSheet);

  const onPress = () => {
    const hasCardNeedingActivation = subCards.findIndex(card => isNeedActiveCard(card.status));

    /**
     * Nếu có ít nhất một thẻ phụ cần kích hoạt thì sẽ hiện lên bottom sheet
     */
    if (subCards.length > 1 || hasCardNeedingActivation !== -1) {
      showSubCardBottomSheet(subCards, subCard => {
        if (isNeedActiveCard(subCard.status)) {
          hostSharedModule.d.domainService.hideBottomSheet();
          handleCardFeatureAction(
            {
              id: CardFeatureType.ChangeStatus,
              isEmpty: true,
              priority: 0,
            },
            mapSubCardToCard(subCard),
          );
        } else {
          navigation.push('DetailCardScreen', {id: subCard.id});
        }
      });
    } else {
      navigation.push('DetailCardScreen', {id: subCards[0]?.id});
    }
  };

  return (
    <MSBTouchable
      testID="cm.sub-card-owner.open-sub-card-detail-btn"
      style={subCardContainerStyle.container}
      onPress={onPress}>
      <View style={subCardContainerStyle.countSubCardContainer}>
        <MSBIcon icon="tone-card-stack" iconSize={32} onIconClick={onPress} />
        <Text style={subCardContainerStyle.text}>
          {translate('detail_card.sub_card_title') + ' ' + subCards.length}
        </Text>
      </View>
      <MSBIcon icon="right" iconSize={32} onIconClick={onPress} />
    </MSBTouchable>
  );
};
