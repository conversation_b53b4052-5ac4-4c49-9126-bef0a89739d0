import RoundedBackgroundView from '@components/background-custom-view';
import {showCardFeaturesBottomSheet} from '@components/bottom-sheet/feature-views';
import {FeaturesView} from '@components/feature-view';
import HorizontalLineView from '@components/line-view';
import {Text} from '@components/text';
import {translate} from '@locales';
import {SizedBox} from '@presentation/components/sized-box';
import {MSBIcon, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {featureBlockStyleSheet} from '../../styles';
import {FeaturesBlockProps} from './type';

export const FeaturesBlock: React.FC<FeaturesBlockProps> = ({features = [], onCardFunction}) => {
  const {styles: featureBlockStyle} = useMSBStyles(featureBlockStyleSheet);

  const renderViewMoreButton = () => (
    <MSBTouchable
      testID={'cm.feature-view.view-more-btn'}
      onPress={() => {
        showCardFeaturesBottomSheet(features, onCardFunction);
      }}>
      <View style={featureBlockStyle.viewMoreContainer}>
        <MSBIcon icon="tone-grid-layout-more" iconSize={24} disable />
        <Text style={featureBlockStyle.moreTxt}>{translate('detail_card.view_more')}</Text>
      </View>
    </MSBTouchable>
  );

  return (
    <RoundedBackgroundView style={featureBlockStyle.container}>
      <FeaturesView features={features} onPress={onCardFunction} />
      {features.length > 3 ? (
        <>
          <HorizontalLineView style={featureBlockStyle.horizontalLine} />
          {renderViewMoreButton()}
        </>
      ) : (
        <SizedBox height={8} />
      )}
    </RoundedBackgroundView>
  );
};
