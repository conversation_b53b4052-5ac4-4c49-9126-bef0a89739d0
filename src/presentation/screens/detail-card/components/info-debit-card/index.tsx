import {Block} from '@components/block';
import HorizontalDivider from '@components/line-view/Divider';
import MoneyView from '@components/money-view';
import {Text} from '@components/text';
import {translate} from '@locales';
import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {InfoDebitCardProps} from './type';

export const InfoDebitCard: React.FC<InfoDebitCardProps> = ({cardModel}) => {
  const {
    theme: {ColorDataView, SizeDataView, Typography},
  } = useMSBStyles();

  const props = {
    accountNumber: cardModel?.rbsNumber,
    accountName: cardModel?.holder?.name,
    moneyValue: cardModel?.availableBalance,
    currency: cardModel?.currency,
    colorAccount: ColorDataView.TextSub,
  };

  return (
    <Block gap={12}>
      <HorizontalDivider marginTop={16} />
      <Block direction="row" gap={SizeDataView.SpacingHorizontal} justifyContent="space-between">
        <Text
          testID="cm.infor-debits-card.title"
          color={ColorDataView.TextSub}
          type={Typography?.small_regular}
          flex={1}>
          {translate('detail_card.availableBalance')}
        </Text>
        <MoneyView money={props.moneyValue} currency={props.currency} />
      </Block>
      <Block direction="row" gap={SizeDataView.SpacingHorizontal} justifyContent="space-between">
        <Text
          testID="cm.infor-debits-card.linked-title"
          color={ColorDataView.TextSub}
          type={Typography?.small_regular}
          flex={1}>
          {translate('detail_card.linked_account_title')}
        </Text>
        <Text testID="cm.account-info.account-number" type={Typography?.small_medium} color={ColorDataView.TextMain}>
          {props.accountNumber}
        </Text>
      </Block>
    </Block>
  );
};
