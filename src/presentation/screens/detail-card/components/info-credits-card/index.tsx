import {Block} from '@components/block';
import HorizontalDivider from '@components/line-view/Divider';
import MoneyView from '@components/money-view';
import {Text} from '@components/text';
import {Card} from '@entities/card/Card';
import {BottomSheetView} from '@gorhom/bottom-sheet';
import {translate} from '@locales';
import {hostSharedModule} from 'msb-host-shared-module';
import {MSBIcon, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {infoCreditStyleSheet, styleSheet} from '../../styles';

const InstrumentDescription = () => {
  const {
    styles,
    theme: {SizeGlobal},
  } = useMSBStyles(styleSheet);
  const {bottom} = useSafeAreaInsets();

  return (
    <BottomSheetView
      testID="cm.bottom-sheet.instrument-description"
      style={[styles.instrumentDescriptionContainer, {paddingBottom: bottom + SizeGlobal.Size400}]}>
      <Text style={styles.instrumentDescTxt}>{translate('detail_card.instrument_description')}</Text>
    </BottomSheetView>
  );
};

export const InfoCreditsCard = ({cardModel}: {cardModel?: Card}) => {
  const {styles: infoCreditStyle} = useMSBStyles(infoCreditStyleSheet);
  const money = Math.abs(cardModel?.spentAmount ?? 0);
  const haveRemainingInstallment = cardModel?.remainingInstallments && cardModel?.remainingInstallments > 0;

  const showInstrumentDescBottomSheet = () => {
    return hostSharedModule.d.domainService.showBottomSheet({
      header: translate('detail_card.instrument_title'),
      children: <InstrumentDescription />,
    });
  };

  return (
    <Block gap={16}>
      <HorizontalDivider marginTop={16} />
      <View style={infoCreditStyle.container}>
        <View style={infoCreditStyle.valueContainer}>
          <View style={infoCreditStyle.valueTitleContainer}>
            <Text testID="cm.infor-credits-card.instrument-title" style={infoCreditStyle.instrumentTitle}>
              {translate('detail_card.instrument_title')}
            </Text>
            <MSBIcon
              testID="cm.infor-credits-card.show-info-btn"
              icon="info-tooltip-circle"
              iconSize={16}
              onIconClick={showInstrumentDescBottomSheet}
            />
          </View>
          <MoneyView money={money} currency={cardModel?.currency} moreStyles={infoCreditStyle.moneyView} />
        </View>
        {haveRemainingInstallment ? (
          <View style={infoCreditStyle.valueContainer}>
            <View style={infoCreditStyle.valueTitleContainer}>
              <Text testID="cm.infor-credits-card.remaining-installment" style={infoCreditStyle.instrumentTitle}>
                {translate('detail_card.remaining_installment')}
              </Text>
            </View>
            <MoneyView
              money={cardModel?.remainingInstallments}
              currency={cardModel?.currency}
              moreStyles={infoCreditStyle.moneyView}
            />
          </View>
        ) : (
          <></>
        )}
      </View>
    </Block>
  );
};
