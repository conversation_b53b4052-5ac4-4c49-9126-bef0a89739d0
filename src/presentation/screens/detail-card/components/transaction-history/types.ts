import {RootStackParamList} from '@app-navigation/types';
import {OnSearchTransaction} from '@components/transaction-history/type';
import {Card} from '@entities/card/Card';
import {CardFeature} from '@entities/card/CardFeature';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

export interface HistoryHeaderComponentProps extends OnSearchTransaction {
  hasError: boolean;
  isEmpty: boolean;
  cardModel?: Card;
  navigation: NativeStackNavigationProp<RootStackParamList>;
  handleCardFunction: (feature?: CardFeature) => void;
  handleCardFeatureAction: (feature: CardFeature, card: Card) => void;
}
