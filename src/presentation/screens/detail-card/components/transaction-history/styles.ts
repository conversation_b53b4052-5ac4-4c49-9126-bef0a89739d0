import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorGlobal, SizeAlias}) => ({
  cardErrorContainer: {
    overflow: 'visible',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: SizeAlias.Radius3,
  },
  detailContainer: {
    flexDirection: 'column',
  },
  bgCardContainer: {
    backgroundColor: ColorGlobal.NeutralWhite,
    borderRadius: SizeAlias.Radius3,
    padding: 16,
    width: '100%',
    flexDirection: 'column',
  },
}));
