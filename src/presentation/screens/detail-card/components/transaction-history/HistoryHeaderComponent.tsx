import {sharedStyleSheet} from '@components/common/styles';
import GeneralInfoCard from '@components/general-info-card-view';
import HistoryHeaderSection from '@components/transaction-history/HistoryHeaderSection';
import {CardType} from '@entities/card/CardType';
import {OwnerShip} from '@entities/card/OwnerShip';
import {translate} from '@locales';
import {EmptyType, MSBEmptyState, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {View} from 'react-native';
import {FeaturesBlock} from '../features-block';
import {InfoCreditsCard} from '../info-credits-card';
import {InfoDebitCard} from '../info-debit-card';
import {SubCardBlockView} from '../sub-card-block';
import {SubCardOwnerName} from '../sub-card-owner-name';
import {styleSheet} from './styles';
import {HistoryHeaderComponentProps} from './types';

const HistoryHeaderComponent: React.FC<HistoryHeaderComponentProps> = ({
  hasError,
  isEmpty,
  cardModel,
  navigation,
  handleCardFunction,
  handleCardFeatureAction,
  onSearchTransaction,
}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  const renderCardInfo = useCallback(() => {
    const subCardLength = cardModel?.subCards?.length ?? 0;
    const isCreditCard = cardModel?.type === CardType.Credit;
    const isSubCard = cardModel?.ownership === OwnerShip.Sub;
    const hasSubCard = subCardLength > 0;

    const generalInfoModel = {
      imageUrl: cardModel?.cardVisual?.images?.[0]?.imageURL ?? '',
      instrumentI18n: cardModel?.instrumentI18n ?? '',
      name: cardModel?.name ?? '',
      status: cardModel?.status ?? undefined,
      markedNumber: cardModel?.maskedNumber ?? '',
      ownership: cardModel?.ownership ?? OwnerShip.Main,
    };

    return (
      <View style={[styles.bgCardContainer, sharedStyles.shadow]}>
        <GeneralInfoCard data={generalInfoModel} />
        {isSubCard && <SubCardOwnerName ownerName={cardModel?.holder.name ?? ''} />}
        {isCreditCard ? <InfoCreditsCard cardModel={cardModel} /> : <InfoDebitCard cardModel={cardModel} />}

        {hasSubCard && (
          <SubCardBlockView
            subCards={cardModel?.subCards ?? []}
            navigation={navigation}
            handleCardFeatureAction={handleCardFeatureAction}
          />
        )}
      </View>
    );
  }, [cardModel, navigation, styles]);

  return (
    <>
      {hasError ? (
        <View style={[sharedStyles.shadow, styles.cardErrorContainer]}>
          <MSBEmptyState
            testID="cm.history-card.empty-state"
            type={EmptyType.System}
            emptyTitle={translate('error_title')}
            emptySubTitle={translate('error_desc')}
          />
        </View>
      ) : (
        <View style={styles.detailContainer}>
          {renderCardInfo()}
          <FeaturesBlock features={cardModel?.features ?? []} onCardFunction={handleCardFunction} />
        </View>
      )}
      <HistoryHeaderSection isEmpty={isEmpty} onSearchTransaction={onSearchTransaction} />
    </>
  );
};

export default HistoryHeaderComponent;
