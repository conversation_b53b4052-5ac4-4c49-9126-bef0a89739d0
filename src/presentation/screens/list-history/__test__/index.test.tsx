import {DISPLAY_DATE_FORMAT} from '@constants';
import {TransactionDateRange, TransactionType} from '@domain/entities/form/FormTransactionFilter';
import {translate} from '@locales';
import {fireEvent, screen, shouldShowToast, userEvent, within} from '@presentation/__test__/test-utils';
import {goToFilterTransactionScreen} from '@presentation/screens/transaction-filter/__test__/test-utils';
import {getDateRange} from '@utils';

import {
  mock2ndPageFailedForGetListFilterHistory,
  mockEmptyPaginationResponseForGetListFilterHistory,
  mockListFilterTransactionHistoriesResponse,
  mockPaginationResponseForGetListFilterHistory,
  mockServerFailureForGetListFilterHistory,
} from 'mocks/service-apis/get-list-filter-transaction';
import {goToListHistory, load2ndPageListFilterHistory} from './test-utils';

describe('List History Screen', () => {
  it('should show filtered transaction results after user submits a valid filter form', async () => {
    await goToFilterTransactionScreen();

    mockPaginationResponseForGetListFilterHistory();
    const searchTouchable = await screen.findByTestId('cm.filter-transaction.confirm-btn');
    const userSetup = userEvent.setup();
    await userSetup.press(searchTouchable);

    const filterBtn = await screen.findByTestId('header-icon-button-tone-filter-sort');

    // have total 5 transactions
    expect(
      within(screen.getByTestId('cm.list-history.header-section')).getByText(
        mockListFilterTransactionHistoriesResponse.totalRecord.toString(),
      ),
    ).toBeOnTheScreen();

    /**
     * mock 5 transaction histories
     * spilt to 2 pages
     * page 1: 3 transactions
     * page 2: 2 transactions
     */
    const lastTransaction = within(screen.getByTestId('cm.transaction-history.list')).getByTestId(
      `cm.history-card.history-item.${mockListFilterTransactionHistoriesResponse.transactionHistories[2].rrn}`,
    );
    // expect render last transaction in 1st page
    expect(lastTransaction).toBeOnTheScreen();
    await userSetup.press(filterBtn);

    // back to filter screen
    expect(await screen.findByTestId('cm.transaction-filter.scroll-view')).toBeOnTheScreen();

    // check last filter options not changes
    const searchTermInput = screen.getByTestId('cm.filter-transaction.search-team');
    expect(searchTermInput).toHaveProp('value', 'Test');

    expect(
      within(screen.getByTestId(`cm.form-radio.${TransactionType.INCOME}`)).getByTestId('checked-icon'),
    ).toBeTruthy();

    const {startDate, endDate} = getDateRange(TransactionDateRange['7_DAYS']);
    expect(
      within(screen.getByTestId('cm.filter-transaction.from-date')).getByText(startDate.format(DISPLAY_DATE_FORMAT)),
    ).toBeOnTheScreen();
    expect(
      within(screen.getByTestId('cm.filter-transaction.to-date')).getByText(endDate.format(DISPLAY_DATE_FORMAT)),
    ).toBeOnTheScreen();
    expect(screen.getByTestId('cm.filter-transaction.confirm-btn')).toBeEnabled();

    const amountFromInput = screen.getByTestId('cm.filter-transaction.from-currency');
    expect(amountFromInput).toHaveProp('value', '10,000');
    const amountToInput = screen.getByTestId('cm.filter-transaction.to-currency');
    expect(amountToInput).toHaveProp('value', '500,000');

    // back to list history screen
    await userSetup.press(screen.getByTestId('cm.filter-transaction.confirm-btn'));
    expect(await screen.findByTestId('cm.transaction-history.list')).toBeOnTheScreen();
  });

  it('should show empty view if not have transaction history', async () => {
    await goToFilterTransactionScreen();
    mockEmptyPaginationResponseForGetListFilterHistory();

    const searchTouchable = await screen.findByTestId('cm.filter-transaction.confirm-btn');
    const userSetup = userEvent.setup();
    await userSetup.press(searchTouchable);

    const emptyState = await screen.findByTestId('cm.history-card.empty-state');
    expect(within(emptyState).getByText(translate('transaction.empty_result'))).toBeOnTheScreen();
  });

  it('should show error view if not have transaction history', async () => {
    await goToFilterTransactionScreen();
    mockServerFailureForGetListFilterHistory();

    const searchTouchable = await screen.findByTestId('cm.filter-transaction.confirm-btn');
    const userSetup = userEvent.setup();
    await userSetup.press(searchTouchable);

    const emptyState = await screen.findByTestId('cm.history-card.empty-state');
    expect(within(emptyState).getByText(translate('errors.messages.temporary_disruption'))).toBeOnTheScreen();

    const prevScrollableView = screen.getByTestId('cm.transaction-history.list');
    const refreshControl = prevScrollableView.props.refreshControl;
    expect(refreshControl).toBeTruthy();

    mockPaginationResponseForGetListFilterHistory();
    refreshControl.props.onRefresh();
    const scrollableView = await screen.findByTestId('cm.transaction-history.list');
    expect(scrollableView.props.data.length).toEqual(4);
  });

  it('loading page 2 when user scroll to bottom', async () => {
    await load2ndPageListFilterHistory();

    const scrollableView = screen.getByTestId('cm.transaction-history.list');
    fireEvent(scrollableView, 'endReached', {
      distanceFromEnd: 100,
    });

    // expect render last transaction in 2nd page
    expect(scrollableView.props.data.length - 1).toEqual(
      mockListFilterTransactionHistoriesResponse.transactionHistories.length,
    );
  });

  it('should refresh data when user pull to refresh', async () => {
    await load2ndPageListFilterHistory();

    const prevScrollableView = screen.getByTestId('cm.transaction-history.list');
    const refreshControl = prevScrollableView.props.refreshControl;
    expect(refreshControl).toBeTruthy();
    expect(prevScrollableView.props.data.length).toEqual(6);
    refreshControl.props.onRefresh();

    const scrollableView = await screen.findByTestId('cm.transaction-history.list');
    expect(scrollableView.props.data.length).toEqual(4);
  });

  it('should show error toast when 2nd page loading failed', async () => {
    mock2ndPageFailedForGetListFilterHistory();
    await goToFilterTransactionScreen();
    const searchTouchable = await screen.findByTestId('cm.filter-transaction.confirm-btn');
    const userSetup = userEvent.setup();
    await userSetup.press(searchTouchable);
    const scrollableView = await screen.findByTestId('cm.transaction-history.list');
    expect(scrollableView).toBeOnTheScreen();

    fireEvent(scrollableView, 'endReached', {
      distanceFromEnd: 100,
    });
    const toast = await shouldShowToast();
    expect(within(toast).getByText(translate('error_desc'))).toBeOnTheScreen();
  });

  it('should go back to detail screen when user presses the back button', async () => {
    await goToListHistory();
    const backBtn = await screen.findByTestId('action-back');
    expect(backBtn).toBeOnTheScreen();
    await userEvent.press(backBtn);
    expect(backBtn).not.toBeOnTheScreen();
    expect(await screen.findByTestId('cm.detail-card.list')).toBeOnTheScreen();
  });
});
