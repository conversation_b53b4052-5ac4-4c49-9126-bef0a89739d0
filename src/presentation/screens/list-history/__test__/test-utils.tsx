import {fireEvent, screen, userEvent, within} from '@presentation/__test__/test-utils';
import {goToFilterTransactionScreen} from '@presentation/screens/transaction-filter/__test__/test-utils';
import {
  mockListFilterTransactionHistoriesResponse,
  mockPaginationResponseForGetListFilterHistory,
} from 'mocks/service-apis/get-list-filter-transaction';

export const goToListHistory = async () => {
  await goToFilterTransactionScreen();

  mockPaginationResponseForGetListFilterHistory();
  const searchTouchable = await screen.findByTestId('cm.filter-transaction.confirm-btn');
  const userSetup = userEvent.setup();
  await userSetup.press(searchTouchable);
};

export const load2ndPageListFilterHistory = async () => {
  await goToListHistory();
  const scrollableView = await screen.findByTestId('cm.transaction-history.list');

  fireEvent(scrollableView, 'endReached', {
    distanceFromEnd: 100,
  });

  const lastTransaction = await within(screen.getByTestId('cm.transaction-history.list')).findByTestId(
    `cm.history-card.history-item.${mockListFilterTransactionHistoriesResponse.transactionHistories[4].rrn}`,
  );
  expect(lastTransaction).toBeOnTheScreen();
  expect(scrollableView.props.data.length - 1).toEqual(
    mockListFilterTransactionHistoriesResponse.transactionHistories.length,
  );
};
