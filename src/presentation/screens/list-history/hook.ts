import {FILTER_HISTORY_PAGE_SIZE, SERVER_DATE_FORMAT_WITH_TIMEZONE} from '@constants';
import {isLeft} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {TransactionType} from '@domain/entities/form/FormTransactionFilter';
import {History, HistoryItem, HistoryItemType, ListHistory} from '@entities/card/History';
import {translate} from '@locales';
import {ApplicationScreenProps, RootStackParamList} from '@presentation/navigation/types';
import {useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {createLazyLoadingManager} from '@utils';
import {parseCurrencyToNumber} from '@utils/StringFormat';
import {isEmpty, isNil} from 'lodash';
import moment from 'moment';
import {hostSharedModule, ToastType} from 'msb-host-shared-module';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {AMOUNT_MAX, AMOUNT_MIN} from './constants';

const DEFAULT_METADATA = {
  totalCount: Number.POSITIVE_INFINITY,
  currentCount: 0,
  processing: false,
  lastSection: undefined,
};

const useListHistoryViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {
    params: {cardId, filterOptions},
  } = useRoute<ApplicationScreenProps<'ListHistoryScreen'>['route']>();

  const [totalCount, setTotalCount] = useState(0);
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
  const [loadingHistories, setLoadingHistories] = useState(true);
  const [loadingMoreHistories, setLoadingMoreHistories] = useState(false);
  const [hasError, setHasError] = useState(false);

  const metadata = useRef<{
    totalCount: number;
    currentCount: number;
    processing: boolean;
    lastSection: string | undefined;
  }>({...DEFAULT_METADATA});

  const loadingManager = useMemo(() => createLazyLoadingManager(setLoadingHistories, setLoadingMoreHistories), []);

  useEffect(() => {
    getHistories(true);
    return () => {};
  }, []);

  const onRefresh = useCallback(() => {
    getHistories(true);
  }, [historyItems, hasError]);

  const countBadge = useMemo(() => {
    const conditions = [
      filterOptions.searchTerm.trim(),
      !isNil(filterOptions.transactionType),
      !isNil(filterOptions.transactionDateRange),
      !isEmpty(filterOptions.fromCurrency.trim()) || !isEmpty(filterOptions.toCurrency.trim()),
    ];

    return conditions.filter(Boolean).length;
  }, [filterOptions]);

  const onFilter = useCallback(() => {
    navigation.navigate(
      'TransactionFilterScreen',
      {
        cardId,
      },
      {pop: true},
    );
  }, [navigation, cardId]);

  const onGoBack = useCallback(() => {
    navigation.popTo('DetailCardScreen', {id: cardId});
  }, [navigation]);

  const resetHistoryState = useCallback(() => {
    metadata.current = {...DEFAULT_METADATA};
    if (historyItems.length !== 0) {
      setHistoryItems([]);
    }
    if (hasError) {
      setHasError(false);
    }
  }, [historyItems.length, hasError]);

  const shouldFetchMoreHistories = useCallback(() => {
    return metadata.current.currentCount < metadata.current.totalCount && !metadata.current.processing;
  }, []);

  const fetchHistoryData = useCallback(async () => {
    const page = Math.ceil(metadata.current.currentCount / FILTER_HISTORY_PAGE_SIZE) + 1;

    const transTypeMapper: Record<number, string> = {
      [TransactionType.ALL]: 'all',
      [TransactionType.INCOME]: '1',
      [TransactionType.OUTCOME]: '-1',
    };

    let from = moment().add(-90, 'days');
    let to = moment();
    if (!isNil(filterOptions.transactionDateRange)) {
      from = moment(filterOptions.fromDate);
      to = moment(filterOptions.toDate);
    }

    return await DIContainer.getInstance()
      .getGetFilterTransactionHistoryUseCase()
      .execute({
        cardId,
        page,
        size: FILTER_HISTORY_PAGE_SIZE,
        from: from.utc().format(SERVER_DATE_FORMAT_WITH_TIMEZONE),
        to: to.utc().format(SERVER_DATE_FORMAT_WITH_TIMEZONE),
        keySearch: filterOptions.searchTerm.trim().length === 0 ? null : filterOptions.searchTerm,
        amountMin:
          filterOptions.fromCurrency.trim().length === 0
            ? AMOUNT_MIN
            : parseCurrencyToNumber(filterOptions.fromCurrency),
        amountMax:
          filterOptions.toCurrency.trim().length === 0 ? AMOUNT_MAX : parseCurrencyToNumber(filterOptions.toCurrency),
        transType: transTypeMapper[filterOptions.transactionType.id],
        categoryCode: [],
      });
  }, [cardId, filterOptions]);

  const handleHistoryError = useCallback((isLoadingFirst: boolean) => {
    const page = Math.ceil(metadata.current.currentCount / FILTER_HISTORY_PAGE_SIZE) + 1;
    if (page > 1) {
      hostSharedModule.d.domainService.showToast({
        message: translate('error_desc'),
        type: ToastType.ERROR,
      });
    } else {
      setHasError(true);
    }
    loadingManager.hideLoading(isLoadingFirst);
    metadata.current.processing = false;
  }, []);

  const updateHistoryItems = useCallback(
    (listHistory: ListHistory, isLoadingFirst: boolean) => {
      const newHistoryItems = isLoadingFirst ? [] : [...historyItems];

      for (const history of listHistory.data) {
        const isNewSection = !metadata.current.lastSection || metadata.current.lastSection !== history.transDay;
        if (isNewSection) {
          newHistoryItems.push({
            type: HistoryItemType.HEADER_SECTION,
            headerTitle: history.transDay,
          });
          metadata.current.lastSection = history.transDay;
        }

        newHistoryItems.push({
          type: HistoryItemType.ITEM,
          data: history,
          isFirstSection: isNewSection,
        });
      }

      setHistoryItems(newHistoryItems);
    },
    [historyItems],
  );

  const updateHistoryMetadata = useCallback(
    (listHistory: ListHistory) => {
      metadata.current.currentCount += listHistory.data.length;
      metadata.current.totalCount = listHistory.metadata.totalItems;
      if (totalCount !== listHistory.metadata.totalItems) {
        setTotalCount(listHistory.metadata.totalItems);
      }
    },
    [totalCount],
  );

  const getHistories = useCallback(
    async (forceRefresh = false) => {
      if (forceRefresh) {
        resetHistoryState();
      }

      if (!shouldFetchMoreHistories()) {
        return;
      }

      metadata.current.processing = true;
      const isLoadingFirst = metadata.current.totalCount === Number.POSITIVE_INFINITY;
      loadingManager.showLoading(isLoadingFirst);

      const result = await fetchHistoryData();

      if (isLeft(result)) {
        handleHistoryError(isLoadingFirst);
        return;
      }

      const listHistory =
        result.data ??
        ({
          data: [],
          metadata: {
            totalItems: metadata.current.totalCount ?? 0,
          },
        } as ListHistory);

      updateHistoryMetadata(listHistory);
      updateHistoryItems(listHistory, isLoadingFirst);

      loadingManager.hideLoading(isLoadingFirst);
      metadata.current.processing = false;
    },
    [
      resetHistoryState,
      shouldFetchMoreHistories,
      fetchHistoryData,
      handleHistoryError,
      updateHistoryMetadata,
      updateHistoryItems,
    ],
  );

  const onEndReached = useCallback(() => {
    if (metadata.current.currentCount < FILTER_HISTORY_PAGE_SIZE) {
      return;
    }
    getHistories();
  }, [getHistories]);

  const onNavToDetailScreen = useCallback((history?: History) => {
    if (!history) {
      return;
    }
    navigation.navigate('HistoryDetailScreen', {
      history,
    });
  }, []);

  return {
    totalCount,
    countBadge,
    historyItems,
    loadingHistories,
    loadingMoreHistories,
    hasError,
    onGoBack,
    onEndReached,
    onRefresh,
    onFilter,
    onNavToDetailScreen,
  };
};

export default useListHistoryViewModel;
