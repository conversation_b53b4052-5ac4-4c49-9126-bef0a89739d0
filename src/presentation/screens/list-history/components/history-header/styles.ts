import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorDataView, SizeGlobal, SizeAlias}) => ({
  historySectionTitle: {
    flex: 1,
    color: ColorDataView.TextSub,
  },
  historySectionContainer: {
    paddingTop: SizeGlobal.Size400,
    paddingBottom: SizeGlobal.Size100,
    paddingLeft: SizeGlobal.Size400,
    justifyContent: 'flex-end',
    backgroundColor: ColorAlias.BackgroundSecondary,
  },
  historySectionContainerFirst: {
    borderTopLeftRadius: SizeAlias.Radius4,
    borderTopRightRadius: SizeAlias.Radius4,
  },
}));
