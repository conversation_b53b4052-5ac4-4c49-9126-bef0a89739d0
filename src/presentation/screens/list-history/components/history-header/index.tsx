import {sharedStyleSheet} from '@components/common/styles.ts';
import {MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles.ts';

interface HistoryHeaderProps {
  title: string;
  isFirst: boolean;
}

const HistoryHeader: React.FC<HistoryHeaderProps> = ({title}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  return (
    <View style={[sharedStyles.shadow, styles.historySectionContainer]}>
      <MSBTextBase type={Typography?.caption_regular} style={styles.historySectionTitle}>
        {title}
      </MSBTextBase>
    </View>
  );
};

export default HistoryHeader;
