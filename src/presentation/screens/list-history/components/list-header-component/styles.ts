import {createMSBStyleSheet, SizeGlobal} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({Typography, ColorDataView, SizeAlias, ColorGlobal}) => ({
  titleWrap: {
    backgroundColor: ColorGlobal.NeutralWhite,
    color: ColorDataView.TextSub,
    borderTopLeftRadius: SizeAlias.Radius4,
    borderTopRightRadius: SizeAlias.Radius4,
    padding: SizeGlobal.Size400,
  },
  titleBold: {
    ...Typography?.small_semiBold,
  },
}));
