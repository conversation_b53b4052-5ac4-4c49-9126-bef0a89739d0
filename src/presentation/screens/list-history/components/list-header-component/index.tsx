import {sharedStyleSheet} from '@components/common/styles';
import {Text} from '@components/text';
import {translate} from '@locales';
import {highlightText} from '@utils/StringFormat';
import {useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {ListHeaderComponentProps} from './type';

const ListHeaderComponent: React.FC<ListHeaderComponentProps> = ({totalItems, hasError, isEmpty, isLoading}) => {
  const {
    styles,
    theme: {Typography},
  } = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  if (!isLoading && (hasError || isEmpty)) {
    return null;
  }

  return (
    <View style={[sharedStyles.shadow, styles.titleWrap]}>
      <Text testID="cm.list-history.header-section" type={Typography?.small_regular}>
        {highlightText(
          translate('transaction.search_results_found', {
            count: totalItems,
          }),
          totalItems.toString(),
          styles.titleBold,
          {},
        )}
      </Text>
    </View>
  );
};

export default ListHeaderComponent;
