import {sharedStyleSheet} from '@components/common/styles';
import {HistoryCardLoadMoreSkeleton, HistoryCardSkeleton} from '@components/loading-skeleton/HistoryCardSkeleton';
import {translate} from '@locales';
import {EmptyType, MSBEmptyState, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {HistoryFooterProps} from './types';

const HistoryFooter: React.FC<HistoryFooterProps> = ({hasError, isEmpty, loading, loadingMore}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);

  if (loading) {
    return (
      <View style={[sharedStyles.shadow, styles.loadingContainer]}>
        <HistoryCardSkeleton isLoading={true} />
      </View>
    );
  }

  if (loadingMore) {
    return (
      <View style={[sharedStyles.shadow, styles.loadingContainer]}>
        <HistoryCardLoadMoreSkeleton isLoading={true} />
      </View>
    );
  }

  if (hasError) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.history-card.empty-state"
          type={EmptyType.System}
          emptyTitle={translate('errors.messages.temporary_disruption')}
          emptySubTitle={translate('errors.messages.retry_apology')}
        />
      </View>
    );
  }

  if (isEmpty) {
    return (
      <View style={[sharedStyles.shadow, styles.errorContainer]}>
        <MSBEmptyState
          testID="cm.history-card.empty-state"
          type={EmptyType.Search}
          emptyTitle={translate('transaction.empty_result')}
          emptySubTitle={translate('transaction.no_search_results_suggestion')}
        />
      </View>
    );
  }

  return null;
};

export default HistoryFooter;
