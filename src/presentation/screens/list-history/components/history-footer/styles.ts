import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeAlias, SizeGlobal}) => {
  return {
    errorContainer: {
      borderRadius: SizeAlias.Radius4,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'white',
      padding: SizeGlobal.Size400,
    },
    loadingContainer: {
      borderBottomLeftRadius: SizeAlias.Radius4,
      borderBottomRightRadius: SizeAlias.Radius4,
      overflow: 'visible',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'white',
    },
  };
});
