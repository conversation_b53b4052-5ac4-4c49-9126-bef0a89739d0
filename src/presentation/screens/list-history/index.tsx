import HistoryCardItem from '@components/transaction-history/HistoryCardItem';
import {HistoryItem, HistoryItemType} from '@domain/entities/card/History';
import {translate} from '@locales';
import {MSBPage, useMSBStyles} from 'msb-shared-component';
import React, {useCallback} from 'react';
import {FlatList, RefreshControl} from 'react-native';
import HistoryFooter from './components/history-footer';
import HistoryHeader from './components/history-header';
import ListHeaderComponent from './components/list-header-component';
import useListHistoryViewModel from './hook';
import {styleSheet} from './styles';

const ListHistoryScreen = () => {
  const {styles} = useMSBStyles(styleSheet);
  const {
    totalCount,
    countBadge,
    historyItems,
    loadingMoreHistories,
    loadingHistories,
    hasError,
    onGoBack,
    onRefresh,
    onEndReached,
    onFilter,
    onNavToDetailScreen,
  } = useListHistoryViewModel();

  const renderItem = useCallback(
    (data: {item: HistoryItem; index: number}) => {
      if (data.item.type === HistoryItemType.HEADER_SECTION) {
        return <HistoryHeader isFirst={data.index === 0} title={data.item.headerTitle} />;
      }

      return (
        <HistoryCardItem
          testID={`cm.history-card.history-item.${data.item.data.rrn}`}
          isLast={data.index === historyItems.length - 1}
          loading={loadingMoreHistories}
          historyItem={data.item}
          onPress={() => onNavToDetailScreen(data.item.data)}
        />
      );
    },
    [loadingMoreHistories, historyItems.length],
  );

  const renderHeader = useCallback(() => {
    return (
      <ListHeaderComponent
        totalItems={totalCount}
        hasError={hasError}
        isEmpty={totalCount === 0}
        isLoading={loadingHistories}
      />
    );
  }, [totalCount, hasError, loadingHistories]);

  return (
    <MSBPage
      backgroundProps={{nameImage: 'bg_main', testID: 'cm.list-history.background'}}
      isScrollable={false}
      headerProps={{
        hasBack: true,
        onGoBack,
        testID: 'cm.list-history.header',
        title: translate('transaction.search_title'),
        rightButtons: [
          {
            icon: 'tone-filter-sort',
            countBadge: countBadge,
            onPress: onFilter,
          },
        ],
      }}>
      <FlatList
        testID="cm.transaction-history.list"
        refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainerStyle}
        showsVerticalScrollIndicator={false}
        data={historyItems}
        extraData={[loadingHistories, loadingMoreHistories, hasError]}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={
          <HistoryFooter
            hasError={hasError}
            isEmpty={historyItems.length === 0}
            loading={loadingHistories}
            loadingMore={loadingMoreHistories}
          />
        }
        keyExtractor={(item, index) => `history-${item.type}-${index}`}
        renderItem={renderItem}
        onEndReachedThreshold={0.25}
        onEndReached={onEndReached}
      />
    </MSBPage>
  );
};

export default ListHistoryScreen;
