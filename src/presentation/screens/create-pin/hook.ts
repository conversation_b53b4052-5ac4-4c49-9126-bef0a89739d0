import {RootStackParamList} from '@app-navigation/types.ts';
import {CardType} from '@entities/card/CardType.ts';
import {translate} from '@locales';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {debounceTimes, isValidPin} from '@utils';
import {OtpInputRef} from 'msb-shared-component';
import {useCallback, useMemo, useRef, useState} from 'react';

const useCreatePinViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const pinInputRef = useRef<OtpInputRef>(null);
  const [error, setError] = useState<string | null>(null);
  const {params} = useRoute<RouteProp<RootStackParamList, 'CreatePinScreen'>>();
  let cardData = params.cardData;

  //Wait clear otp completely
  const onFocusDebounceTimes = useCallback(
    debounceTimes(() => pinInputRef?.current?.focus(), 150),
    [],
  );
  const onClearDebounceTimes = useCallback(
    debounceTimes(() => pinInputRef?.current?.clear(), 50),
    [],
  );

  const {isDebitCard, otpLength} = useMemo(() => {
    return {
      isDebitCard: cardData?.type === CardType.Debit,
      otpLength: cardData?.type === CardType.Debit ? 6 : 4,
    };
  }, [cardData?.type]);

  const navigationReInputPinScreen = (pin: string) => {
    navigation.navigate('ReInputPinScreen', {
      cardData,
      pin: pin,
    });
  };

  const handleValidInput = (pinStr: string) => {
    if (pinStr.trim()?.length === otpLength) {
      if (!isValidPin(pinStr, otpLength) || pinStr.startsWith('0')) {
        setError(translate('create_pin.pin_error'));
        onClearDebounceTimes();
        onFocusDebounceTimes();
      } else {
        setError(null);
        navigationReInputPinScreen(pinStr.trim());
      }
    } else {
      setError(null);
    }
  };

  const value = useMemo(() => {
    return {error, handleValidInput, isDebitCard, otpLength, pinInputRef, onClearDebounceTimes};
  }, [isDebitCard, otpLength, error, onClearDebounceTimes]);

  return value;
};

export default useCreatePinViewModel;
