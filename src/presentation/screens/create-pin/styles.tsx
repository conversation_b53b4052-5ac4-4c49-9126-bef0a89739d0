import {screenWidthToDesignRatio} from '@constants/sizes';
import {createMSBStyleSheet, SizeGlobal} from 'msb-shared-component';

const IMAGE_W_6 = 245;
const IMAGE_W_4 = 186.94737243652344;
const IMAGE_H = 144;

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorGlobal, ColorLabelCaption, Typography}) => ({
  flexGrow: {
    flexGrow: 1,
  },
  container: {
    marginTop: SizeGlobal.Size600,
    marginHorizontal: SizeGlobal.Size400,
  },
  content: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SizeGlobal.Size400,
    rowGap: SizeGlobal.Size600,
  },
  keyIcon: {
    width: screenWidthToDesignRatio * IMAGE_W_6,
    height: screenWidthToDesignRatio * IMAGE_H,
    alignItems: 'center',
  },
  keyIcon4: {
    width: screenWidthToDesignRatio * IMAGE_W_4,
    height: screenWidthToDesignRatio * IMAGE_H,
    alignItems: 'center',
  },
  label: {
    ...Typography?.base_regular,
    color: ColorGlobal.Neutral800,
    width: '100%',
    textAlign: 'left',
    alignItems: 'flex-start',
  },
  pinInput: {
    marginTop: SizeGlobal.Size600,
    textAlign: 'center',
  },
  title: {
    ...Typography?.title_semiBold,
    color: '#091E42',
  },
  infoTooltipImage: {width: 20, height: 20},
  otpInputContainer: {
    width: '100%',
  },
  otpInput: {
    alignItems: 'center',
    justifyContent: 'space-between',
    columnGap: SizeGlobal.Size300,
  },
  otpInputLeft: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    columnGap: SizeGlobal.Size300,
  },
  errorText: {
    ...Typography?.small_regular,
    marginTop: SizeGlobal.Size200,
    color: ColorAlias.TextError,
    width: '100%',
  },
  bottomWrap: {flexDirection: 'row', paddingHorizontal: SizeGlobal.Size400},
  bottomContent: {flexDirection: 'column', marginStart: SizeGlobal.Size200},
  noteWrap: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    marginTop: SizeGlobal.Size100,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: ColorAlias.IconDefault,
    margin: SizeGlobal.Size200,
    marginLeft: 0,
  },
  pinNote: {
    ...Typography?.small_regular,
    color: ColorLabelCaption.TextSub,
  },
  pinNoteTop: {...Typography?.small_regular, color: ColorLabelCaption.TextMain},
}));
