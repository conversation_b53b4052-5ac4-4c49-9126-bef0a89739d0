import {RootStackParamList} from '@app-navigation/types.ts';
import RoundedBackgroundView from '@components/background-custom-view';
import {translate} from '@locales';
import {useIsFocused} from '@react-navigation/native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {
  MSBFastImage,
  MSBFolderImage,
  MSBIcon,
  MSBOTPInput,
  MSBPage,
  MSBTextBase,
  useMSBStyles,
} from 'msb-shared-component';
import React, {useEffect} from 'react';
import {ScrollView, View} from 'react-native';
import useCreatePinViewModel from './hook.ts';
import {styleSheet} from './styles.tsx';

const CreatePinScreen: React.FC<NativeStackScreenProps<RootStackParamList, 'CreatePinScreen'>> = ({navigation}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {error, handleValidInput, isDebitCard, otpLength, pinInputRef, onClearDebounceTimes} = useCreatePinViewModel();
  const isFocused = useIsFocused();

  useEffect(() => {
    if (!isFocused) {
      onClearDebounceTimes();
    }
    const unsubscribe = navigation.addListener('transitionEnd', () => {
      if (isFocused) {
        pinInputRef.current?.focus();
      }
    });
    return () => {
      unsubscribe();
    };
  }, [navigation, isFocused]);

  return (
    <MSBPage
      testID="cm.createPinScreen"
      style={styles.container}
      isScrollable={false}
      headerProps={{
        title: translate('create_pin.create_new_pin'),
        hasBack: true,
      }}>
      <ScrollView style={styles.flexGrow} scrollEnabled={false}>
        <RoundedBackgroundView style={styles.content}>
          <MSBFastImage
            folder={MSBFolderImage.IMAGES}
            nameImage={isDebitCard ? 'otp' : 'otp-numbers'}
            style={isDebitCard ? styles.keyIcon : styles.keyIcon4}
          />

          <MSBTextBase style={styles.label}>{translate('create_pin.enter_new_pin')}</MSBTextBase>
          <View style={styles.otpInputContainer}>
            <MSBOTPInput
              ref={pinInputRef}
              isEnable
              otpLength={otpLength}
              autoFocus={false}
              secureTextEntry
              onComplete={handleValidInput}
              containerStyle={isDebitCard ? styles.otpInput : styles.otpInputLeft}
            />
            {error && <MSBTextBase style={styles.errorText}>{error}</MSBTextBase>}
          </View>
          <View style={styles.bottomWrap}>
            <MSBIcon icon="info-tooltip-circle" iconSize={20} />
            <View style={styles.bottomContent}>
              <MSBTextBase style={styles.pinNoteTop}>{translate('create_pin.pin_note')}</MSBTextBase>
              <View style={styles.noteWrap}>
                {/* <View style={styles.dot} /> */}
                <MSBTextBase style={styles.pinNote}>
                  {isDebitCard ? translate('create_pin.pin_6_warning') : translate('create_pin.pin_4_warning')}
                </MSBTextBase>
              </View>
            </View>
          </View>
        </RoundedBackgroundView>
      </ScrollView>
    </MSBPage>
  );
};

export default CreatePinScreen;
