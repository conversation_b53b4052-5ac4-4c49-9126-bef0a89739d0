import {getAnnouncementResultUI} from '@entities/card/AnnouncementResults';
import {EAnnouncementResultsStatus, EAnnouncementResultsType} from '@entities/card/EAnnouncementResults';
import {highlightText} from '@utils/StringFormat';
import {MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {styleSheet} from './styles';
import {MessageInfoProps} from './type';

/**
 * MessageInfo component for displaying announcement messages based on type and status
 */
const MessageInfo: React.FC<MessageInfoProps> = ({extendData}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {type, status, errorCode} = extendData;
  const config = getAnnouncementResultUI(type, status, errorCode);

  // Return empty fragment if no config is available
  if (!config) {
    return <></>;
  }

  // Determine if we need to highlight text or display a plain message
  const shouldHighlightText = [
    // Cases where text highlighting is needed
    {
      type: EAnnouncementResultsType.activeCard,
      status: EAnnouncementResultsStatus.success,
    },
    {
      type: EAnnouncementResultsType.activeCard,
      status: EAnnouncementResultsStatus.fail,
    },
    {
      type: EAnnouncementResultsType.openCard,
      status: EAnnouncementResultsStatus.success,
    },
  ].some(cond => cond.type === type && cond.status === status);

  // Determine if we should display any message
  const shouldDisplayMessage = [
    // Success cases
    {
      type: EAnnouncementResultsType.activeCard,
      status: EAnnouncementResultsStatus.success,
    },
    {
      type: EAnnouncementResultsType.openCard,
      status: EAnnouncementResultsStatus.success,
    },
    // Failure cases
    {
      type: EAnnouncementResultsType.createNewPin,
      status: EAnnouncementResultsStatus.fail,
    },
    {
      type: EAnnouncementResultsType.activeCard,
      status: EAnnouncementResultsStatus.fail,
    },
    {
      type: EAnnouncementResultsType.unlockCard,
      status: EAnnouncementResultsStatus.fail,
    },
    {
      type: EAnnouncementResultsType.customerLockCard,
      status: EAnnouncementResultsStatus.fail,
    },
    {
      type: EAnnouncementResultsType.updateDebitPaymentAccount,
      status: EAnnouncementResultsStatus.fail,
    },
    {
      type: EAnnouncementResultsType.autoDebitPayment,
      status: EAnnouncementResultsStatus.fail,
    },
    {
      type: EAnnouncementResultsType.openCard,
      status: EAnnouncementResultsStatus.fail,
    },
  ].some(cond => cond.type === type && cond.status === status);

  // Return empty fragment if we shouldn't display a message
  if (!shouldDisplayMessage) {
    return <></>;
  }

  // Display message with highlighting if needed
  if (shouldHighlightText && config.boldText) {
    return (
      <MSBTextBase style={styles.messageTxt}>
        {highlightText(config.message, config.boldText, styles.messageBoldTxt, styles.messageTxt)}
      </MSBTextBase>
    );
  }

  // Otherwise display plain message
  return <MSBTextBase style={styles.messageTxt}>{config.message}</MSBTextBase>;
};

export default MessageInfo;
