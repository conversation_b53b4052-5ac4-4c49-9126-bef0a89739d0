import {EAnnouncementResultsStatus, EAnnouncementResultsType} from '@entities/card/EAnnouncementResults';
import {translate} from '@locales';
import {ButtonSize, ButtonType, MSBButton, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {ButtonBottomProps} from './type';

const ButtonBottom: React.FC<ButtonBottomProps> = ({extendData, onClose, onNavDetail, callHotline, onActiveCard}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {status, type} = extendData;

  const isSuccess = status === EAnnouncementResultsStatus.success;
  const isOpenCard = type === EAnnouncementResultsType.openCard;

  if (isSuccess && isOpenCard) {
    return (
      <View style={styles.failBtnContainer}>
        <MSBButton
          testID="active_result_close_button"
          style={styles.stateIsFailedButton}
          label={translate('card.close')}
          buttonType={ButtonType.Secondary}
          buttonSize={ButtonSize.Medium}
          onPress={onClose}
        />
        <MSBButton
          testID="active_result_active_card"
          style={styles.stateIsFailedButton}
          label={translate('announcement_results.active_card')}
          buttonType={ButtonType.Primary}
          buttonSize={ButtonSize.Medium}
          onPress={onActiveCard}
        />
      </View>
    );
  }
  // if (!isSuccess && isOpenCard) {
  //   return (
  //     <View style={styles.failBtnContainer}>
  //       <MSBButton
  //         testID="active_result_call_button"
  //         style={styles.stateIsFailedButton}
  //         label={translate('card.call_hotline')}
  //         buttonType={ButtonType.Secondary}
  //         buttonSize={ButtonSize.Medium}
  //         onPress={callHotline}
  //       />
  //       <MSBButton
  //         testID="active_result_experience_now"
  //         style={styles.stateIsFailedButton}
  //         label={translate('announcement_results.experience_now')}
  //         buttonType={ButtonType.Primary}
  //         buttonSize={ButtonSize.Medium}
  //         onPress={onActiveCard}
  //       />
  //     </View>
  //   );
  // }

  if (isSuccess) {
    return (
      <MSBButton
        testID="active_result_detail_button"
        style={styles.detailButton}
        label={translate('detail_card.detail_title')}
        buttonType={ButtonType.Primary}
        buttonSize={ButtonSize.Medium}
        onPress={onNavDetail}
      />
    );
  }

  return (
    <View style={styles.failBtnContainer}>
      <MSBButton
        testID="active_result_close_button"
        style={styles.stateIsFailedButton}
        label={translate('card.close')}
        buttonType={ButtonType.Secondary}
        buttonSize={ButtonSize.Medium}
        onPress={onClose}
      />
      <MSBButton
        testID="active_result_call_button"
        style={styles.stateIsFailedButton}
        label={translate('card.call_hotline')}
        buttonType={ButtonType.Primary}
        buttonSize={ButtonSize.Medium}
        onPress={callHotline}
      />
    </View>
  );
};

export default ButtonBottom;
