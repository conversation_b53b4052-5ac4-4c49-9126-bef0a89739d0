import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({SizeGlobal}) => ({
  detailButton: {
    marginTop: SizeGlobal.Size400,
    width: '100%',
  },
  stateIsFailedButton: {
    marginTop: SizeGlobal.Size400,
    width: '100%',
    flex: 1,
  },
  failBtnContainer: {
    flexDirection: 'row',
    width: '100%',
    columnGap: SizeGlobal.Size400,
  },
}));
