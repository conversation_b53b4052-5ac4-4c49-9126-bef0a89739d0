import {getAnnouncementResultUI} from '@entities/card/AnnouncementResults';
import {EAnnouncementResultsStatus} from '@entities/card/EAnnouncementResults';
import {translate} from '@locales';
import moment from 'moment';
import {MSBFastImage, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';
import {StatusInfoProps} from './type';

const StatusInfo: React.FC<StatusInfoProps> = ({status, type, date}) => {
  const {styles} = useMSBStyles(styleSheet);
  const config = getAnnouncementResultUI(type, status);
  if (!config) {
    return null;
  }

  const dateFormat = `HH:mm [${translate('announcement_results.day')}] DD/MM/YYYY`;

  return (
    <View style={styles.stateContainer}>
      <MSBFastImage style={styles.imageStatus} nameImage={config.icon} resizeMode="contain" />
      <View style={styles.status}>
        <MSBTextBase
          style={[
            styles.message,
            status === EAnnouncementResultsStatus.success ? styles.messageSuccess : styles.messageFail,
          ]}>
          {config.title}
        </MSBTextBase>
        <MSBTextBase style={styles.dateTxt}>{moment(new Date(date)).format(dateFormat)}</MSBTextBase>
      </View>
    </View>
  );
};

export default StatusInfo;
