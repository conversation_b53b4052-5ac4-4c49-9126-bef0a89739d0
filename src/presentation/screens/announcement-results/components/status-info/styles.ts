import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, SizeGlobal, Typography}) => ({
  stateContainer: {
    flexDirection: 'row',
    width: '100%',
    alignItems: 'center',
    gap: SizeGlobal.Size200,
  },
  dateTxt: {
    color: ColorAlias.TextSecondary,
    ...Typography?.small_regular,
  },
  imageStatus: {
    width: 32,
    height: 32,
  },
  status: {
    flexDirection: 'column',
    flex: 1,
  },
  message: {
    ...Typography?.base_semiBold,
  },
  messageSuccess: {
    color: ColorAlias.TextSuccess,
  },
  messageFail: {
    color: ColorAlias.TextError,
  },
}));
