import {Text} from '@components/text';
import {AnnouncementResultsParams, getAnnouncementResultUI} from '@domain/entities/card/AnnouncementResults';
import HorizontalLineView from '@presentation/components/line-view';
import {MSBIcon, MSBIconSize, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {View} from 'react-native';
import {styleSheet} from './styles';

interface Props {
  extendData: AnnouncementResultsParams['extendData'];
  onDownload: (url: string) => void;
}

const FilesView: React.FC<Props> = ({onDownload, extendData}) => {
  const {styles} = useMSBStyles(styleSheet);
  const {type, status, errorCode, fileUrl} = extendData;
  const config = getAnnouncementResultUI(type, status, errorCode);

  // Return empty fragment if no config is available
  if (!config?.file || !fileUrl) {
    return <></>;
  }

  return (
    <View>
      <HorizontalLineView style={styles.noMargin} />
      <View style={styles.container}>
        <MSBTouchable style={styles.containerBtn} onPress={() => onDownload(fileUrl)}>
          <MSBIcon icon={config.file.iconName} iconSize={MSBIconSize.SIZE_32} />
          <Text style={styles.title}>{config.file.title}</Text>
        </MSBTouchable>
      </View>
    </View>
  );
};

export default FilesView;
