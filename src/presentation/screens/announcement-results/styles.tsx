import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorGlobal, ColorPopup, SizeGlobal, SizeHeader, Typography}) => ({
  container: {
    flex: 1,
    paddingHorizontal: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size400,
  },
  subContainer: {
    flexDirection: 'column',
    flex: 1,
    width: '100%',
  },
  roundBg: {
    marginHorizontal: SizeGlobal.Size400,
    borderWidth: 0,
  },
  backgroundCardResult: {
    padding: SizeGlobal.Size400,
    rowGap: SizeGlobal.Size400,
  },
  cardView: {
    rowGap: SizeGlobal.Size300,
  },
  errorCode: {
    ...Typography?.base_regular,
    color: ColorPopup.TextErrorCode,
  },
  noMargin: {marginHorizontal: 0},
  headerContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: <PERSON>zeHeader.SpacingHorizontal,
    paddingVertical: SizeHeader.SpacingVertical,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: ColorGlobal.NeutralWhite,
    justifyContent: 'center',
    alignItems: 'center',
  },
  extend: {},
}));
