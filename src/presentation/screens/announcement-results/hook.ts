import {ApplicationScreenProps, RootStackParamList} from '@app-navigation/types';
import {usePreventBack} from '@app-navigation/usePreventBack';
import {HOTLINE} from '@constants';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {CommonActions, NavigationRoute, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {hostSharedModule} from 'msb-host-shared-module';
import {useMemo} from 'react';
import {Linking, Platform} from 'react-native';
import ReactNativeBlobUtil from 'react-native-blob-util';

function isDetailCardRoute(
  route_: NavigationRoute<RootStackParamList, keyof RootStackParamList>,
): route_ is NavigationRoute<RootStackParamList, 'DetailCardScreen'> {
  return route_.name === 'DetailCardScreen' && !!route_.params && 'id' in route_.params;
}

const useAnnouncementResult = () => {
  usePreventBack();

  const route = useRoute<ApplicationScreenProps<'AnnouncementResultsScreen'>['route']>();
  const {extendData, cardData} = route.params;
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  // const [downloadProgress, setDownloadProgress] = useState<number | null>(0);
  // console.log('🚀 ~ useAnnouncementResult ~ downloadProgress:', downloadProgress);

  //nav to Home
  const rightIconPress = () => {
    //@ts-ignore
    navigation.popTo('BottomTabs');
  };

  const callHotline = () => {
    Linking.openURL(`tel:${HOTLINE}`);
  };

  const onNavDetail = () => {
    navigation.dispatch(state => {
      // Remove all the screens after `Profile`
      let sliceIndex = state.routes.findIndex(
        route_ => isDetailCardRoute(route_) && route_.params.id === cardData.cardId,
      );
      let haveDetail = sliceIndex !== -1;

      if (!haveDetail) {
        /**
         * Kiểm tra xem trong danh sách routes đã tồn tại detail card gần nhất nhưng không phải @var cardData.cardId
         * => Push thêm route mới của card với mục đích:
         * URD: Back về màn chi tiết thẻ chính trước đó khách hàng đang xem.
         *
         * */
        sliceIndex = -1;
        for (let i = state.routes.length - 1; i >= 0; i--) {
          if (isDetailCardRoute(state.routes[i]) || state.routes[i].name === 'ListCardScreen') {
            sliceIndex = i;
            break;
          }
        }
      }
      const routes = state.routes.slice(0, sliceIndex + 1);

      if (!haveDetail) {
        routes.push({
          key: `DetailCardScreen-${Date.now()}`,
          name: 'DetailCardScreen',
          params: {
            id: cardData.cardId,
          },
        });
      }

      return CommonActions.reset({
        ...state,
        routes,
        index: routes.length - 1,
      });
    });
  };

  const onClose = () => {
    navigation.dispatch(state => {
      // Remove all the screens after `Profile`
      let sliceIndex = state.routes.findIndex(
        route_ => isDetailCardRoute(route_) && route_.params.id === cardData.cardId,
      );
      let haveDetail = sliceIndex !== -1;

      if (!haveDetail) {
        /**
         * Kiểm tra xem trong danh sách routes đã tồn tại ListCardScreen
         * URD: Back về entry point(list hoặc detail) khi người dùng thực thi chức năng mà bị fail
         *
         * */
        sliceIndex = -1;
        for (let i = state.routes.length - 1; i >= 0; i--) {
          if (state.routes[i].name === 'ListCardScreen') {
            sliceIndex = i;
            break;
          }
        }
      }
      const routes = state.routes.slice(0, sliceIndex + 1);

      return CommonActions.reset({
        ...state,
        routes,
        index: routes.length - 1,
      });
    });
  };

  const onActiveCard = () => {
    console.log('🚀 ~ onActiveCard ~ onActiveCard:');
  };

  const onDownload = async (url: string) => {
    // setDownloadProgress(0);

    // setDownloadProgress(30);
    let path = `${ReactNativeBlobUtil.fs.dirs.DocumentDir}/xxxx-file.pdf`;
    if (Platform.OS === 'android') {
      path = `${ReactNativeBlobUtil.fs.dirs.DownloadDir}/xxxx-file.pdf`;
    }
    hostSharedModule.d.domainService.addSpinnerRequest();
    ReactNativeBlobUtil.config({
      addAndroidDownloads: {
        useDownloadManager: true,
        path,
        title: 'xxxx-file',
      },
      path,
      IOSBackgroundTask: true,
    })
      .fetch('GET', url, {})
      .progress({count: 25}, (received, total) => {
        console.log('🚀 ~ .progress ~ received, total:', received, total);
        // setDownloadProgress(30 + Math.round((received * 70) / total));
      })
      .then(async res => {
        console.log('🚀 ~ onDownload ~ res:', res);
        // setDownloadProgress(null);
      })
      .finally(() => {
        hostSharedModule.d.domainService.addSpinnerCompleted();
      });
  };

  const generalInfoModel = useMemo(
    () => ({
      imageUrl: cardData.imageUrl,
      instrumentI18n: cardData.instrumentI18n,
      name: cardData.name,
      markedNumber: cardData.markedNumber,
      style: {marginTop: 16},
    }),
    [cardData],
  );

  const errorCode = useMemo(() => {
    if (!extendData.errorCode || typeof extendData.errorCode !== 'string') {
      return '';
    }
    if (
      [
        MSBErrorCode.NotFoundCardDetails,
        MSBErrorCode.NotValidDataFormat,
        MSBErrorCode.DefaultFromTransactionSigning,
      ].includes(extendData.errorCode)
    ) {
      return MSBErrorCode.Default;
    } else {
      return extendData.errorCode;
    }
  }, [extendData.errorCode]);

  return {
    extendData,
    generalInfoModel,
    onNavDetail,
    callHotline,
    rightIconPress,
    onClose: onClose,
    errorCode,
    onActiveCard,
    onDownload,
  };
};

export default useAnnouncementResult;
