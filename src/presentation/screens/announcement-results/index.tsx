import RoundedBackgroundView from '@components/background-custom-view';
import {sharedStyleSheet} from '@components/common/styles.ts';
import GeneralInfoCard from '@components/general-info-card-view';
import HorizontalLineView from '@components/line-view';
import {MSBBackgroundFastImage, MSBPage, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import ButtonBottom from './components/button-bottom';
import FilesView from './components/files-view/index.tsx';
import MessageInfo from './components/message-info';
import StatusInfo from './components/status-info';
import useAnnouncementResult from './hook.ts';
import {styleSheet} from './styles.tsx';

const AnnouncementResultsScreen = () => {
  const {styles: sharedStyles} = useMSBStyles(sharedStyleSheet);
  const {styles} = useMSBStyles(styleSheet);

  const {
    extendData,
    callHotline,
    onNavDetail,
    rightIconPress,
    generalInfoModel,
    onClose,
    errorCode,
    onActiveCard,
    onDownload,
  } = useAnnouncementResult();

  return (
    <MSBPage
      testID="cm.announcementResults"
      style={styles.container}
      backgroundProps={{nameImage: 'bg_final'}}
      isScrollable={false}
      headerProps={{
        isLogo: true,
        rightButtons: [
          {
            icon: 'home',
            isBgIcon: true,
            onPress: rightIconPress,
          },
        ],
      }}>
      <View style={styles.subContainer}>
        <RoundedBackgroundView style={StyleSheet.flatten([styles.roundBg, sharedStyles.shadow])}>
          <MSBBackgroundFastImage
            nameImage="bg_decorative_motifs"
            resizeMode="center"
            style={styles.backgroundCardResult}>
            <StatusInfo status={extendData.status} date={extendData.date} type={extendData.type} />
            <HorizontalLineView style={styles.noMargin} />
            <View style={styles.cardView}>
              <GeneralInfoCard data={generalInfoModel} />
              <MessageInfo extendData={extendData} />
              {!!errorCode && <MSBTextBase style={styles.errorCode}>{errorCode}</MSBTextBase>}
            </View>
            <FilesView onDownload={onDownload} extendData={extendData} />
          </MSBBackgroundFastImage>
        </RoundedBackgroundView>
      </View>
      <ButtonBottom
        extendData={extendData}
        onNavDetail={onNavDetail}
        callHotline={callHotline}
        onClose={onClose}
        onActiveCard={onActiveCard}
      />
    </MSBPage>
  );
};

export default AnnouncementResultsScreen;
