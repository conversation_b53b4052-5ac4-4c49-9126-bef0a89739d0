import {CardDomainStatus} from '@entities/card/CardDomainStatus.ts';
import {SubCard} from '@entities/card/SubCard.ts';

export interface HandleNavToDetailListener {
  handleNavToDetailListener: (subCard: SubCard) => void;
}
export interface SubCardsBottomSheetProps extends HandleNavToDetailListener {
  subCards: SubCard[];
}

export interface SubCardProps extends HandleNavToDetailListener {
  cardId: string;
  status?: CardDomainStatus;
  name: string;
  image: string;
  markedNumber: string;
}
