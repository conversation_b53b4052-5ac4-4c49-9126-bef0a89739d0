import {cardAspectRatio} from '@constants/sizes';
import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorAlias, ColorDataView, Typography}) => ({
  container: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
    padding: 16,
    alignItems: 'center',
  },
  infoContainer: {flexDirection: 'row', alignItems: 'center', flex: 1},
  cardImage: {width: 76, aspectRatio: cardAspectRatio},
  detailContainer: {flexDirection: 'column', marginStart: 12},
  nameTxt: {...Typography?.small_medium, color: ColorDataView.TextSub},
  statusContainer: {
    width: '100%',
    marginTop: 4,
    flexDirection: 'row',
    alignContent: 'center',
  },
  numberCardValue: {
    ...Typography?.small_regular,
    color: Color<PERSON>lias.TextPrimary,
    marginStart: 4,
  },
  iconRight: {width: 20, height: 20},
}));
