import MSBFastImage from '@components/fast-image';
import StatusCardView from '@components/status-card-view';
import {Text} from '@components/text';
import {SubCard} from '@domain/entities/card/SubCard.ts';
import {BottomSheetFlatList} from '@gorhom/bottom-sheet';
import images from '@images';
import {translate} from '@locales';
import {isNotNullOrUndefined} from '@utils/StringFormat.tsx';
import {hostSharedModule} from 'msb-host-shared-module';
import {MSBIcon, MSBTouchable, useMSBStyles} from 'msb-shared-component';
import React, {useMemo} from 'react';
import {View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {styleSheet} from './styles.tsx';
import {HandleNavToDetailListener, SubCardsBottomSheetProps} from './type';

const SubCardsBottomSheet: React.FC<SubCardsBottomSheetProps> = ({subCards, handleNavToDetailListener}) => {
  const {bottom} = useSafeAreaInsets();

  return (
    <BottomSheetFlatList
      data={subCards}
      contentContainerStyle={{paddingBottom: bottom}}
      keyExtractor={(item, index) => `sub-card-${item.id}-${index}`}
      renderItem={({item}) => {
        return <SubCardItemView card={item} handleNavToDetailListener={handleNavToDetailListener} />;
      }}
    />
  );
};

const showSubCardBottomSheet = (
  subCards: SubCardsBottomSheetProps['subCards'],
  handleNavToDetailListener: SubCardsBottomSheetProps['handleNavToDetailListener'],
) => {
  return hostSharedModule.d.domainService.showBottomSheet({
    header: translate('detail_card.sub_card_bottom_sheet_title'),
    children: <SubCardsBottomSheet subCards={subCards} handleNavToDetailListener={handleNavToDetailListener} />,
  });
};

const SubCardItemView: React.FC<{
  card: SubCard;
  handleNavToDetailListener: HandleNavToDetailListener['handleNavToDetailListener'];
}> = ({card, handleNavToDetailListener}) => {
  const {styles} = useMSBStyles(styleSheet);

  const props = useMemo(
    () => ({
      cardId: card.id,
      status: card.status,
      name: card.holder.name,
      image: card.cardVisual?.images[0]?.imageURL,
      markedNumber: card.maskedNumber,
    }),
    [],
  );
  return (
    <View style={styles.container}>
      <MSBTouchable
        testID={`cm.bottom-sheet.sub-card.${props.cardId}`}
        style={styles.infoContainer}
        onPress={() => {
          try {
            hostSharedModule.d.domainService.hideBottomSheet();
            handleNavToDetailListener(card);
          } catch (e) {
            console.log(e);
          }
        }}>
        <MSBFastImage
          style={styles.cardImage}
          defaultSource={images.card_default}
          resizeMode="contain"
          source={{
            uri: props.image,
          }}
        />
        <View style={styles.detailContainer}>
          <Text style={styles.nameTxt}>{props.name}</Text>
          <View style={styles.statusContainer}>
            {isNotNullOrUndefined(props.status) && <StatusCardView status={props.status} />}
            <Text style={styles.numberCardValue}>{props.markedNumber}</Text>
          </View>
        </View>
      </MSBTouchable>
      <MSBIcon icon="right-orange" iconSize={20} />
    </View>
  );
};

export default showSubCardBottomSheet;
