import {CardOpenFormData} from '@domain/entities/form';
import {SelectionItem} from '@entities/base/SelectionItem';
import {CardInstrument} from '@entities/card/CardInstrument';
import {create} from 'zustand';
import {useShallow} from 'zustand/react/shallow';

interface CardOpenFlowStateData {
  cache: Record<string, SelectionItem[]>;
  formState: Record<string, CardOpenFormData>;
}

export const initCardOpenFormData: CardOpenFormData = {
  loading: false,
  error: undefined,
  instrument: CardInstrument.VIRTUAL,
  province: null,
  district: null,
  ward: null,
  address: '',
  referralInformation: null,
  defaultSourceAccount: '',
  sourceAccount: null,
  hasSecurityQuestion: true,
  secretQuestion: '',
  referralCode: '',
  card: null,
};

interface CardOpenFlowActions {
  // Actions
  setFormState: (id: string, formData?: Partial<CardOpenFormData>) => void;
  getFormState: (id: string) => CardOpenFormData;
  resetFormState: (id: string) => void;
  setLoading: (id: string, isLoading: boolean) => void;
  // Initialize a card entry if it doesn't exist
  initialize: (id: string) => void;
  resetCache: (id: string) => void;
  getCache: (id: string) => SelectionItem[];
  setCache: (id: string, cache: SelectionItem[]) => void;
  // Dispose method to reset all state
  dispose: () => void;
}

type CardOpenFlow = CardOpenFlowStateData & CardOpenFlowActions;

const initialState: CardOpenFlowStateData = {
  cache: {},
  formState: {},
};

const useCardOpenFlowStore = create<CardOpenFlow>((set, get) => ({
  ...initialState,
  // Dispose method to reset all state
  setFormState: (id, formData) => {
    set(state => {
      // Initialize if needed
      if (!state.formState[id]) {
        get().initialize(id);
      }
      const updatedFormState = get().formState;
      return {
        formState: {
          ...updatedFormState,
          [id]: {
            ...updatedFormState[id],
            ...formData,
          },
        },
      };
    });
  },
  getFormState: id => {
    const state = get();
    return state.formState[id] ?? initCardOpenFormData;
  },
  resetFormState: id => {
    set(state => ({
      formState: {
        ...state.formState,
        [id]: initCardOpenFormData,
      },
    }));
  },
  // Set loading state for a specific ID
  setLoading: (id, isLoading) =>
    set(state => {
      // Initialize if needed
      if (!state.formState[id]) {
        get().initialize(id);
      }

      return {
        formState: {
          ...state.formState,
          [id]: {
            ...state.formState[id],
            loading: isLoading,
          },
        },
      };
    }),
  initialize: id => {
    set(state => {
      if (state.formState[id]) {
        return state;
      }
      return {
        formState: {
          ...state.formState,
          [id]: initCardOpenFormData,
        },
      };
    });
  },
  resetCache: id => {
    set(state => {
      const newCache = {...state.cache};
      delete newCache[id];
      return {
        cache: newCache,
      };
    });
  },
  getCache: id => {
    const state = get();
    return state.cache[id] || [];
  },
  setCache: (id, cache) => {
    set(state => {
      return {
        cache: {
          ...state.cache,
          [id]: cache,
        },
      };
    });
  },
  dispose: () => {
    console.log('[CardOpenFlowStore]: dispose');
    set(initialState);
  },
}));

const useCardOpenFlowActions = () =>
  useCardOpenFlowStore(
    useShallow(state => {
      return {
        getCache: state.getCache,
        setCache: state.setCache,
        setFormState: state.setFormState,
        getFormState: state.getFormState,
        resetFormState: state.resetFormState,
        setLoading: state.setLoading,
      };
    }),
  );

const useCardOpenProcessing = (id: string) =>
  useCardOpenFlowStore(
    useShallow(state => {
      const formState = state.formState[id];
      return formState?.loading ?? false;
    }),
  );

export {useCardOpenFlowActions, useCardOpenFlowStore, useCardOpenProcessing};
