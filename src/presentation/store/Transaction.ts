import {FormTransactionFilter} from '@domain/entities/form/FormTransactionFilter';
import {create} from 'zustand';
import {useShallow} from 'zustand/react/shallow';

interface TransactionStateData {
  formFilterTransaction: Record<string, Partial<FormTransactionFilter>>;
}

interface TransactionStateActions {
  dispose: () => void;
  setFormFilterTransaction: (id: string, filter: Partial<FormTransactionFilter>) => void;
  getFormFilterTransaction: (id: string) => Partial<FormTransactionFilter> | undefined;
  resetFormFilterTransaction: (id: string) => void;
}

type TransactionState = TransactionStateData & TransactionStateActions;

const initialState: TransactionStateData = {
  formFilterTransaction: {},
};

const useTransactionStore = create<TransactionState>((set, get) => ({
  ...initialState,
  // Dispose method to reset all state
  dispose: () => {
    console.log('[TransactionStore]: dispose');
    set(initialState);
  },
  setFormFilterTransaction: (id, filter) =>
    set(state => ({
      formFilterTransaction: {
        ...state.formFilterTransaction,
        [id]: filter,
      },
    })),
  getFormFilterTransaction: id => {
    const state = get();
    return state.formFilterTransaction[id];
  },
  resetFormFilterTransaction: id =>
    set(state => {
      const {[id]: remove, ...rest} = state.formFilterTransaction;
      console.log('[resetFormFilterTransaction] remove:', remove);
      return {formFilterTransaction: rest};
    }),
}));

const useTransactionActions = () =>
  useTransactionStore(
    useShallow(state => {
      return {
        setFormFilterTransaction: state.setFormFilterTransaction,
        resetFormFilterTransaction: state.resetFormFilterTransaction,
        getFormFilterTransaction: state.getFormFilterTransaction,
      };
    }),
  );

export {useTransactionActions, useTransactionStore};
