import {useAmountInputFocusStore} from '../AmountInputFocus';

describe('useAmountInputFocusStore', () => {
  beforeEach(() => {
    // Ensure fresh state before each test
    useAmountInputFocusStore.getState().dispose();
  });

  it('should have initial state', () => {
    const state = useAmountInputFocusStore.getState();
    expect(state.amountSuggestList).toEqual([]);
    expect(state.isAmountInputNameFocused).toBeUndefined();
  });

  it('setIsFocused should update isAmountInputNameFocused', () => {
    useAmountInputFocusStore.getState().setIsFocused('inputA');

    const state = useAmountInputFocusStore.getState();
    expect(state.isAmountInputNameFocused).toBe('inputA');
  });

  it('resetFocus should reset isAmountInputNameFocused to undefined', () => {
    const store = useAmountInputFocusStore.getState();
    store.setIsFocused('inputB');
    store.resetFocus();

    const state = useAmountInputFocusStore.getState();
    expect(state.isAmountInputNameFocused).toBeUndefined();
  });

  it('setAmountSuggestList should update amountSuggestList', () => {
    const suggestions = ['1,000', '2,000', '5,000'];

    useAmountInputFocusStore.getState().setAmountSuggestList(suggestions);

    const state = useAmountInputFocusStore.getState();
    expect(state.amountSuggestList).toEqual(suggestions);
  });

  it('dispose should reset state to initial values', () => {
    const store = useAmountInputFocusStore.getState();
    store.setIsFocused('inputX');
    store.setAmountSuggestList(['10,000', '20,000']);
    store.dispose();

    const state = useAmountInputFocusStore.getState();
    expect(state.amountSuggestList).toEqual([]);
    expect(state.isAmountInputNameFocused).toBeUndefined();
  });
});
