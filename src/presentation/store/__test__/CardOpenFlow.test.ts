import {SelectionItem} from '@domain/entities/base/SelectionItem';
import {CardInstrument} from '@domain/entities/card/CardInstrument';
import {initCardOpenFormData, useCardOpenFlowStore} from '../CardOpenFlow';

describe('useCardOpenFlowStore', () => {
  beforeEach(() => {
    // reset store state before each test
    useCardOpenFlowStore.getState().dispose();
  });

  it('should initialize with empty cache and formState', () => {
    const state = useCardOpenFlowStore.getState();
    expect(state.cache).toEqual({});
    expect(state.formState).toEqual({});
  });

  it('should set and get cache', () => {
    const cacheData: SelectionItem[] = [{id: 1, name: 'Item 1'}];
    useCardOpenFlowStore.getState().setCache('card1', cacheData);

    const result = useCardOpenFlowStore.getState().getCache('card1');
    expect(result).toEqual(cacheData);
  });

  it('should reset cache for given id', () => {
    const cacheData: SelectionItem[] = [{id: 1, name: 'Item 1'}];
    const store = useCardOpenFlowStore.getState();
    store.setCache('card1', cacheData);
    store.resetCache('card1');

    expect(useCardOpenFlowStore.getState().getCache('card1')).toEqual([]);
  });

  it('should initialize and set form state', () => {
    const initState = {
      loading: true,
      error: undefined,
      instrument: CardInstrument.PHYSICAL,
    };
    useCardOpenFlowStore.getState().setFormState('card1', initState);

    const state = useCardOpenFlowStore.getState();
    expect(state.formState['card1']).toEqual({
      ...initCardOpenFormData,
      loading: true,
      instrument: CardInstrument.PHYSICAL,
    });
  });

  it('should reset form state to default values', () => {
    const store = useCardOpenFlowStore.getState();
    store.setFormState('card1', {loading: true, instrument: CardInstrument.PHYSICAL});
    store.resetFormState('card1');

    const form = useCardOpenFlowStore.getState().formState['card1'];
    expect(form).toEqual(initCardOpenFormData);
  });

  it('should set loading state for a form entry', () => {
    const store = useCardOpenFlowStore.getState();
    store.setLoading('card1', true);

    expect(useCardOpenFlowStore.getState().formState['card1'].loading).toBe(true);

    useCardOpenFlowStore.getState().setLoading('card1', false);

    expect(useCardOpenFlowStore.getState().formState['card1'].loading).toBe(false);
  });

  it('should call dispose and reset the whole state', () => {
    const store = useCardOpenFlowStore.getState();
    store.setCache('card1', [{id: 1, name: 'Item'}]);
    store.setFormState('card1', {loading: true, instrument: CardInstrument.VIRTUAL});
    store.dispose();

    const state = useCardOpenFlowStore.getState();
    expect(state.cache).toEqual({});
    expect(state.formState).toEqual({});
  });
});
