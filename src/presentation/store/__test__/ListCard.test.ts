import {SlideTab<PERSON>arKey} from '@app-screens/list-card/types';
import {OwnerShip} from '@domain/entities/card/OwnerShip';
import {Card} from '@entities/card/Card';
import {CardInstrument} from '@entities/card/CardInstrument';
import {CardType} from '@entities/card/CardType';
import {TabViewRoute} from '@entities/card/ListCard';
import {act, renderHook} from '@testing-library/react-hooks';
import {
  useListingCardActionSelectors,
  useListingCardStore,
  useTabSceneSelectors,
  useTabViewSelectors,
} from '../ListCard';

/**
 * Always use @method useListingCardStore.getState() to get latest state in @method jest.expect()
 */
describe('useListingCardStore', () => {
  afterEach(() => {
    // Reset store state after each test
    useListingCardStore.getState().dispose();
  });

  describe('useListingCardStore', () => {
    it('initialize with initial state', () => {
      const state = useListingCardStore.getState();
      expect(state.topTabbar).toEqual([]);
      expect(state.tabViewIndex).toBe(0);
      expect(state.debitCards).toEqual([]);
      expect(state.creditCards).toEqual([]);
      expect(state.loading).toBe(true);
      expect(state.error).toBe(false);
    });

    it('update topTabbar when calling setTopTabbar', () => {
      const store = useListingCardStore.getState();
      const mockTabbar: TabViewRoute[] = [
        {key: 'tab1', title: 'Tab 1'},
        {key: 'tab2', title: 'Tab 2'},
      ];

      store.setTopTabbar(mockTabbar);

      expect(useListingCardStore.getState().topTabbar).toEqual(mockTabbar);
    });

    it('update tabViewIndex when calling setTabViewIndex', () => {
      const store = useListingCardStore.getState();

      store.setTabViewIndex(1);

      expect(useListingCardStore.getState().tabViewIndex).toBe(1);
    });

    it('update debitCards when calling setDebitCards', () => {
      const store = useListingCardStore.getState();
      const mockCards: Card[] = [
        {id: 'card-1', type: CardType.Debit, name: 'Debit Card 1'} as Card,
        {id: 'card-2', type: CardType.Debit, name: 'Debit Card 2'} as Card,
      ];

      store.setDebitCards(mockCards);

      expect(useListingCardStore.getState().debitCards).toEqual(mockCards);
    });

    it('update creditCards when calling setCreditCards', () => {
      const store = useListingCardStore.getState();
      const mockCards: Card[] = [
        {id: 'card-1', type: CardType.Credit, name: 'Credit Card 1'} as Card,
        {id: 'card-2', type: CardType.Credit, name: 'Credit Card 2'} as Card,
      ];

      store.setCreditCards(mockCards);

      expect(useListingCardStore.getState().creditCards).toEqual(mockCards);
    });

    it('update loading when calling setLoading', () => {
      const store = useListingCardStore.getState();

      store.setLoading(false);

      expect(useListingCardStore.getState().loading).toBe(false);
    });

    it('update error when calling setError', () => {
      const store = useListingCardStore.getState();

      store.setError(true);

      expect(useListingCardStore.getState().error).toBe(true);
    });

    it('update debit card in list when calling updateCardInList', () => {
      const store = useListingCardStore.getState();
      const mockCards: Card[] = [
        {id: 'card-1', type: CardType.Debit, name: 'Debit Card 1'} as Card,
        {id: 'card-2', type: CardType.Debit, name: 'Debit Card 2'} as Card,
      ];
      store.setDebitCards(mockCards);

      const updatedCard: Card = {
        id: 'card-1',
        type: CardType.Debit,
        name: 'Updated Debit Card 1',
      } as Card;

      store.updateCardInList(updatedCard);

      expect(useListingCardStore.getState().debitCards[0].name).toBe('Updated Debit Card 1');
      expect(useListingCardStore.getState().debitCards[1].name).toBe('Debit Card 2');
    });

    it('update credit cards in the list when calling updateCardInList', () => {
      const store = useListingCardStore.getState();
      const mockCards: Card[] = [
        {id: 'card-1', type: CardType.Credit, name: 'Credit Card 1'} as Card,
        {id: 'card-2', type: CardType.Credit, name: 'Credit Card 2'} as Card,
      ];
      store.setCreditCards(mockCards);

      const updatedCard: Card = {
        id: 'card-1',
        type: CardType.Credit,
        name: 'Updated Credit Card 1',
      } as Card;

      store.updateCardInList(updatedCard);

      expect(useListingCardStore.getState().creditCards[0].name).toBe('Updated Credit Card 1');
      expect(useListingCardStore.getState().creditCards[1].name).toBe('Credit Card 2');
    });

    it('no state change when updateCardInList with unknown card type', () => {
      const store = useListingCardStore.getState();
      const mockDebitCards: Card[] = [
        {
          id: 'card-1',
          type: CardType.Debit,
          name: 'Debit Card 1',
          brand: '',
          ownership: OwnerShip.Main,
          holder: {
            name: '',
          },
          currency: '',
          maskedNumber: '',
          cardVisual: {images: []},
          instrument: CardInstrument.VIRTUAL,
          instrumentI18n: '',
          productCode: '',
          productId: '',
          rbsNumber: '',
          features: [],
          subCards: [],
          benefits: [],
        },
      ];
      const mockCreditCards: Card[] = [
        {
          id: 'card-2',
          type: CardType.Credit,
          name: 'Credit Card 2',
          brand: '',
          ownership: OwnerShip.Main,
          holder: {
            name: '',
          },
          currency: '',
          maskedNumber: '',
          cardVisual: {images: []},
          instrument: CardInstrument.VIRTUAL,
          instrumentI18n: '',
          productCode: '',
          productId: '',
          rbsNumber: '',
          features: [],
          subCards: [],
          benefits: [],
        },
      ];
      store.setDebitCards(mockDebitCards);
      store.setCreditCards(mockCreditCards);

      const updatedCard: Card = {
        id: 'card-1',
        type: 'UNKNOWN' as any,
        name: 'Updated Card',
        brand: '',
        ownership: OwnerShip.Main,
        holder: {
          name: '',
        },
        currency: '',
        maskedNumber: '',
        cardVisual: {images: []},
        instrument: CardInstrument.VIRTUAL,
        instrumentI18n: '',
        productCode: '',
        productId: '',
        rbsNumber: '',
        features: [],
        subCards: [],
        benefits: [],
      };

      store.updateCardInList(updatedCard);

      expect(useListingCardStore.getState().debitCards[0].name).toBe('Debit Card 1');
      expect(useListingCardStore.getState().creditCards[0].name).toBe('Credit Card 2');
    });

    it('reset state to initial value when calling dispose', () => {
      const store = useListingCardStore.getState();
      const mockTabbar: TabViewRoute[] = [
        {key: 'tab1', title: 'Tab 1'},
        {key: 'tab2', title: 'Tab 2'},
      ];
      const mockCards: Card[] = [{id: 'card-1', type: CardType.Debit, name: 'Debit Card 1'} as Card];

      store.setTopTabbar(mockTabbar);
      store.setTabViewIndex(1);
      store.setDebitCards(mockCards);
      store.setLoading(false);
      store.setError(true);

      store.dispose();

      expect(store.topTabbar).toEqual([]);
      expect(store.tabViewIndex).toBe(0);
      expect(store.debitCards).toEqual([]);
      expect(store.creditCards).toEqual([]);
      expect(store.loading).toBe(true);
      expect(store.error).toBe(false);
    });
  });

  describe('useTabSceneSelectors', () => {
    it('returns a list of credit cards when the key is Credits', () => {
      const store = useListingCardStore.getState();
      const mockCreditCards: Card[] = [{id: 'card-1', type: CardType.Credit, name: 'Credit Card 1'} as Card];
      store.setCreditCards(mockCreditCards);

      const {result} = renderHook(() => useTabSceneSelectors(SlideTabBarKey.Credits));

      expect(result.current.cards).toEqual(mockCreditCards);
    });

    it('returns a list of debit cards when the key is Debits', () => {
      const store = useListingCardStore.getState();
      const mockDebitCards: Card[] = [{id: 'card-1', type: CardType.Debit, name: 'Debit Card 1'} as Card];
      store.setDebitCards(mockDebitCards);

      const {result} = renderHook(() => useTabSceneSelectors(SlideTabBarKey.Debits));

      expect(result.current.cards).toEqual(mockDebitCards);
    });
  });

  describe('useTabViewSelectors', () => {
    it('isEmpty returns true when there is no tabbar or no tag', () => {
      const {result} = renderHook(() => useTabViewSelectors());

      expect(result.current.isEmpty).toBe(true);
    });

    it('isEmpty returns false when there is a tabbar and there is a tag', () => {
      const store = useListingCardStore.getState();
      const mockTabbar: TabViewRoute[] = [{key: 'tab1', title: 'Tab 1'}];
      const mockCards: Card[] = [{id: 'card-1', type: CardType.Debit, name: 'Debit Card 1'} as Card];

      store.setTopTabbar(mockTabbar);
      store.setDebitCards(mockCards);

      const {result} = renderHook(() => useTabViewSelectors());

      expect(result.current.isEmpty).toBe(false);
    });

    it('return the correct state values', () => {
      const store = useListingCardStore.getState();
      const mockTabbar: TabViewRoute[] = [{key: 'tab1', title: 'Tab 1'}];

      store.setTopTabbar(mockTabbar);
      store.setTabViewIndex(1);
      store.setLoading(false);
      store.setError(true);

      const {result} = renderHook(() => useTabViewSelectors());

      expect(result.current.topTabbar).toEqual(mockTabbar);
      expect(result.current.tabViewIndex).toBe(1);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(true);
    });
  });

  describe('useListingCardActionSelectors', () => {
    it('return all action selectors', () => {
      const {result} = renderHook(() => useListingCardActionSelectors());

      expect(result.current).toEqual({
        setTopTabbar: expect.any(Function),
        setTabViewIndex: expect.any(Function),
        setDebitCards: expect.any(Function),
        setCreditCards: expect.any(Function),
        setLoading: expect.any(Function),
        setError: expect.any(Function),
        updateCardInList: expect.any(Function),
        dispose: expect.any(Function),
      });
    });

    it('action selectors work correctly', () => {
      const {result} = renderHook(() => useListingCardActionSelectors());
      const mockTabbar: TabViewRoute[] = [{key: 'tab1', title: 'Tab 1'}];
      const mockDebitCards: Card[] = [{id: 'card-1', type: CardType.Debit, name: 'Debit Card 1'} as Card];
      const mockCreditCards: Card[] = [{id: 'card-2', type: CardType.Credit, name: 'Credit Card 2'} as Card];

      act(() => {
        result.current.setTopTabbar(mockTabbar);
        result.current.setTabViewIndex(1);
        result.current.setDebitCards(mockDebitCards);
        result.current.setCreditCards(mockCreditCards);
        result.current.setLoading(false);
        result.current.setError(true);
      });

      const store = useListingCardStore.getState();
      expect(store.topTabbar).toEqual(mockTabbar);
      expect(store.tabViewIndex).toBe(1);
      expect(store.debitCards).toEqual(mockDebitCards);
      expect(store.creditCards).toEqual(mockCreditCards);
      expect(store.loading).toBe(false);
      expect(store.error).toBe(true);
    });

    it('updateCardInList works correctly', () => {
      const {result} = renderHook(() => useListingCardActionSelectors());
      const mockDebitCards: Card[] = [{id: 'card-1', type: CardType.Debit, name: 'Debit Card 1'} as Card];

      act(() => {
        result.current.setDebitCards(mockDebitCards);
      });

      const updatedCard: Card = {
        id: 'card-1',
        type: CardType.Debit,
        name: 'Updated Debit Card 1',
      } as Card;

      act(() => {
        result.current.updateCardInList(updatedCard);
      });

      const store = useListingCardStore.getState();
      expect(store.debitCards[0].name).toBe('Updated Debit Card 1');
    });

    it('dispose works correctly', () => {
      const {result} = renderHook(() => useListingCardActionSelectors());
      const mockTabbar: TabViewRoute[] = [{key: 'tab1', title: 'Tab 1'}];

      act(() => {
        result.current.setTopTabbar(mockTabbar);
        result.current.setTabViewIndex(1);
      });

      act(() => {
        result.current.dispose();
      });

      const store = useListingCardStore.getState();
      expect(store.topTabbar).toEqual(mockTabbar);
      expect(store.tabViewIndex).toBe(1);
    });
  });
});
