import {FormTransactionFilter} from '@domain/entities/form/FormTransactionFilter';
import {getTransactionDateRangeOptions, getTransactionTypeOptions} from '@presentation/screens/transaction-filter/form';
import moment from 'moment';
import {useTransactionStore} from '../Transaction';

describe('useTransactionStore', () => {
  const filter: Partial<FormTransactionFilter> = {
    searchTerm: 'searchTerm',
    transactionType: getTransactionTypeOptions()[0],
    transactionDateRange: getTransactionDateRangeOptions()[0],
    fromDate: moment().subtract(7, 'days').toDate(),
    toDate: moment().toDate(),
    fromCurrency: '0',
    toCurrency: '999,999,999',
  };

  beforeEach(() => {
    // Reset store state before each test
    useTransactionStore.getState().dispose();
  });

  it('should set a form filter transaction', () => {
    useTransactionStore.getState().setFormFilterTransaction('form1', filter);

    const result = useTransactionStore.getState().getFormFilterTransaction('form1');
    expect(result).toEqual(filter);
  });

  it('should get an existing form filter transaction', () => {
    useTransactionStore.getState().setFormFilterTransaction('form2', filter);

    const result = useTransactionStore.getState().getFormFilterTransaction('form2');
    expect(result).toEqual(filter);
  });

  it('should return undefined for non-existent form filter transaction', () => {
    const result = useTransactionStore.getState().getFormFilterTransaction('nonexistent');
    expect(result).toBeUndefined();
  });

  it('should reset a specific form filter transaction', () => {
    useTransactionStore.getState().setFormFilterTransaction('form3', filter);
    useTransactionStore.getState().resetFormFilterTransaction('form3');

    const result = useTransactionStore.getState().getFormFilterTransaction('form3');
    expect(result).toBeUndefined();
  });

  it('should dispose and reset the entire store state', () => {
    useTransactionStore.getState().setFormFilterTransaction('form4', filter);
    useTransactionStore.getState().dispose();

    const result = useTransactionStore.getState().getFormFilterTransaction('form4');
    expect(result).toBeUndefined();

    expect(useTransactionStore.getState().formFilterTransaction).toEqual({});
  });
});
