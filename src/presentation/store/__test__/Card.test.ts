import {OwnerShip} from '@domain/entities/card/OwnerShip';
import {Card} from '@entities/card/Card';
import {CardInstrument} from '@entities/card/CardInstrument';
import {act, renderHook} from '@testing-library/react-hooks';
import {useCardDetailsStore, useCardRefreshSelectors, useCardSelectors} from '../Card';

describe('useCardDetailsStore', () => {
  afterEach(() => {
    // Reset store state after each test
    useCardDetailsStore.getState().dispose();
  });

  describe('useCardDetailsStore', () => {
    it('should initialize with empty cardDetails', () => {
      const state = useCardDetailsStore.getState();
      expect(state.cardDetails).toEqual({});
    });

    it('should initialize a card when initializeCard is called', () => {
      const store = useCardDetailsStore.getState();
      store.initializeCard('card-1');

      expect(store.getCardState('card-1')).toEqual({
        cardModel: undefined,
        loading: false,
        error: false,
        refreshing: false,
      });
    });

    it('should set card model for a specific ID', () => {
      const store = useCardDetailsStore.getState();
      const mockCard = {id: 'card-1', name: 'Test Card'} as Card;

      store.setCardModel('card-1', mockCard);

      expect(store.getCardState('card-1').cardModel).toEqual(mockCard);
    });

    it('should set loading state for a specific ID', () => {
      const store = useCardDetailsStore.getState();

      store.setLoading('card-1', true);

      expect(store.getCardState('card-1').loading).toBe(true);
    });

    it('should set error state for a specific ID', () => {
      const store = useCardDetailsStore.getState();

      store.setError('card-1', true);

      expect(store.getCardState('card-1').error).toBe(true);
    });

    it('should set refreshing state for a specific ID', () => {
      const store = useCardDetailsStore.getState();

      store.setRefreshing('card-1', true);

      expect(store.getCardState('card-1').refreshing).toBe(true);
    });

    it('should reset a card state', () => {
      const store = useCardDetailsStore.getState();
      const mockCard = {id: 'card-1', name: 'Test Card'} as Card;

      // Setup initial state
      store.setCardModel('card-1', mockCard);
      store.setLoading('card-1', true);
      store.setError('card-1', true);
      store.setRefreshing('card-1', true);

      // Reset the card
      store.resetCard('card-1');

      // Check if state is reset
      expect(store.getCardState('card-1')).toEqual({
        cardModel: undefined,
        loading: false,
        error: false,
        refreshing: false,
      });
    });

    it('should get a card state correctly', () => {
      const store = useCardDetailsStore.getState();
      const mockCard: Card = {
        id: 'card-1',
        name: 'Test Card',
        brand: '',
        ownership: OwnerShip.Main,
        holder: {
          name: '',
        },
        currency: '',
        maskedNumber: '',
        cardVisual: {images: []},
        instrument: CardInstrument.VIRTUAL,
        instrumentI18n: '',
        productCode: '',
        productId: '',
        rbsNumber: '',
        features: [],
        subCards: [],
        benefits: [],
      };

      // Setup initial state
      store.setCardModel('card-1', mockCard);

      // Check if state is correct
      expect(store.getCardState('card-1')).toEqual({
        cardModel: mockCard,
        error: false,
        loading: false,
        refreshing: false,
      });
    });

    it('should initialize a card if it does not exist when getting state', () => {
      const store = useCardDetailsStore.getState();

      // Get card state for non-existent card
      store.getCardState('non-existent-card');

      // Check if state is initialized
      expect(store.getCardState('non-existent-card')).toEqual({
        cardModel: undefined,
        loading: false,
        error: false,
        refreshing: false,
      });
    });

    it('should dispose all state', () => {
      const store = useCardDetailsStore.getState();

      // Setup initial state
      store.setCardModel('card-1', {id: 'card-1', name: 'Test Card'} as Card);
      store.setCardModel('card-2', {id: 'card-2', name: 'Test Card 2'} as Card);

      // Dispose
      store.dispose();

      // Check if state is reset
      expect(store.cardDetails).toEqual({});
    });
  });

  describe('useCardSelectors', () => {
    it('should return correct selectors for a specific card ID', () => {
      const {result} = renderHook(() => useCardSelectors('card-1'));

      expect(result.current).toEqual({
        setCardModel: expect.any(Function),
        setLoading: expect.any(Function),
        setError: expect.any(Function),
        setRefreshing: expect.any(Function),
        initializeCard: expect.any(Function),
        resetCard: expect.any(Function),
        cardModel: undefined,
        loading: undefined,
        error: undefined,
        refreshing: undefined,
      });
    });

    it('should update state when using selectors', () => {
      const {result} = renderHook(() => useCardSelectors('card-1'));
      const mockCard = {id: 'card-1', name: 'Test Card'} as Card;

      act(() => {
        result.current.setCardModel('card-1', mockCard);
      });

      expect(result.current.cardModel).toEqual(mockCard);
    });
  });

  describe('useCardRefreshSelectors', () => {
    it('should return only setCardModel selector', () => {
      const {result} = renderHook(() => useCardRefreshSelectors());

      expect(result.current).toEqual({
        setCardModel: expect.any(Function),
      });
    });

    it('should update card model when using selector', () => {
      const {result} = renderHook(() => useCardRefreshSelectors());
      const mockCard = {id: 'card-1', name: 'Test Card'} as Card;

      act(() => {
        result.current.setCardModel('card-1', mockCard);
      });

      const store = useCardDetailsStore.getState();
      expect(store.cardDetails['card-1'].cardModel).toEqual(mockCard);
    });
  });
});
