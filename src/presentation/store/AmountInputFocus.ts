import {create} from 'zustand';
import {useShallow} from 'zustand/react/shallow';

interface AmountInputFocusStateData {
  amountSuggestList: string[];
  isAmountInputNameFocused?: string;
}

interface AmountInputFocusStateActions {
  setIsFocused: (fieldName: string) => void;
  setAmountSuggestList: (list: string[]) => void;
  resetFocus: () => void;
  dispose: () => void;
}

type AmountInputFocusState = AmountInputFocusStateData & AmountInputFocusStateActions;

const initialState: AmountInputFocusStateData = {
  amountSuggestList: [],
  isAmountInputNameFocused: undefined,
};

const useAmountInputFocusStore = create<AmountInputFocusState>(set => ({
  ...initialState,

  setIsFocused: fieldName => set({isAmountInputNameFocused: fieldName}),
  resetFocus: () => set({isAmountInputNameFocused: undefined}),
  setAmountSuggestList: amountSuggestList => {
    set({amountSuggestList});
  },

  dispose: () => {
    console.log('[AmountInputFocusStore]: dispose');
    set(initialState);
  },
}));

const useAmountInputFocusActions = () =>
  useAmountInputFocusStore(
    useShallow(state => ({
      setIsFocused: state.setIsFocused,
      resetFocus: state.resetFocus,
      setAmountSuggestList: state.setAmountSuggestList,
    })),
  );
const useAmountInputFocused = () =>
  useAmountInputFocusStore(
    useShallow(state => ({
      isAmountInputNameFocused: state.isAmountInputNameFocused,
      amountSuggestList: state.amountSuggestList,
    })),
  );

export {useAmountInputFocusActions, useAmountInputFocused, useAmountInputFocusStore};
