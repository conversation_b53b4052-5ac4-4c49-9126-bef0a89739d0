import {Card} from '@entities/card/Card';
import {create} from 'zustand';
import {useShallow} from 'zustand/react/shallow';

interface CardDetailsStateData {
  cardDetails: Record<
    string,
    {
      cardModel?: Card;
      loading: boolean;
      error: boolean;
      refreshing: boolean;
    }
  >;
}

interface CardDetailsStateActions {
  // Actions
  setCardModel: (id: string, cardModel?: Card) => void;
  setLoading: (id: string, isLoading: boolean) => void;
  setError: (id: string, hasError: boolean) => void;
  setRefreshing: (id: string, isRefreshing: boolean) => void;

  // Initialize a card entry if it doesn't exist
  initializeCard: (id: string) => void;

  // Reset a card's state (e.g., when navigating away)
  resetCard: (id: string) => void;

  // Get a card's state - convenience method
  getCardState: (id: string) => {
    cardModel?: Card;
    loading: boolean;
    error: boolean;
    refreshing: boolean;
  };

  // Dispose method to reset all state
  dispose: () => void;
}

type CardDetailsState = CardDetailsStateData & CardDetailsStateActions;

const initialState: CardDetailsStateData = {
  cardDetails: {},
};

const useCardDetailsStore = create<CardDetailsState>((set, get) => ({
  ...initialState,

  // Initialize a card's state if it doesn't exist
  initializeCard: id =>
    set(state => {
      if (!state.cardDetails[id]) {
        return {
          cardDetails: {
            ...state.cardDetails,
            [id]: {
              cardModel: undefined,
              loading: false,
              error: false,
              refreshing: false,
            },
          },
        };
      }
      return state;
    }),

  // Set card model for a specific ID
  setCardModel: (id, cardModel) =>
    set(state => {
      // Initialize if needed
      if (!state.cardDetails[id]) {
        get().initializeCard(id);
      }
      const updatedCardDetails = get().cardDetails;
      return {
        cardDetails: {
          ...updatedCardDetails,
          [id]: {
            ...updatedCardDetails[id],
            cardModel,
          },
        },
      };
    }),

  // Set loading state for a specific ID
  setLoading: (id, isLoading) =>
    set(state => {
      // Initialize if needed
      if (!state.cardDetails[id]) {
        get().initializeCard(id);
      }

      return {
        cardDetails: {
          ...state.cardDetails,
          [id]: {
            ...state.cardDetails[id],
            loading: isLoading,
          },
        },
      };
    }),

  // Set error state for a specific ID
  setError: (id, hasError) =>
    set(state => {
      // Initialize if needed
      if (!state.cardDetails[id]) {
        get().initializeCard(id);
      }

      return {
        cardDetails: {
          ...state.cardDetails,
          [id]: {
            ...state.cardDetails[id],
            error: hasError,
          },
        },
      };
    }),

  // Set refreshing state for a specific ID
  setRefreshing: (id, isRefreshing) =>
    set(state => {
      // Initialize if needed
      if (!state.cardDetails[id]) {
        get().initializeCard(id);
      }

      return {
        cardDetails: {
          ...state.cardDetails,
          [id]: {
            ...state.cardDetails[id],
            refreshing: isRefreshing,
          },
        },
      };
    }),

  // Reset a card's state (e.g., when navigating away)
  resetCard: id =>
    set(state => ({
      cardDetails: {
        ...state.cardDetails,
        [id]: {
          cardModel: undefined,
          loading: false,
          error: false,
          refreshing: false,
        },
      },
    })),

  // Get a card's state - convenience method
  getCardState: id => {
    const state = get();
    // Initialize if needed
    if (!state.cardDetails[id]) {
      state.initializeCard(id);
    }
    return state.cardDetails[id];
  },

  // Dispose method to reset all state
  dispose: () => {
    console.log('[CardDetailsStore]: dispose');
    set(initialState);
  },
}));

const useCardSelectors = (id: string) =>
  useCardDetailsStore(
    useShallow(state => {
      const card = state.cardDetails[id] || {};
      return {
        setCardModel: state.setCardModel,
        setLoading: state.setLoading,
        setError: state.setError,
        setRefreshing: state.setRefreshing,
        initializeCard: state.initializeCard,
        resetCard: state.resetCard,
        cardModel: card.cardModel,
        loading: card.loading,
        error: card.error,
        refreshing: card.refreshing,
      };
    }),
  );

const useCardRefreshSelectors = () =>
  useCardDetailsStore(
    useShallow(state => {
      return {
        setCardModel: state.setCardModel,
      };
    }),
  );

export {useCardDetailsStore, useCardRefreshSelectors, useCardSelectors};
