// cardStore.ts
import {SlideTabBarKey} from '@app-screens/list-card/types';
import {Card} from '@entities/card/Card.ts';
import {CardType} from '@entities/card/CardType.ts';
import {TabViewRoute} from '@entities/card/ListCard';
import {create} from 'zustand';
import {useShallow} from 'zustand/react/shallow';

interface CardStateData {
  topTabbar: TabViewRoute[];
  tabViewIndex: number;
  debitCards: Card[];
  creditCards: Card[];
  loading: boolean;
  error: boolean;
}

interface CardStateActions {
  setTopTabbar: (tabbar: TabViewRoute[]) => void;
  setTabViewIndex: (index: number) => void;
  setDebitCards: (cards: Card[]) => void;
  setCreditCards: (cards: Card[]) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (hasError: boolean) => void;
  updateCardInList: (card: Card) => void;
  dispose: () => void;
}

type CardState = CardStateData & CardStateActions;

const initialState: CardStateData = {
  topTabbar: [],
  tabViewIndex: 0,
  debitCards: [],
  creditCards: [],
  loading: true,
  error: false,
};

export const useListingCardStore = create<CardState>(set => ({
  ...initialState,

  // Actions
  setTopTabbar: tabbar => set({topTabbar: tabbar}),
  setTabViewIndex: index => set({tabViewIndex: index}),
  setDebitCards: cards => set({debitCards: cards}),
  setCreditCards: cards => set({creditCards: cards}),
  setLoading: isLoading => set({loading: isLoading}),
  setError: hasError => set({error: hasError}),

  updateCardInList: card =>
    set(state => {
      if (card.type === CardType.Debit) {
        return {
          debitCards: state.debitCards.map(item => (item.id === card.id ? {...item, ...card} : item)),
        };
      }

      if (card.type === CardType.Credit) {
        return {
          creditCards: state.creditCards.map(item => (item.id === card.id ? {...item, ...card} : item)),
        };
      }

      return state;
    }),

  dispose: () => {
    console.log('[ListCardStore]: dispose');
  },
}));

export const useTabSceneSelectors = (key: string) =>
  useListingCardStore(
    useShallow(state => {
      return {
        cards: key === SlideTabBarKey.Credits ? state.creditCards : state.debitCards,
      };
    }),
  );

export const useTabViewSelectors = () =>
  useListingCardStore(
    useShallow(state => {
      return {
        isEmpty: !state.topTabbar?.length || (state.debitCards?.length ?? 0) + (state.creditCards?.length ?? 0) === 0,
        error: state.error,
        loading: state.loading,
        tabViewIndex: state.tabViewIndex,
        topTabbar: state.topTabbar,
      };
    }),
  );

export const useAssetCreditCardSelectors = () =>
  useListingCardStore(
    useShallow(state => {
      // const listCardOK = state.creditCards.filter(card => card.status !== CardDomainStatus.CardClosed);
      return {
        error: state.error,
        loading: state.loading,
      };
    }),
  );

export const useListingCardActionSelectors = () =>
  useListingCardStore(
    useShallow(state => {
      return {
        setTopTabbar: state.setTopTabbar,
        setTabViewIndex: state.setTabViewIndex,
        setDebitCards: state.setDebitCards,
        setCreditCards: state.setCreditCards,
        setLoading: state.setLoading,
        setError: state.setError,
        updateCardInList: state.updateCardInList,
        dispose: state.dispose,
      };
    }),
  );
