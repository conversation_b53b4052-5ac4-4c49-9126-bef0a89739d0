import {renderHook} from '@testing-library/react-hooks';
import usePrevious from '../usePrevious';

describe('usePrevious', () => {
  it('should return initialValue on first render', () => {
    const initialValue = 'initial';
    const {result} = renderHook(() => usePrevious('current', initialValue));

    expect(result.current).toBe(initialValue);
  });

  it('should return previous value after value changes', () => {
    const initialValue = 'initial';
    const {result, rerender} = renderHook(({value}) => usePrevious(value, initialValue), {
      initialProps: {value: 'first value'},
    });

    // On first render, it should return the initialValue
    expect(result.current).toBe(initialValue);

    // Update the value
    rerender({value: 'second value'});

    // After first update, it should return the first value
    expect(result.current).toBe('first value');

    // Update again
    rerender({value: 'third value'});

    // After second update, it should return the second value
    expect(result.current).toBe('second value');
  });

  it('should work with different types', () => {
    // Test with numbers
    const {result: numberResult, rerender: numberRerender} = renderHook(({value}) => usePrevious(value, 0), {
      initialProps: {value: 1},
    });

    expect(numberResult.current).toBe(0);
    numberRerender({value: 2});
    expect(numberResult.current).toBe(1);

    // Test with objects
    const initialObject = {key: 'initial'};
    const firstObject = {key: 'first'};
    const secondObject = {key: 'second'};

    const {result: objectResult, rerender: objectRerender} = renderHook(
      ({value}) => usePrevious(value, initialObject),
      {initialProps: {value: firstObject}},
    );

    expect(objectResult.current).toBe(initialObject);
    objectRerender({value: secondObject});
    expect(objectResult.current).toBe(firstObject);
  });
});
