import {translate} from '@locales';
import {renderHook} from '@testing-library/react-hooks';
import {formatMoney} from '@utils/StringFormat';
import {useErrorMessageTranslation, ValidateMessageObject} from '../useErrorMessageTranslation';

describe('useErrorMessageTranslation', () => {
  it('should return undefined if no message is provided', () => {
    const {result} = renderHook(() => useErrorMessageTranslation());
    expect(result.current).toBeUndefined();
  });

  it('should return translated string for plain key message', () => {
    const {result} = renderHook(() => useErrorMessageTranslation('select_card_to_open.insufficient_minimum_balance'));

    expect(result.current).toBe(translate('select_card_to_open.insufficient_minimum_balance'));
  });

  it('should return translated string from JSON string with keyT only', () => {
    const msgObject: ValidateMessageObject = {
      keyT: 'select_card_to_open.insufficient_minimum_balance',
    };

    const {result} = renderHook(() => useErrorMessageTranslation(JSON.stringify(msgObject)));

    expect(result.current).toBe(translate('select_card_to_open.insufficient_minimum_balance'));
  });

  it('should merge options and optionsTx and translate', () => {
    const msgObject: ValidateMessageObject = {
      keyT: 'select_card_to_open.insufficient_minimum_balance',
      options: {
        amount: formatMoney(50000),
      },
      optionsTx: {
        amount: 'common.app_version',
      },
    };

    const {result} = renderHook(() => useErrorMessageTranslation(JSON.stringify(msgObject)));

    /**
     * @param {string} optionsTx has priority over @param options if both are provided
     */
    expect(result.current).toBe(
      translate('select_card_to_open.insufficient_minimum_balance', {
        amount: translate('common.app_version'),
      }),
    );
  });

  it('should translate message as key of localization if JSON parse fails', () => {
    const {result} = renderHook(() => useErrorMessageTranslation('select_card_to_open.insufficient_minimum_balance'));

    expect(result.current).toBe(translate('select_card_to_open.insufficient_minimum_balance'));
  });
});
