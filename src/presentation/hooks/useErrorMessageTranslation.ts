import {I18nKeys, translate} from '@locales';
import {useMemo} from 'react';

export type ValidateMessageObject = {
  keyT: I18nKeys;
  optionsTx?: Record<string, I18nKeys>;
  options?: Record<string, string | number>;
};

/**
 * This custom hook allows to get translation from a message
 *
 * Using with translation object
 * @example
 * ```tsx
 * const messageObject: ValidateMessageObject<ScreenIds.ListingLocationConfirmationScreen> =
 *  {
 *    key: 'validation:notValidZipCode',
 *  };
 *
 * const transformed = useErrorMessageTranslation(JSON.stringify(messageObject))
 * ```
 *
 * Using with single message
 *
 * @example
 * ```tsx
 * const transformed = useErrorMessageTranslation('validation:notValidZipCode')
 * ```
 *
 */
export const useErrorMessageTranslation = (msg?: string) => {
  const parsed = useMemo<ValidateMessageObject | undefined>(() => {
    if (!msg) {
      return undefined;
    }

    try {
      return JSON.parse(msg);
    } catch {
      return undefined;
    }
  }, [msg]);

  return useMemo<string | undefined>(() => {
    if (!parsed && typeof msg === 'string') {
      return translate(msg as I18nKeys);
    }

    if (!parsed) {
      return undefined;
    }

    const optionsTx: Record<string, string> = {};

    if (parsed.optionsTx) {
      Object.keys(parsed.optionsTx).forEach(key => {
        optionsTx[key] = translate(String((parsed.optionsTx as Record<string, string | number>)[key]) as I18nKeys);
      });
    }

    return translate(parsed.keyT, {...(parsed.options ?? {}), ...optionsTx});
  }, [parsed, msg]);
};
