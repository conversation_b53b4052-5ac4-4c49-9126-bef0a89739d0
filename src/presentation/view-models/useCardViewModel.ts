import {RootStackParamList} from '@app-navigation/types';
import {showCardSecretInfoBottomSheet} from '@components/bottom-sheet/card-secret-info';
import {HOTLINE} from '@constants';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {isLeft, isRight} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {OwnerShip} from '@domain/entities/card/OwnerShip';
import {CommonStateStatus} from '@entities/base/CommonState';
import {
  AnnouncementResultsParams,
  IAnnouncementExtendData,
  mapCardToCardDataNoticeScreen,
} from '@entities/card/AnnouncementResults';
import {Card} from '@entities/card/Card';
import {CardDomainStatus, isNeedActiveCard, STATUS_GROUPS} from '@entities/card/CardDomainStatus';
import {CardFeature} from '@entities/card/CardFeature';
import {CardFeatureType} from '@entities/card/CardFeatureType';
import {CreatePinParams} from '@entities/card/CreatePinParams';
import {EAnnouncementResultsStatus, EAnnouncementResultsType} from '@entities/card/EAnnouncementResults';
import {Way4CardStatusCode} from '@entities/card/Way4CardStatusCode';
import {translate} from '@locales';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useCardRefreshSelectors} from '@store/Card';
import {useListingCardActionSelectors} from '@store/ListCard';
import {showPopup, waitForShowingModal} from '@utils';
import {showErrorCodePopup} from '@utils/CommonHandler';
import {hostSharedModule} from 'msb-host-shared-module';
import {Linking} from 'react-native';

const useCardViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const {updateCardInList} = useListingCardActionSelectors();
  const {setCardModel} = useCardRefreshSelectors();

  const handleChangeStatusAction = (cardModel: Card) => {
    if (isNeedActiveCard(cardModel?.status)) {
      // Kích hoạt thẻ
      if (cardModel.ownership === OwnerShip.Sub) {
        showPopup({
          iconType: 'WARNING',
          title: translate('card.active_sub_card_title'),
          content: translate('card.active_sub_card_desc'),
          confirmBtnText: translate('feature_card.active'),
          cancelBtnText: translate('card.close'),
          verticalAlign: false,
          onConfirmAfterAnimation: () => {
            changeCardStatus({
              cardModel,
              newStatus: Way4CardStatusCode.CardOK,
              announcementResultsType: EAnnouncementResultsType.activeCard,
            });
          },
          onConfirm: hostSharedModule.d.domainService.addSpinnerRequest,
        });
      } else {
        changeCardStatus({
          cardModel,
          newStatus: Way4CardStatusCode.CardOK,
          announcementResultsType: EAnnouncementResultsType.activeCard,
          showLoading: true,
        });
      }
    } else if (cardModel?.status === CardDomainStatus.Active) {
      // Khóa thẻ
      showPopup({
        iconType: 'WARNING',
        title: translate('card.lock_card_title'),
        content: translate('card.lock_card_desc'),
        confirmBtnText: translate('card.confirm'),
        cancelBtnText: translate('card.close'),
        verticalAlign: false,
        onConfirm: hostSharedModule.d.domainService.addSpinnerRequest,
        onConfirmAfterAnimation: () => {
          changeCardStatus({
            cardModel,
            newStatus: Way4CardStatusCode.CustomerBlock,
            announcementResultsType: EAnnouncementResultsType.customerLockCard,
            skipTransactionSigning: true,
          });
        },
      });
    } else if (
      !cardModel?.status ||
      [...STATUS_GROUPS.BLOCKED, CardDomainStatus.BlockCardByCustomer].includes(cardModel?.status)
    ) {
      // Gọi hotline
      showPopup({
        iconType: 'WARNING',
        title: translate('card.unlock_card_need_call_hotline_title'),
        content: translate('card.unlock_card_need_call_hotline_desc'),
        confirmBtnText: translate('card.call_hotline'),
        cancelBtnText: translate('card.close'),
        verticalAlign: true,
        onConfirm: callHotline,
      });
    } else {
      // Mở khóa thẻ
      showPopup({
        iconType: 'WARNING',
        title: translate('card.unlock_card_title'),
        content: translate('card.unlock_card_desc'),
        confirmBtnText: translate('card.confirm'),
        cancelBtnText: translate('card.close'),
        verticalAlign: false,
        onConfirm: hostSharedModule.d.domainService.addSpinnerRequest,
        onConfirmAfterAnimation: () => {
          changeCardStatus({
            cardModel,
            newStatus: Way4CardStatusCode.CardOK,
            announcementResultsType: EAnnouncementResultsType.unlockCard,
          });
        },
      });
    }
  };

  // Xử lý các tính năng của thẻ
  const handleCardFeatureAction = (feature: CardFeature, cardModel: Card) => {
    switch (feature?.id) {
      case CardFeatureType.ChangeStatus:
        handleChangeStatusAction(cardModel);
        break;

      case CardFeatureType.SecurityInfo:
        getSecretInfo(cardModel);
        break;

      case CardFeatureType.NewPin:
        createNewPin(cardModel);
        break;

      case CardFeatureType.LinkedAccount:
        navigation.navigate('UpdateDebitPaymentAccount', {
          cardModel,
        });
        break;

      case CardFeatureType.AutoDebit:
        navigation.navigate('AutomaticDebitPayment', {
          cardModel,
        });
        break;

      default:
        console.log(`Chưa xử lý tính năng: ${feature.id}`);
        break;
    }
  };

  // Tạo mã PIN mới
  const createNewPin = async (cardModel: Card) => {
    const props: CreatePinParams = {
      cardData: cardModel,
    };
    navigation.navigate('CreatePinScreen', props);
  };

  // Lấy thông tin bảo mật
  const getSecretInfo = async (cardModel: Card) => {
    hostSharedModule.d.domainService.addSpinnerRequest();
    const getSecretInfoUseCase = DIContainer.getInstance().getGetCardSecretInfoUseCase();
    const result = await getSecretInfoUseCase.execute({
      cardId: cardModel.id,
    });

    if (isLeft(result)) {
      const appError = result.error;
      await waitForShowingModal(300);
      hostSharedModule.d.domainService.addSpinnerCompleted();

      const errorCode = appError.code;
      if (errorCode === MSBErrorCode.Unauthorized) {
        //Common handles show auth popup automatically
        return;
      }

      showErrorCodePopup({
        errorCode: errorCode,
      });
      return;
    }

    hostSharedModule.d.domainService.addSpinnerCompleted();
    const secretInfo = result.data;
    if (secretInfo?.status === CommonStateStatus.SUCCESS) {
      showCardSecretInfoBottomSheet(secretInfo.data, cardModel);
    }
  };

  // Cập nhật thông tin thẻ sau khi thay đổi trạng thái
  const updateCardDetails = async (cardId: string) => {
    const cardDetailsResult = await DIContainer.getInstance().getGetDetailCardUseCase().execute(cardId);

    if (isRight(cardDetailsResult)) {
      const cardDetails = cardDetailsResult.data;
      const haveDetailsScreen = navigation.getState().routes.some(r => r.name === 'DetailCardScreen');

      if (cardDetails) {
        updateCardInList(cardDetails);
      }

      if (haveDetailsScreen && cardDetails?.id) {
        setCardModel(cardDetails.id, cardDetails);
      }

      return cardDetails;
    }

    return;
  };

  // Thay đổi trạng thái thẻ
  const changeCardStatus = async ({
    cardModel,
    newStatus,
    skipTransactionSigning = false,
    announcementResultsType,
    showLoading = false,
  }: {
    cardModel: Card;
    newStatus: Way4CardStatusCode;
    skipTransactionSigning?: boolean;
    announcementResultsType: EAnnouncementResultsType;
    showLoading?: boolean;
  }) => {
    if (showLoading) {
      hostSharedModule.d.domainService.addSpinnerRequest();
    }
    const changeCardStatusUseCase = DIContainer.getInstance().getChangeCardStatusUseCase();
    const result = await changeCardStatusUseCase.execute({
      cardId: cardModel.id,
      newStatus: newStatus,
      skipTransactionSigning,
    });

    let updatedCard: Card | undefined = {} as Card;
    /**
     * Trường hợp với use-case thay đổi trạng thái của sub-card từ main-card
     * Lúc ấy ta chưa có đủ thông tin của sub-card nên gọi lại luôn để lấy chi tiết
     * Đảm bảo trong result screen luôn hiển thị đúng thông tin của sub-card
     */
    if (cardModel?.id) {
      updatedCard = await updateCardDetails(cardModel.id);
    }

    if (isLeft(result)) {
      hostSharedModule.d.domainService.addSpinnerCompleted();
      const error = result.error;

      if (error.code === MSBErrorCode.Unauthorized) {
        //Common handles show auth popup automatically
        return;
      }

      navigationToResultScreen(
        {...cardModel, ...updatedCard},
        EAnnouncementResultsStatus.fail,
        announcementResultsType,
        {
          errorCode: error.code,
        },
      );
      return;
    }

    const data = result.data;
    if (data?.status === CommonStateStatus.SUCCESS) {
      navigationToResultScreen(
        {...cardModel, ...updatedCard},
        EAnnouncementResultsStatus.success,
        announcementResultsType,
      );
    }

    hostSharedModule.d.domainService.addSpinnerCompleted();
  };

  // Chuyển hướng đến màn hình kết quả
  const navigationToResultScreen = (
    cardModel: Card,
    status: EAnnouncementResultsStatus,
    type: EAnnouncementResultsType,
    extendData?: Partial<Pick<IAnnouncementExtendData, 'errorCode'>>,
  ) => {
    const props: AnnouncementResultsParams = {
      cardData: mapCardToCardDataNoticeScreen(cardModel),
      extendData: {
        date: new Date().toString(),
        type,
        status,
        ...extendData,
      },
    };

    navigation.navigate('AnnouncementResultsScreen', props);
  };

  // Gọi hotline
  const callHotline = () => {
    Linking.openURL(`tel:${HOTLINE}`);
  };

  return {
    navigation,
    handleCardFeatureAction,
    getSecretInfo,
    changeCardStatus,
    navigationToResultScreen,
    setCardModel,
    updateCardDetails,
  };
};

export default useCardViewModel;
