import {ResultState, isLeft} from '@core/ResultState';
import {SelectionItem} from '@entities/base/SelectionItem';
import {useCardOpenFlowActions} from '@store/CardOpenFlow';
import {showErrorCodePopup} from '@utils/CommonHandler';
import {hostSharedModule} from 'msb-host-shared-module';
import {useCallback} from 'react';

export const useAdditionalInformationViewModel = () => {
  const {getCache, setCache} = useCardOpenFlowActions();

  const getMetadata = useCallback(async (cacheKey: string, executeUseCase: Promise<ResultState<SelectionItem[]>>) => {
    const cache = getCache(cacheKey);

    if (cache.length > 0) {
      return cache;
    }

    hostSharedModule.d.domainService?.addSpinnerRequest();

    const result = await executeUseCase;

    hostSharedModule.d.domainService?.addSpinnerCompleted();
    if (isLeft(result)) {
      const errorCode = result.error.code;
      showErrorCodePopup({errorCode});
      return [];
    }

    const data = result.data ?? [];
    setCache(cacheKey, data);
    return data;
  }, []);

  return {getMetadata};
};
