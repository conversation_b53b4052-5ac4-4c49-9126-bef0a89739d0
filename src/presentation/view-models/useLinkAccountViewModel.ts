import {orderBy} from 'lodash';
import {useMemo, useRef, useState} from 'react';

import {CustomError} from '@core/MSBCustomError';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {isLeft} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {Card} from '@entities/card/Card';
import {LinkAccountItem, LinkAccountItemType} from '@entities/daily/ListLinkAccount';
import {translate} from '@locales';
import {createLazyLoadingManager, showPopup} from '@utils';

const DEFAULT_METADATA = {
  totalCount: Number.POSITIVE_INFINITY,
  currentCount: 0,
  processing: false,
};

export const useLinkAccountViewModel = (cardModel: Card) => {
  const [loadingFirst, setLoadingFirst] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [internalCardDetail, setInternalCardDetail] = useState<Card>();

  const [linkItems, setLinkItems] = useState<LinkAccountItem[]>([]);
  const loadingManager = useMemo(() => createLazyLoadingManager(setLoadingFirst, setLoadingMore), []);

  const metadata = useRef<{
    totalCount: number;
    currentCount: number;
    processing: boolean;
  }>({...DEFAULT_METADATA});

  const setErrorWhenFailed = (isLoadingFirst: boolean, error: CustomError) => {
    console.error('[Get list link account] Error:', error);

    if (error.code === MSBErrorCode.Timeout) {
      showPopup({
        iconType: 'WARNING',
        title: translate('errors.messages.temporary_disruption'),
        content: translate('errors.messages.unexpected_error_with_support_info'),
        confirmBtnText: translate('card.confirm'),
        cancelBtnText: translate('card.close'),
        verticalAlign: false,
      });
    }

    setHasError(true);
    loadingManager.hideLoading(isLoadingFirst);
    metadata.current.processing = false;
  };

  const getListLinkAccount = async (forceRefresh = false) => {
    if (forceRefresh) {
      metadata.current = {...DEFAULT_METADATA};
      if (linkItems.length !== 0) {
        setLinkItems([]);
      }
      if (hasError) {
        setHasError(false);
      }
    }

    if (metadata.current.currentCount >= metadata.current.totalCount) {
      return;
    }
    if (metadata.current.processing) {
      return;
    }

    metadata.current.processing = true;

    const isLoadingFirst = metadata.current.totalCount === Number.POSITIVE_INFINITY;
    loadingManager.showLoading(isLoadingFirst);

    if (forceRefresh) {
      const cardDetailsResult = await DIContainer.getInstance().getGetDetailCardUseCase().execute(cardModel.id);

      if (isLeft(cardDetailsResult)) {
        setErrorWhenFailed(true, cardDetailsResult.error);
        return;
      }

      const cardDetails = cardDetailsResult.data;
      setInternalCardDetail(cardDetails);
    }

    const result = await DIContainer.getInstance().getGetListLinkAccountUseCase().execute({
      from: metadata.current.currentCount,
    });

    if (isLeft(result)) {
      const error = result.error;
      setErrorWhenFailed(isLoadingFirst, error);
      return;
    }

    const listLinkAccount = result.data;
    if (!listLinkAccount) {
      loadingManager.hideLoading(isLoadingFirst);
      metadata.current.processing = false;

      setHasError(true);
      return;
    }

    setLinkItems(prev => {
      let newLinkItems = forceRefresh ? [] : [...prev];

      for (const account of listLinkAccount.data) {
        if (account.id !== cardModel.rbsNumber) {
          newLinkItems.push({
            type: LinkAccountItemType.ITEM,
            data: account,
          });
        } else {
          newLinkItems.unshift({
            type: LinkAccountItemType.DEFAULT,
            data: account,
          });
        }
      }
      const hasDefaultAccount = newLinkItems.length > 0 && newLinkItems[0].type === LinkAccountItemType.DEFAULT;

      // If the first item is DEFAULT type, keep it at the top and sort the rest
      if (hasDefaultAccount) {
        const defaultItem = newLinkItems[0];
        const restItems = newLinkItems.slice(1);

        // Sort only the non-default items
        const sortedRestItems = orderBy(
          restItems,
          (item: LinkAccountItem<Omit<LinkAccountItemType, LinkAccountItemType.HEADER_SECTION>>) =>
            item.data.availableBalance,
          ['desc'],
        );

        // Combine the default item with the sorted rest
        newLinkItems = [defaultItem, ...sortedRestItems] as LinkAccountItem[];
      } else {
        // If no default item at the beginning, sort all items
        newLinkItems = orderBy(
          newLinkItems,
          (item: LinkAccountItem<Omit<LinkAccountItemType, LinkAccountItemType.HEADER_SECTION>>) =>
            item.data.availableBalance,
          ['desc'],
        ) as LinkAccountItem[];
      }

      return newLinkItems;
    });

    metadata.current.currentCount += listLinkAccount.data.length;
    metadata.current.totalCount = listLinkAccount.metadata.totalItems;

    setHasError(false);
    loadingManager.hideLoading(isLoadingFirst);
    metadata.current.processing = false;
  };

  return {
    loadingFirst,
    loadingMore,
    linkItems,
    hasError,
    internalCardDetail,
    getListLinkAccount,
  };
};
