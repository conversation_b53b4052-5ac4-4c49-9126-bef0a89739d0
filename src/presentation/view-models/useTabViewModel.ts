import {RootStackParamList} from '@app-navigation/types';
import {SlideTabBarKey} from '@app-screens/list-card/types';
import {isLeft} from '@core/ResultState';
import {DIContainer} from '@di/DIContainer';
import {Card} from '@entities/card/Card';
import {CardFeature} from '@entities/card/CardFeature';
import {CardType} from '@entities/card/CardType';
import {EAnnouncementResultsType} from '@entities/card/EAnnouncementResults';
import {Way4CardStatusCode} from '@entities/card/Way4CardStatusCode';
import {translate} from '@locales';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useListingCardActionSelectors} from '@store/ListCard';
import {hostSharedModule} from 'msb-host-shared-module';
import {useRef} from 'react';
import useCardViewModel from './useCardViewModel';

export const useTabViewModel = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const {handleCardFeatureAction, changeCardStatus} = useCardViewModel();
  const {setTabViewIndex, setTopTabbar, setLoading, setError, setCreditCards, setDebitCards} =
    useListingCardActionSelectors();
  const tabViewIndexPrev = useRef(false);

  const handleTabView = (creditCards_: Card[] = [], debitCards_: Card[] = []) => {
    const creditCardsLength = creditCards_?.length || 0;
    const debitCardsLength = debitCards_?.length || 0;
    const creditTitle = `${translate('list_card.credit')} (${creditCardsLength})`;

    const debitTitle = `${translate('list_card.debit')} (${debitCardsLength})`;
    if (!tabViewIndexPrev.current && (debitCardsLength > 0 || creditCardsLength > 0)) {
      tabViewIndexPrev.current = true;
      setTabViewIndex(creditCardsLength <= 0 && debitCardsLength > 0 ? 1 : 0);
    }

    setTopTabbar([
      {
        key: SlideTabBarKey.Credits,
        title: creditTitle,
        testID: 'cm.tab-view.credits',
        disabled: creditCardsLength <= 0,
      },
      {
        key: SlideTabBarKey.Debits,
        title: debitTitle,
        testID: 'cm.tab-view.debits',
        disabled: debitCardsLength <= 0,
      },
    ]);
  };

  const getCardList = async () => {
    setLoading(true);
    setError(false);

    const result = await DIContainer.getInstance().getGetListCardUseCase().execute();

    if (isLeft(result)) {
      setError(true);
      setDebitCards([]);
      setCreditCards([]);
      setLoading(false);
      return;
    }

    const cards = result.data ?? [];
    const debitCardsRs = cards.filter(item => item.type === CardType.Debit);
    const creditCardsRs = cards.filter(item => item.type === CardType.Credit);

    setDebitCards(debitCardsRs);
    setCreditCards(creditCardsRs);

    handleTabView(creditCardsRs, debitCardsRs);
    setLoading(false);
  };

  const handleActiveCardListener = async (cardModel: Card) => {
    changeCardStatus({
      cardModel,
      newStatus: Way4CardStatusCode.CardOK,
      announcementResultsType: EAnnouncementResultsType.activeCard,
      showLoading: true,
    });
  };

  const handleFeatureAction = (feature?: CardFeature, cardModel?: Card) => {
    if (feature && cardModel) {
      handleCardFeatureAction(feature, cardModel);
    } else {
      hostSharedModule.d.domainService.undevelopedFeature();
    }
  };

  const navigationToDetailCard = (cardModel: Card) => {
    navigation.navigate(
      'DetailCardScreen',
      {id: cardModel.id},
      {
        pop: true,
      },
    );
  };

  return {
    getCardList,
    handleActiveCardListener,
    handleFeatureAction,
    navigationToDetailCard,
  };
};
