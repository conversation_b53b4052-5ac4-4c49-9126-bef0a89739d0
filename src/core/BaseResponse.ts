import {CustomError} from './MSBCustomError';

export interface MSBStatus {
  code?: string;
  message?: string;
  title?: string;
}

export interface MSBError {
  message?: string;
  key?: string;
  context?: MSBLocalization;
}

export interface MSBLocalization {
  vi?: string;
  en?: string;
}

export type BaseResponse<T = unknown, TStatus = boolean> = {
  status: number;
} & (TStatus extends false
  ? {
      success: false;
      error: CustomError;
    }
  : {
      data?: T;
      success: true;
    });

export const isValidResponse = (response: BaseResponse<any>): response is BaseResponse<any, true> => {
  if (!response.success) {
    return false;
  }

  return true;
};
