import {CustomError} from './MSBCustomError';

export type Right<T> = {status: 'SUCCESS'; data: T | undefined};
export type Left = {status: 'ERROR'; error: CustomError};

export type ResultState<T> = Left | Right<T>;

export type UnwrapResultState = <T>(result: ResultState<T>) => NonNullable<T | CustomError>;

export function isLeft<T>(result: ResultState<T>): result is Left {
  return result.status === 'ERROR';
}

export function isRight<T>(result: ResultState<T>): result is Right<T> {
  return result.status === 'SUCCESS';
}

export const makeLeft = (error: CustomError): Left => ({
  status: 'ERROR',
  error,
});

export const makeRight = <T>(data: T | undefined): Right<T> => ({
  status: 'SUCCESS',
  data,
});
