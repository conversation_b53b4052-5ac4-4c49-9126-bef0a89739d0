import {ICardService} from '@data/datasources/ICardService';
import {ICommonService} from '@data/datasources/ICommonService';
import {IDailyService} from '@data/datasources/IDailyService';
import {ITransactionService} from '@data/datasources/ITransactionService';
import {CardService} from '@data/datasources/remote/CardService';
import {CommonService} from '@data/datasources/remote/CommonService';
import {DailyService} from '@data/datasources/remote/DailyService';
import {TransactionService} from '@data/datasources/remote/TransactionService';
import {CardRepository} from '@data/repositories/CardRepository';
import {CommonRepository} from '@data/repositories/CommonRepository';
import {DailyRepository} from '@data/repositories/DailyRepository';
import {ICardRepository} from '@domain/repositories/ICardRepository';
import {ICommonRepository} from '@domain/repositories/ICommonRepository';
import {IDailyRepository} from '@domain/repositories/IDailyRepository';
import {ChangeCardStatusUseCase} from '@domain/use-cases/card/ChangeCardStatusUseCase';
import {CreateNewPinUseCase} from '@domain/use-cases/card/CreateNewPinUseCase';
import {GetCardSecretInfoUseCase} from '@domain/use-cases/card/GetCardSecretInfoUseCase';
import {GetDetailCardUseCase} from '@domain/use-cases/card/GetDetailCardUseCase';
import {GetDetailHistoryUseCase} from '@domain/use-cases/card/GetDetailHistoryUseCase';
import {GetListCardUseCase} from '@domain/use-cases/card/GetListCardUseCase';
import {GetListHistoryUseCase} from '@domain/use-cases/card/GetListHistoryUseCase';
import {GetListLinkAccountUseCase} from '@domain/use-cases/daily/GetListLinkAccountUseCase';
import {AutoDebitPaymentUseCase} from '@use-cases/card/AutoDebitPaymentUseCase';
import {GetAllowedCardProductsUseCase} from '@use-cases/card/GetAllowedCardProductsUseCase';
import {GetFilterTransactionHistoryUseCase} from '@use-cases/card/GetFilterTransactionHistoryUseCase';
import {InitializeCardOpeningFlowUseCase} from '@use-cases/card/InitializeCardOpeningFlowUseCase';
import {UpdateDebitAccountPaymentUseCase} from '@use-cases/card/UpdateDebitAccountPaymentUseCase';
import {GetBranchesUseCase} from '@use-cases/common/GetBranchesUseCase';
import {GetDistrictsUseCase} from '@use-cases/common/GetDistrictsUseCase';
import {GetProvincesUseCase} from '@use-cases/common/GetProvincesUseCase';
import {GetReferralInformationUseCase} from '@use-cases/common/GetReferralInformationUseCase';
import {GetWardsUseCase} from '@use-cases/common/GetWardsUseCase';
import {hostSharedModule, IHttpClient} from 'msb-host-shared-module';

export class DIContainer {
  private static instance: DIContainer;

  private readonly httpClient: IHttpClient;
  /**
   * Warning!: Không xoá cờ này
   */
  private readonly flagToInjectFields = false;

  private commonService!: ICommonService;
  private commonRepository!: ICommonRepository;

  private dailyService!: IDailyService;
  private dailyRepository!: IDailyRepository;

  private cardService!: ICardService;
  private cardRepository!: ICardRepository;

  private transactionService!: ITransactionService;

  private constructor() {
    this.httpClient = hostSharedModule.d.httpClient!;
  }

  public static getInstance(): DIContainer {
    if (!DIContainer.instance) {
      DIContainer.instance = new DIContainer();
    }
    return DIContainer.instance;
  }

  //DATA SOURCES

  public getCommonService(): ICommonService {
    if (!this.commonService) {
      this.commonService = new CommonService(this.httpClient);
    }
    return this.commonService;
  }

  public getDailyService(): IDailyService {
    if (!this.dailyService) {
      this.dailyService = new DailyService(this.httpClient);
    }
    return this.dailyService;
  }

  public getCardService(): ICardService {
    if (!this.cardService) {
      this.cardService = new CardService(this.httpClient);
    }
    return this.cardService;
  }

  public getTransactionService(): ITransactionService {
    if (!this.transactionService) {
      this.transactionService = new TransactionService(this.httpClient);
    }
    return this.transactionService;
  }

  // REPORITORIES

  public getCommonRepository(): ICommonRepository {
    if (!this.commonRepository) {
      this.commonRepository = new CommonRepository(this.getCommonService());
    }
    return this.commonRepository;
  }

  public getDailyRepository(): IDailyRepository {
    if (!this.dailyRepository) {
      this.dailyRepository = new DailyRepository(this.getDailyService());
    }
    return this.dailyRepository;
  }

  public getCardRepository(): ICardRepository {
    if (!this.cardRepository) {
      this.cardRepository = new CardRepository(this.getCardService(), this.getTransactionService());
    }
    return this.cardRepository;
  }

  // USE CASES

  public getGetAllowedCardProductsUseCase(): GetAllowedCardProductsUseCase {
    return new GetAllowedCardProductsUseCase(this.getCardRepository());
  }

  public getInitializeCardOpeningFlowUseCase(): InitializeCardOpeningFlowUseCase {
    return new InitializeCardOpeningFlowUseCase(this.getCardRepository());
  }

  public getGetFilterTransactionHistoryUseCase(): GetFilterTransactionHistoryUseCase {
    return new GetFilterTransactionHistoryUseCase(this.getCardRepository());
  }

  public getGetReferralInformationUseCase(): GetReferralInformationUseCase {
    return new GetReferralInformationUseCase(this.getCardRepository());
  }

  public getGetBranchesUseCase(): GetBranchesUseCase {
    return new GetBranchesUseCase(this.getCommonRepository());
  }

  public getGetWardsUseCase(): GetWardsUseCase {
    return new GetWardsUseCase(this.getCommonRepository());
  }

  public getGetDistrictsUseCase(): GetDistrictsUseCase {
    return new GetDistrictsUseCase(this.getCommonRepository());
  }

  public getGetProvincesUseCase(): GetProvincesUseCase {
    return new GetProvincesUseCase(this.getCommonRepository());
  }

  public getAutoDebitPaymentUseCase(): AutoDebitPaymentUseCase {
    return new AutoDebitPaymentUseCase(this.getCardRepository());
  }

  public getUpdateDebitAccountPaymentUseCase(): UpdateDebitAccountPaymentUseCase {
    return new UpdateDebitAccountPaymentUseCase(this.getCardRepository());
  }

  public getGetListHistoryUseCase(): GetListHistoryUseCase {
    return new GetListHistoryUseCase(this.getCardRepository());
  }

  public getGetDetailHistoryUseCase(): GetDetailHistoryUseCase {
    return new GetDetailHistoryUseCase(this.getCardRepository());
  }

  public getCreatePinUseCase(): CreateNewPinUseCase {
    return new CreateNewPinUseCase(this.getCardRepository());
  }

  public getGetListLinkAccountUseCase(): GetListLinkAccountUseCase {
    return new GetListLinkAccountUseCase(this.getDailyRepository());
  }

  public getGetCardSecretInfoUseCase(): GetCardSecretInfoUseCase {
    return new GetCardSecretInfoUseCase(this.getCardRepository());
  }

  public getChangeCardStatusUseCase(): ChangeCardStatusUseCase {
    return new ChangeCardStatusUseCase(this.getCardRepository());
  }

  public getGetDetailCardUseCase(): GetDetailCardUseCase {
    return new GetDetailCardUseCase(this.getCardRepository());
  }

  public getGetListCardUseCase(): GetListCardUseCase {
    return new GetListCardUseCase(this.getCardRepository());
  }
}
