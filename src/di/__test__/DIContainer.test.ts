import {DIContainer} from '../DIContainer';

describe('DIContainer', () => {
  beforeEach(() => {
    // @ts-ignore
    DIContainer.instance = undefined;
  });

  it('should return the same instance (singleton)', () => {
    const instance1 = DIContainer.getInstance();
    const instance2 = DIContainer.getInstance();
    expect(instance1).toBe(instance2);
  });

  it('should initialize DailyRepository', () => {
    const container = DIContainer.getInstance();
    const repo = container.getDailyRepository();
    expect(repo).toBeDefined();
  });

  it('should initialize CardRepository', () => {
    const container = DIContainer.getInstance();
    const repo = container.getCardRepository();
    expect(repo).toBeDefined();
  });

  it('should initialize CommonRepository', () => {
    const container = DIContainer.getInstance();
    const repo = container.getCommonRepository();
    expect(repo).toBeDefined();
  });

  it('should initialize all use cases', () => {
    const container = DIContainer.getInstance();
    expect(container.getAutoDebitPaymentUseCase()).toBeDefined();
    expect(container.getUpdateDebitAccountPaymentUseCase()).toBeDefined();
    expect(container.getGetListHistoryUseCase()).toBeDefined();
    expect(container.getGetDetailHistoryUseCase()).toBeDefined();
    expect(container.getCreatePinUseCase()).toBeDefined();
    expect(container.getGetListLinkAccountUseCase()).toBeDefined();
    expect(container.getGetCardSecretInfoUseCase()).toBeDefined();
    expect(container.getChangeCardStatusUseCase()).toBeDefined();
    expect(container.getGetDetailCardUseCase()).toBeDefined();
    expect(container.getGetListCardUseCase()).toBeDefined();
    expect(container.getGetProvincesUseCase()).toBeDefined();
    expect(container.getGetDistrictsUseCase()).toBeDefined();
    expect(container.getGetWardsUseCase()).toBeDefined();
    expect(container.getGetReferralInformationUseCase()).toBeDefined();
    expect(container.getDailyService()).toBeDefined();
    expect(container.getCardService()).toBeDefined();
    expect(container.getCommonService()).toBeDefined();
    expect(container.getTransactionService()).toBeDefined();
    expect(container.getDailyRepository()).toBeDefined();
    expect(container.getCardRepository()).toBeDefined();
    expect(container.getCommonRepository()).toBeDefined();
  });
});
