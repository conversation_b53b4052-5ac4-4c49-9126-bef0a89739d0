import {jest} from '@jest/globals';
/**
 * To fix: @link https://stackoverflow.com/questions/73432367/skip-import-react-from-react-from-test-file-using-jest
 */
import React from 'react';

global.React = React;

/**
 * react-native-gesture-handler
 */
import 'react-native-gesture-handler/jestSetup';

/**
 * react-native-unistyles
 */
import 'mocks/unistyles';

jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');

/**
 * Configure testing-library
 */
import {configure as configureReactNative} from '@testing-library/react-native';
configureReactNative({
  asyncUtilTimeout: 60000,
  defaultIncludeHiddenElements: false,
  concurrentRoot: true,
});

/**
 * Configure react-native-reanimated
 */
import {setUpTests} from 'react-native-reanimated';
setUpTests();

jest.mock('react-native-reanimated', () => {
  const originalModule: any = jest.requireActual('react-native-reanimated');

  return {
    __esModule: true,
    ...originalModule,
    /**
     * Mock for custom scrollable view from react-native-reanimated
     * Ex: Animated.FlatList
     */
    useAnimatedScrollHandler: jest.fn(),
  };
});

/**
 * Mock @gorhom/bottom-sheet
 */
jest.mock('@gorhom/bottom-sheet', () => ({
  __esModule: true,
  ...require('@gorhom/bottom-sheet/mock'),
}));

/**
 * Mock react-native-safe-area-context
 */
jest.mock('react-native-safe-area-context', () => {
  const inset = {top: 0, right: 0, bottom: 0, left: 0};
  const originalModule: any = jest.requireActual('react-native-safe-area-context');
  return {
    __esModule: true,
    ...originalModule,
    SafeAreaProvider: jest.fn(({children}) => children),
    SafeAreaConsumer: jest.fn(({children}) => children(inset)),
    useSafeAreaInsets: jest.fn(() => inset),
    useSafeAreaFrame: jest.fn(() => ({x: 0, y: 0, width: 390, height: 844})),
  };
});

/**
 * Mock react-native-async-storage
 */
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

/**
 * Mock clipboard
 */
// @ts-ignore
import mockClipboard from '@react-native-clipboard/clipboard/jest/clipboard-mock';

jest.mock('@react-native-clipboard/clipboard', () => mockClipboard);

jest.mock('react-native-linear-gradient', () => 'LinearGradient');
/**
 * Mock http requests
 */
import {server} from 'mocks/msw-node';

// Enable API mocking via Mock Service Worker (MSW)
beforeAll(() => {
  console.log('[MSW Server listen]');
  server.listen();
  jest.useFakeTimers();
  jest.spyOn(global, 'setTimeout');
});

// Reset any runtime request handlers we may add during the tests
afterEach(() => {
  console.log('[MSW Server resetHandlers]');
  server.resetHandlers();
});

// Disable API mocking after the tests are done
afterAll(() => {
  console.log('[MSW Server close]');
  server.close();
  jest.useRealTimers();
});

/**
 * !Important: Need to mock from setup file for jest instead from mockup module from <rootDir>/__mocks__/*
 */
jest.mock('zustand', () => {
  return jest.requireActual('mocks/zustand');
});

jest.mock('react-native-webview', () => {
  const {View} = require('react-native');
  return {
    __esModule: true,
    default: View,
    WebView: View,
  };
});

/**
 * Repack
 */
jest.mock('@callstack/repack/client', () => ({
  Federated: jest.fn(),
}));

/**
 * Mock react-native-pdf
 */
jest.mock('react-native-pdf', () => {
  const {View} = require('react-native');
  return {
    __esModule: true,
    default: View,
    Pdf: View,
  };
});

jest.mock('react-native-blob-util', () => {
  return {
    __esModule: true,
    default: {
      DocumentDir: jest.fn(),
      config: jest.fn(() => ({
        fetch: jest.fn(() => ({
          progress: jest.fn<() => Promise<boolean>>().mockResolvedValue(true),
        })),
      })),
      fs: {
        cp: jest.fn<() => Promise<boolean>>().mockResolvedValue(true),
        dirs: {
          CacheDir: '/mockCacheDir',
        },
        unlink: jest.fn(),
      },
    },
  };
});
