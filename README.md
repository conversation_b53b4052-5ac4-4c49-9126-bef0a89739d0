# Card Module(Remote app)

## Cấu trúc dự án

```bash
src
|-- assets
|   `-- images
|-- constants
|-- core
|-- data
|   |-- datasources
|   |-- mappers
|   |-- models
|   `-- repositories
|-- di
|-- domain
|   |-- base
|   |-- entities
|   |-- repositories
|   |-- states
|   `-- use-cases
|-- locales
|-- presentation
|   |-- components
|   |-- navigation
|   |-- screens
|   |-- store
|   `-- view-models
`-- utils
```

## Quản lý phiên bản các dependency của remote app

- Khi có yêu cầu nâng cấp hay `thêm/sửa/xoá` phiên bản của dependency để sử dụng cần yêu cầu để cập nhật các metadata ở [`msb-digibank-sdk`](https://gitlab-dso.msb.com.vn/digital-banking-platform/ibretail/mobile/super-app/library/msb-digibank-sdk)
- Sau khi `msb-digibank-sdk` cập nhật các metadata mới cho các dependency, hãy cập nhật phiên bản của nó

  ```sh
  yarn msb-digibank-sdk@new-version -D
  ```

- Để kiểm tra các phiên bản hiện tại đang sử dụng có đúng với yêu cầu hay không.

  ```sh
  yarn check-deps
  ```

  Nếu có bất kì nào không phù hợp, sẽ nhận được thông báo cần chỉnh sửa version cho phù hợp. VD

  ```
  error package.json: Changes are needed to satisfy all capabilities.
          In dependencies:
          - i18n-js "3.9.3" should be "3.9.2"
  error Re-run with '--write' to fix them.
  ```

- Nếu muốn áp dụng các thay đổi được đề xuất từ `check-deps` thay vì phải sửa thủ công, hãy sử dụng

  ```sh
  yarn align-deps
  ```

## Code generator với Clean Architecture

!!! Lưu ý là script chỉ chạy phù hợp với module này và được tích hợp sẵn module resolver

```sh
yarn gen:clean --domain=<DomainName> --useCase=<UseCaseName> --dataModel=<DataModelName> --errorMapper=<ErrorMapperName> [--param]
```

Trong đó các tham số:

- `--domain`(bắt buộc):
  - Tên mà domain sẽ làm việc, VD: `Card/Payment/Daily`. Các DTO và entities sẽ được gen vào folder dựa trên domain này
- `--useCase`(bắt buộc):
  - Tên của use case để chạy script. VD: `GetCardSecretInfo` -> tên use case sau khi được chạy sẽ là `GetCardSecretInfoUseCase`
- `--dataModel`(không bắt buộc):
  - Tên của data model b sẽ làm việc ở cả domain(Entity) và data layer(DTO). VD: `-useCase=CardSecretInfo` thì sau khi chạy sẽ có entity `CardSecretInfo` và DTO là `CardSecretInfoDTO`, hàm mapper sẽ là `mapToCardSecretInfo`
  - Nếu tham số này không được khai báo thì sẽ không có DTO và entity và hàm mapper sẽ không được tạo ra. Nếu không khai báo thì sẽ không có tham số đầu vào cho use case và service, chỉ có tham số đầu vào là `input` và `request` cho use case và service sẽ nhận được từ data layer. Mặc định type cho các tham số này sẽ là `any`. Mục đích cho trường hợp này là có một số use case chỉ quan tâm việc thực thi có thành công hay không, không quan tâm đến dữ liệu trả về.
- `--errorMapper`(không bắt buộc):
  - Mục đích: Mỗi use case sẽ có thể có error khác nhau. Khi đó sẽ cần tạo ra một error mapper riêng để xử lý error của use case ở tầng service
  - Tham số truyền vào là tên của error mapper sẽ được tạo ra. VD: `-errorMapper=CardSecretInfo` thì sau khi chạy sẽ có error mapper `CardSecretInfoErrorMapper` và được inject vào `handleData` trong service để xử lý error của use case
- `--param`(không bắt buộc):
  - Nếu có tham số này thì sẽ tạo `Input` cho use case và `Request` cho service. VD: Nếu use case `GetCardSecretInfo` khai báo là có param thông qua command thì use case sẽ có tham số đầu vào ở `execute(input: GetCardSecretInfoInput)` và service sẽ có request với tham số đầu vào là `getCardSecretInfo(request: GetCardSecretInfoRequest)`

## Lỗi và cách sửa lỗi

1. `Cannot read property ‘createTranslationOptions’ of undefined`

   Nguyên nhân: Do import trực tiếp hàm `translate()` thẳng từ module `i18n`
   Giải pháp: Sử dụng hàm đã được định nghĩa ở trong folder `locales` của project

   ```jsx
   import {translate} from '@locales';
   ```
