FROM node:22.14.0

RUN corepack enable
RUN corepack prepare yarn@4.3.1 --activate

WORKDIR /app

# Copy dự án hiện tại vào docker để thực hiện build local
# Để kiểm tra xem có lỗi build lúc build time không
COPY . .

RUN apt-get update && apt-get install -y git

# # Clone repo từ gitlab với commit ID cụ thể
# RUN git clone https://hungtv11:<EMAIL>/digital-banking-platform/ibretail/mobile/super-app/card-module.git . && \
#     git checkout 2713f8a0d88a7afe6297d160f3d9e680f3a4d060

# RUN rm -rf yarn.lock

RUN yarn install --immutable

RUN yarn bundle:ios:sit --verbose

# Throw ra lỗi để stop docker build
RUN command_that_does_not_exist