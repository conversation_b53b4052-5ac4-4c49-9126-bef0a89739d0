import type {FederationRuntimePlugin} from '@module-federation/enhanced/runtime';

/**
 * Runtime plugin cho Federation để handle khi remote module có thể bị tải fail(cdn down)
 * <PERSON><PERSON> dụng hook remoteFallbackPlugin để trả về một fallback component đ<PERSON> tr<PERSON>h throw {@link FederatedRuntimeError}
 * <PERSON>à<PERSON> ứng dụng bị crash
 * @returns {@link ScreenLoadFailMicroApp}
 */
const remoteFallbackPlugin: () => FederationRuntimePlugin = () => ({
  name: 'remote-fallback-plugin',
  async errorLoadRemote({id, error, from, origin}) {
    console.log('[remote-fallback-plugin]', id, error, from, origin);
    const {ScreenLoadFailMicroApp} = await import('./src/utils/LoadFailMicroApp');

    return () => ({
      __esModule: true,
      default: ScreenLoadFailMicroApp,
    });
  },
});
export default remoteFallbackPlugin;
