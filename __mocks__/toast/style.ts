import {createMSBStyleSheet} from 'msb-shared-component';

export const makeStyles = createMSBStyleSheet(({ColorToast, SizeToast, SizeGlobal}) => {
  return {
    error: {
      backgroundColor: ColorToast.SurfaceError,
    },
    success: {
      backgroundColor: ColorToast.SurfaceSuccess,
    },
    text: {
      color: ColorToast.TextDefault,
      width: '90%',
    },
    toastContainer: {
      alignItems: 'center',
      borderRadius: SizeToast.BorderRadius,
      bottom: 50,
      flexDirection: 'row',
      gap: SizeToast.SpacingGap,
      maxHeight: SizeGlobal.Size2400,
      left: SizeGlobal.Size400,
      paddingHorizontal: SizeToast.SpacingHorizontal,
      paddingVertical: SizeToast.SpacingVertical,
      position: 'absolute',
      right: SizeGlobal.Size400,
    },
    txtContainer: {
      flex: 1,
    },
  };
});
