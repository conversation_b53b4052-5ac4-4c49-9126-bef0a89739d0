import {createMSBStyleSheet} from 'msb-shared-component';

export const styleSheet = createMSBStyleSheet(({ColorPopup, SizeGlobal, SizePopup, Typography}) => ({
  container: {
    backgroundColor: ColorPopup.SurfaceDefault,
    borderRadius: SizePopup.BorderRadius,
    padding: SizeGlobal.Size400,
  },
  contentWrap: {
    gap: SizeGlobal.Size400,
    paddingTop: SizeGlobal.Size100,
    paddingBottom: SizeGlobal.Size200,
  },
  contentContainer: {},
  errorContainer: {},
  iconContainer: {},
  txtContent: {
    color: ColorPopup.TextContent,
    ...Typography?.base_regular,
  },
  txtErrorCode: {
    color: ColorPopup.TextErrorCode,
    ...Typography?.base_regular,
  },
  txtTitle: {
    color: ColorPopup.TextTitle,
  },
  btn: {
    marginTop: SizeGlobal.Size400,
  },
}));
