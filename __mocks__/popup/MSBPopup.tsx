import {PopupActions, PopupProps} from 'msb-host-shared-module/dist/types/PopupType';
import {MSBGroupButton, MSBTextBase} from 'msb-shared-component';
import React, {RefAttributes, useEffect, useImperativeHandle, useState} from 'react';
import {View} from 'react-native';
import Modal, {ReactNativeModal} from 'react-native-modal';

import {useMSBStyles} from 'msb-shared-component';
import {styleSheet} from './style.ts';

export const MSB_POPUP_CONFIRM_TEST_ID = 'msb.popup.confirm';
export const MSB_POPUP_CANCEL_TEST_ID = 'msb.popup.close';

const MSBPopup = ({ref}: RefAttributes<PopupActions>) => {
  const {styles, theme} = useMSBStyles(styleSheet);
  const modalRef = React.useRef<ReactNativeModal>(null);
  const [data, setData] = useState<PopupProps & {onConfirmAfterAnimation?: () => void}>();
  const [defaultContent] = useState(<></>);

  const show = (dataShow?: PopupProps) => {
    setData(dataShow as PopupProps);
  };

  const hide = () => {
    modalRef.current?.close();
    setData(undefined);
  };

  useEffect(() => {
    if (data) {
      modalRef.current?.open();
    }
  }, [data]);

  useImperativeHandle(ref, () => ({
    show,
    hide,
  }));

  const _onClose = () => {
    data?.onCancel?.();
    if (!data?.isKeepPopupWhenCancel) {
      hide();
    }
  };

  const _onConfirm = () => {
    data?.onConfirm?.();
    if (!data?.isKeepPopupWhenConfirm) {
      modalRef.current?.close();
      data?.onConfirmAfterAnimation?.();
      setData(undefined);
    }
  };

  const renderIcon = () => {
    return <View style={styles.iconContainer} />;
  };

  const renderTitle = () => {
    return <MSBTextBase content={data?.title} type={theme.Typography?.title_bold} style={styles.txtTitle} />;
  };

  const renderContent = () => {
    if (data?.content || data?.contentCustom) {
      return (
        <View style={styles.contentContainer}>
          {data?.content ? <MSBTextBase content={data?.content} style={styles.txtContent} /> : data?.contentCustom}
        </View>
      );
    }

    return defaultContent;
  };

  const renderErrorContent = () => {
    return (
      data?.errorCode && (
        <View style={styles.errorContainer}>
          <MSBTextBase
            type={theme.Typography?.base_regular}
            style={styles.txtErrorCode}
            content={`${data?.errorCode}`}
          />
        </View>
      )
    );
  };

  if (!data) {
    return null;
  }

  return (
    <Modal
      ref={modalRef}
      testID="msb.popup"
      backdropColor={theme.ColorGlobal.Overlay60}
      backdropOpacity={0.6}
      animationIn="slideInUp"
      useNativeDriver={true}
      onBackdropPress={() => {
        if (data?.isBackdropPress) {
          hide();
        }
      }}
      avoidKeyboard={true}
      hideModalContentWhileAnimating>
      <View style={styles.container}>
        <View style={styles.contentWrap}>
          {renderIcon()}
          {renderTitle()}
          {renderContent()}
          {renderErrorContent()}
        </View>
        <MSBGroupButton
          testIDClose={MSB_POPUP_CANCEL_TEST_ID}
          testIDConfirm={MSB_POPUP_CONFIRM_TEST_ID}
          confirmBtnText={data?.confirmBtnText}
          cancelBtnText={data?.cancelBtnText}
          verticalAlign={data?.verticalAlign}
          onConfirm={_onConfirm}
          onClose={_onClose}
          style={styles.btn}
        />
      </View>
    </Modal>
  );
};

export default MSBPopup;
