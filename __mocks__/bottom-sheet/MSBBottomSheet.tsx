import BottomSheet, {BottomSheetBackdropProps, useBottomSheet} from '@gorhom/bottom-sheet';
import {BottomSheetActions, BottomSheetProps} from 'msb-host-shared-module';
import {MSBGroupButton, MSBIcon, MSBIcons, MSBIconSize, MSBTextBase, useMSBStyles} from 'msb-shared-component';
import React, {
  memo,
  RefAttributes,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {BackHandler, Dimensions, Keyboard, Platform, View} from 'react-native';
import {Gesture, GestureDetector} from 'react-native-gesture-handler';
import Animated, {
  Extrapolation,
  interpolate,
  runOnJS,
  useAnimatedReaction,
  useSharedValue,
} from 'react-native-reanimated';
import {styleSheet} from './style.ts';

export const BOTTOM_SHEET_CONTAINER_TEST_ID = 'cm.bottom-sheet.container';
export const BOTTOM_SHEET_FOOTER_CLOSE_BTN_TEST_ID = 'cm.bottom-sheet.footer.close-btn';
export const BOTTOM_SHEET_FOOTER_CONFIRM_BTN_TEST_ID = 'cm.bottom-sheet.footer.confirm-btn';
export const BOTTOM_SHEET_HANDLE_TEST_ID = 'cm.bottom-sheet.handle';
export const BOTTOM_SHEET_HANDLE_TITLE_TEST_ID = 'cm.bottom-sheet.handle.title';
export const BOTTOM_SHEET_HANDLE_CLOSE_BTN_TEST_ID = 'cm.bottom-sheet.close-btn';

function AppBottomSheetBackdrop({animatedIndex, style}: Readonly<BottomSheetBackdropProps>) {
  const {theme} = useMSBStyles();

  const sheet = useBottomSheet();
  const opacity = useSharedValue(0);

  useAnimatedReaction(
    () => animatedIndex.value,
    (current, previous) => {
      if (current === -1 && previous === 0) {
        return;
      }
      opacity.value = interpolate(animatedIndex.value, [-1, 0], [0, 0.6], Extrapolation.CLAMP);
    },
  );

  const tapHandler = useMemo(() => {
    const gesture = Gesture.Tap().onEnd(() => {
      runOnJS(sheet.close)();
    });
    return gesture;
  }, []);

  return (
    <GestureDetector gesture={tapHandler}>
      <Animated.View
        style={[
          style,
          {
            backgroundColor: theme.ColorGlobal.Overlay60,
            opacity,
          },
        ]}
      />
    </GestureDetector>
  );
}

const MSBBottomSheet = ({ref}: RefAttributes<BottomSheetActions>) => {
  const {theme, styles} = useMSBStyles(styleSheet);
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [data, setData] = useState<BottomSheetProps>({});
  const {height: screenHeight} = Dimensions.get('screen');
  const maxHeight = screenHeight * 0.85;
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  const handleBackPress = useCallback(() => {
    if (data.children) {
      bottomSheetRef.current?.close();
      return true;
    }
    return false;
  }, [data]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, [handleBackPress]);

  useEffect(() => {
    if (bottomSheetRef.current && data?.children) {
      bottomSheetRef.current?.expand();
    }
  }, [data?.children]);

  useEffect(() => {
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event: any) => {
        setKeyboardHeight(event.endCoordinates.height);
      },
    );

    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      },
    );

    return () => {
      keyboardWillShowListener.remove();
      keyboardWillHideListener.remove();
    };
  }, []);

  const show = useCallback((sheetData?: BottomSheetProps) => {
    Keyboard.dismiss();
    setData(sheetData as BottomSheetProps);
  }, []);

  const hide = () => {
    Keyboard.dismiss();
    bottomSheetRef.current?.close();
    handleSheetClose();
  };

  useImperativeHandle(ref, () => ({
    show,
    hide,
  }));

  const _onClose = () => {
    hide();
    data?.onCancel?.();
  };

  const _onConfirm = () => {
    hide();
    data?.onConfirm?.();
  };

  const handleSheetClose = useCallback(() => {
    setData({} as BottomSheetProps);
    data?.onClose?.();
  }, [data]);

  const renderFooter = useCallback(
    () => (
      <View>
        {(data?.confirmBtnText || data?.cancelBtnText) && keyboardHeight === 0 && (
          <View
            style={[
              !data?.verticalAlign && {
                paddingHorizontal: theme.SizeGlobal.Size600,
                paddingBottom: theme.SizeGlobal.Size600,
              },
            ]}>
            <MSBGroupButton
              testIDConfirm={BOTTOM_SHEET_FOOTER_CONFIRM_BTN_TEST_ID}
              testIDClose={BOTTOM_SHEET_FOOTER_CLOSE_BTN_TEST_ID}
              confirmBtnText={data?.confirmBtnText}
              cancelBtnText={data?.cancelBtnText}
              verticalAlign={data?.verticalAlign}
              style={{backgroundColor: theme.ColorGlobal.NeutralWhite}}
              onConfirm={_onConfirm}
              onClose={_onClose}
            />
          </View>
        )}
      </View>
    ),
    [data, keyboardHeight, theme],
  );

  const renderHandle = useCallback(() => {
    return (
      <View testID={BOTTOM_SHEET_HANDLE_TEST_ID} style={styles.headerContainer}>
        <MSBTextBase
          testID={BOTTOM_SHEET_HANDLE_TITLE_TEST_ID}
          type={theme.Typography?.title_semiBold}
          numberOfLines={1}
          style={styles.titleHeader}>
          {data?.header}
        </MSBTextBase>

        <MSBIcon
          testID={BOTTOM_SHEET_HANDLE_CLOSE_BTN_TEST_ID}
          icon={MSBIcons.IconDeleteCancelClose}
          iconSize={MSBIconSize.SIZE_24}
          onIconClick={() => {
            bottomSheetRef.current?.close();
          }}
        />
      </View>
    );
  }, [data]);

  const renderBackdrop = useCallback((props: BottomSheetBackdropProps) => <AppBottomSheetBackdrop {...props} />, []);
  const effectiveMaxHeight = keyboardHeight > 0 ? Math.min(maxHeight, maxHeight - keyboardHeight) : maxHeight;

  if (!data?.children) {
    return null;
  }

  return (
    <BottomSheet
      accessible={false}
      ref={bottomSheetRef}
      enablePanDownToClose
      handleComponent={renderHandle}
      maxDynamicContentSize={effectiveMaxHeight}
      topInset={Platform.OS === 'ios' ? screenHeight * 0.15 : 0}
      keyboardBehavior={'interactive'}
      android_keyboardInputMode={'adjustResize'}
      keyboardBlurBehavior="restore"
      enableBlurKeyboardOnGesture
      onClose={handleSheetClose}
      enableContentPanningGesture={data?.enableContentPanningGesture}
      enableDynamicSizing={true}
      backdropComponent={renderBackdrop}
      footerComponent={renderFooter}>
      <View testID={BOTTOM_SHEET_CONTAINER_TEST_ID}>
        <View>{renderHandle()}</View>
        <View>{data?.children}</View>
        <View>{renderFooter()}</View>
      </View>
    </BottomSheet>
  );
};

export default memo(MSBBottomSheet);
