import {MSBErrorCode} from '@core/MSBErrorCode';
import {ResponseErrors} from '@core/ResponseErrors';
import {CARDS_API, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';

export const mockResponseForCheckTransactionStatus = () => {
  server.use(
    http.get(`${CARDS_API}/tnx/requests/:id`, () => {
      return HttpResponse.json(mockTransactionStatusResponse, {status: 200});
    }),
  );
};

export const mockResponseNotHaveSecretKeyForCheckTransactionStatus = () => {
  server.use(
    http.get(`${CARDS_API}/tnx/requests/:id`, () => {
      return HttpResponse.json({...mockTransactionStatusResponse, secretKey: null}, {status: 200});
    }),
  );
};

export const mockRDBForCheckTransactionStatus = () => {
  server.use(
    http.get(`${CARDS_API}/tnx/requests/:id`, () => {
      const error: ResponseErrors = {
        message: 'Error',
        errors: [
          {
            key: MSBErrorCode['RDB.CA.0018'],
            message: 'Error from RDB',
            context: ['second context'],
          },
        ],
      };
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockTransactionStatusResponse = {
  confirmationId: '1f14ca2b-308d-4dc3-8665-a3e10a253a7a',
  confirmationStatus: 'CONFIRMED',
  transactionStatus: 'SUCCESS',
  secretKey: 'c86b26225ff240d7171144a437dbb853b52ea86fbea86291e88f2e67a548def4',
};
