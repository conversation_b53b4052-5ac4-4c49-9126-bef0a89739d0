import {CARD_JOURNEY_API, createInternalServerError, http, HttpResponse, server} from 'mocks/msw-node';

export const mockResponseForInitializeCardOpeningFlow = () => {
  server.use(
    http.post(`${CARD_JOURNEY_API}/card-issuance`, () => {
      return HttpResponse.json({id: '123', isDropOff: true}, {status: 200});
    }),
  );
};

export const mockResponseForInitializeCardOpeningFlowNotDropOff = () => {
  server.use(
    http.post(`${CARD_JOURNEY_API}/card-issuance`, () => {
      return HttpResponse.json({id: '123', isDropOff: false}, {status: 200});
    }),
  );
};

export const mockResponseForInitializeCardOpeningError = () => {
  server.use(
    http.post(`${CARD_JOURNEY_API}/card-issuance`, () => {
      return HttpResponse.json(createInternalServerError, {status: 500});
    }),
  );
};
