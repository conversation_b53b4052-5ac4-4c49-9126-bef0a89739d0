import {CARDS_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockListTransactionHistoriesResponse from './data-sources/list-transaction-histories.json';

export const mockPaginationResponseForGetListHistory = (options?: {fixedPageSize?: number}) => {
  server.use(
    http.get(`${CARDS_API}/:id/transaction-histories`, ({request}) => {
      const url = new URL(request.url);
      const page = Number(url.searchParams.get('page') ?? 1);
      /**
       * option @param fixedPageSize to test pagination for integration tests
       * default to test service apis
       */
      const size = options?.fixedPageSize ?? Number(url.searchParams.get('size') ?? 3);

      // Split mock data to 2 pages
      const data = mockListTransactionHistoriesResponse.transactionHistories;
      const start = (page - 1) * size;
      const end = start + size;
      const pagedData = data.slice(start, end);

      return HttpResponse.json(
        {
          totalRecord: data.length,
          transactionHistories: pagedData,
        },
        {status: 200},
      );
    }),
  );
};

export const mock2ndPageFailedForGetListHistory = () => {
  server.use(
    http.get(`${CARDS_API}/:id/transaction-histories`, ({request}) => {
      const url = new URL(request.url);
      const page = Number(url.searchParams.get('page') ?? 1);
      const size = Number(3);

      console.log('mock2ndPageFailedForGetListFilterHistory', page);

      if (page === 2) {
        const error = createInternalServerError();
        return HttpResponse.json(error, {status: 500});
      }
      // Split mock data to 2 pages
      const data = mockListTransactionHistoriesResponse.transactionHistories;
      const start = (page - 1) * size;
      const end = start + size;
      const pagedData = data.slice(start, end);

      return HttpResponse.json(
        {
          totalRecord: data.length,
          transactionHistories: pagedData,
        },
        {status: 200},
      );
    }),
  );
};

export const mockEmptyResponseForGetListHistory = () => {
  server.use(
    http.get(`${CARDS_API}/:id/transaction-histories`, () => {
      return HttpResponse.json(
        {
          totalRecord: 0,
          transactionHistories: [],
        },
        {status: 200},
      );
    }),
  );
};

export const mockInvalidResponseForGetListHistory = () => {
  server.use(
    http.get(`${CARDS_API}/:id/transaction-histories`, () => {
      return HttpResponse.json(
        {
          totalRecord: '2',
          transactionHistories: 'invalid data',
        },
        {status: 200},
      );
    }),
  );
};

export {mockListTransactionHistoriesResponse};
