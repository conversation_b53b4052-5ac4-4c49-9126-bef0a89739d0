import {ARRANGEMENT_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockListLinkAccountResponse from './data-sources/list-link-accounts.json';

export const mockEmptyResponseForGetListLinkAccount = () => {
  server.use(
    http.post(`${ARRANGEMENT_API}/search`, () => {
      return HttpResponse.json(
        {
          totalCount: 0,
          data: [],
        },
        {status: 200},
      );
    }),
  );
};

export const mockResponseForGetLinkAccount = () => {
  server.use(
    http.post(`${ARRANGEMENT_API}/search`, () => {
      return HttpResponse.json(mockListLinkAccountResponse, {status: 200});
    }),
  );
};

export const mockNotHaveAliasResponseForGetLinkAccount = () => {
  server.use(
    http.post(`${ARRANGEMENT_API}/search`, () => {
      return HttpResponse.json(
        {
          data: [
            {
              externalArrangementId: 'test-id',
              bankAlias: 'bankAlias',
            },
          ],
          totalCount: 1,
        },
        {status: 200},
      );
    }),
  );
};

export const mockInvalidResponseForGetLinkAccount = () => {
  server.use(
    http.post(`${ARRANGEMENT_API}/search`, () => {
      return HttpResponse.json({data: 666, totalCount: 'invalid data'}, {status: 200});
    }),
  );
};

export const mockServerFailureForGetLinkAccount = () => {
  server.use(
    http.post(`${ARRANGEMENT_API}/search`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockListLinkAccountResponse};
