import {CARDS_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockCardDetailResponse from './data-sources/card-details.json';
import {mockListCardResponse} from './get-list-cards';

export function getMaskedNumberFromId(cardId: string) {
  return mockListCardResponse.find(card => card.id === cardId)!.maskedNumber;
}

export const mockResponseForGetCardDetail = () => {
  server.use(
    http.get(`${CARDS_API}/:id`, ({params}) => {
      const data =
        mockCardDetailResponse[params.id as unknown as keyof typeof mockCardDetailResponse] ??
        mockCardDetailResponse['246245020'];

      // Use masked number as test flag to check render correctly
      const maskedNumber = getMaskedNumberFromId(params.id as string);
      return HttpResponse.json({...data, id: params.id, maskedNumber}, {status: 200});
    }),
  );
};

export const mockServerFailureForGetDetailCard = () => {
  server.use(
    http.get(`${CARDS_API}/:id`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockCardDetailResponse};
