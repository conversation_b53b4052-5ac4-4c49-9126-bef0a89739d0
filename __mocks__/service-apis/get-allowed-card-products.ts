import {CARD_JOURNEY_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockGetAllowedCardProductsResponse from './data-sources/list-allowed-card-products.json';

export const mockResponseForGetAllowedCardProducts = () => {
  server.use(
    http.get(`${CARD_JOURNEY_API}/products`, () => {
      return HttpResponse.json(mockGetAllowedCardProductsResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetAllowedCardProducts = () => {
  server.use(
    http.get(`${CARD_JOURNEY_API}/products`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockInvalidDataForGetAllowedCardProducts = () => {
  server.use(
    http.get(`${CARD_JOURNEY_API}/products`, () => {
      return HttpResponse.json({totalRecords: 5, cards: 'invalid data'}, {status: 200});
    }),
  );
};

export {mockGetAllowedCardProductsResponse};
