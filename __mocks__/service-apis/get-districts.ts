import {ADDRESS_BOOK_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockGetDistrictsResponse from './data-sources/districts.json';

export const mockResponseForGetDistricts = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces/:provinceId/districts`, () => {
      return HttpResponse.json(mockGetDistrictsResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetDistricts = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces/:provinceId/districts`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockInvalidDataForGetDistricts = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces/:provinceId/districts`, () => {
      return HttpResponse.json('data is not array', {status: 200});
    }),
  );
};

export {mockGetDistrictsResponse};
