{"totalCount": 6, "data": [{"id": "07fd5a0f-da86-4629-bfab-9c80ea802fcc", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "Y", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:47.275Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "0b8125a9-8483-44ae-b252-51ea0f3541c9", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": ************, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:48.755Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "1659e7b5-1419-4d48-844d-0f2f3000b37f", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:44.125Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "194ac2e6-403e-44e6-af8a-3e189a6cd06b", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:48.022Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "2dde4acf-3da3-466b-9010-3172ca4ef7b4", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:46.93Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "477c35c7-e7a0-4fd9-a032-fd2dc8ef96eb", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:45.409Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "4dfd5c13-d79c-4a46-ad7c-47a68f411bc3", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:44.534Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "73feead4-18f0-40d3-acc9-46ce128245d4", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:47.652Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "7cc1e20b-ced3-4772-816a-4d7e50ba3bf8", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BAE8498E063A584000AA30A", "productTypeName": "<PERSON>", "externalProductId": "RB.CA.KIMPHAT", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BAE8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.KIMPHAT", "externalTypeId": null, "typeName": "<PERSON>", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:48.415Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "82d69fff-96e5-42f5-b64d-2097419a492c", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BC28498E063A584000AA30A", "productTypeName": "TKCD-<PERSON> d.tu gian tiep ra nc ngoai", "externalProductId": "RB.CA.FPI.FRG", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BC28498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.FPI.FRG", "externalTypeId": null, "typeName": "TKCD-<PERSON> d.tu gian tiep ra nc ngoai", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "USD", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:43.052Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "958d7682-b003-4aca-9db1-42b893a97f81", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:43.696Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "a7c52896-7f47-406d-ba40-9a3bd06f8780", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BAE8498E063A584000AA30A", "productTypeName": "<PERSON>", "externalProductId": "RB.CA.KIMPHAT", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BAE8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.KIMPHAT", "externalTypeId": null, "typeName": "<PERSON>", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:37.771Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "baa38f01-b77a-42ae-9ee4-9bef2cc7b1a9", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:46.566Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "d0d07e1c-560f-4c3c-b1b4-827e4ebba74d", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:44.937Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "d5a73162-ba6d-42d1-af97-b796691923f0", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:46.156Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}, {"id": "e732670d-04af-4bf3-8c85-e4804c05d510", "productKindName": "Current Account", "legalEntityIds": ["8a4e9e0f9619f03001961df4c5a10000"], "productId": "2E05AE8F4BBD8498E063A584000AA30A", "productTypeName": "M-PRO", "externalProductId": "RB.CA.MPRO", "externalArrangementId": "***********", "product": {"id": "2E05AE8F4BBD8498E063A584000AA30A", "translations": [], "additions": null, "externalId": "RB.CA.MPRO", "externalTypeId": null, "typeName": "M-PRO", "productKind": {"externalKindId": "kind1", "kindName": "Current Account", "kindUri": "current-account", "expectsChildren": false, "additions": null}}, "state": {"externalStateId": "ACTIVE", "state": "ACTIVE", "additions": null}, "isDefault": "N", "cifNo": "107614", "name": "ANH NGOC", "availableBalance": 0, "currency": "VND", "bankBranchCode": "VN0011000", "accountOpeningDate": "2025-04-18T07:42:45.798Z", "accountHolderNames": "ANH NGOC", "bankAlias": "<PERSON><PERSON><PERSON>h toán", "debitCards": [], "BBAN": "***********"}]}