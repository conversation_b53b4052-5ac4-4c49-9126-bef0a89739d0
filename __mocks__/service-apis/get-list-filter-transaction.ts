import {GetFilterTransactionHistoryRequest} from '@data/models/card/GetFilterTransactionHistoryRequest';
import {CARDS_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockListFilterTransactionHistoriesResponse from './data-sources/list-filter-transactions.json';

export const mockPaginationResponseForGetListFilterHistory = () => {
  server.use(
    http.post(`${CARDS_API}/:id/histories/search`, async ({request}) => {
      const filterBody = (await request.json()) as GetFilterTransactionHistoryRequest;
      const page = filterBody.page;
      const size = 3;

      // Split mock data to 2 pages
      const data = mockListFilterTransactionHistoriesResponse.transactionHistories;
      const start = (page - 1) * size;
      const end = start + size;
      const pagedData = data.slice(start, end);

      return HttpResponse.json(
        {
          totalRecord: data.length,
          transactionHistories: pagedData,
        },
        {status: 200},
      );
    }),
  );
};

export const mock2ndPageFailedForGetListFilterHistory = () => {
  server.use(
    http.post(`${CARDS_API}/:id/histories/search`, async ({request}) => {
      const filterBody = (await request.json()) as GetFilterTransactionHistoryRequest;
      const page = filterBody.page;
      const size = 3;

      console.log('mock2ndPageFailedForGetListFilterHistory', page);

      if (page === 2) {
        const error = createInternalServerError();
        return HttpResponse.json(error, {status: 500});
      }
      // Split mock data to 2 pages
      const data = mockListFilterTransactionHistoriesResponse.transactionHistories;
      const start = (page - 1) * size;
      const end = start + size;
      const pagedData = data.slice(start, end);

      return HttpResponse.json(
        {
          totalRecord: data.length,
          transactionHistories: pagedData,
        },
        {status: 200},
      );
    }),
  );
};

export const mockEmptyPaginationResponseForGetListFilterHistory = () => {
  server.use(
    http.post(`${CARDS_API}/:id/histories/search`, async () => {
      return HttpResponse.json(
        {
          totalRecord: 0,
          transactionHistories: [],
        },
        {status: 200},
      );
    }),
  );
};

export const mockServerFailureForGetListFilterHistory = () => {
  server.use(
    http.post(`${CARDS_API}/:id/histories/search`, async () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockServerFailureForGetListHistory = () => {
  server.use(
    http.get(`${CARDS_API}/:id/transaction-histories`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockListFilterTransactionHistoriesResponse};
