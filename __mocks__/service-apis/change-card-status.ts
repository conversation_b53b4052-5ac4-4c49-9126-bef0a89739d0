import {CARDS_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';

export const mockResponseForChangeStatus = () => {
  server.use(
    http.patch(`${CARDS_API}/:id/status`, () => {
      return HttpResponse.json({}, {status: 200});
    }),
  );
};

export const mockServerFailureForChangeCardStatus = () => {
  server.use(
    http.patch(`${CARDS_API}/:id/status`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};
