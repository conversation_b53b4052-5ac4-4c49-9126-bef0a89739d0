import {ADDRESS_BOOK_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockGetProvincesResponse from './data-sources/provinces.json';

export const mockResponseForGetProvinces = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces`, () => {
      return HttpResponse.json(mockGetProvincesResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetProvinces = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockInvalidDataForGetProvinces = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces`, () => {
      return HttpResponse.json('data is not array', {status: 200});
    }),
  );
};

export {mockGetProvincesResponse};
