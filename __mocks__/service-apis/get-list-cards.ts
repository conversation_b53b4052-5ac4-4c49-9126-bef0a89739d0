import {CARDS_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockListCardResponse from './data-sources/list-card.json';

export const mockResponseForGetListCard = () => {
  server.use(
    http.get(CARDS_API, () => {
      return HttpResponse.json(mockListCardResponse, {status: 200});
    }),
  );
};

export const mocResponseOnlyDebitForGetListCard = () => {
  server.use(
    http.get(CARDS_API, () => {
      return HttpResponse.json(
        mockListCardResponse.filter(card => card.type === 'DEBIT'),
        {status: 200},
      );
    }),
  );
};

export const mockServerFailureForGetListCard = () => {
  server.use(
    http.get(CARDS_API, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockInvalidDataForGetListCard = () => {
  server.use(
    http.get(CARDS_API, () => {
      return HttpResponse.json({status: 200, success: true, data: 'data is not array'}, {status: 200});
    }),
  );
};

export {mockListCardResponse};
