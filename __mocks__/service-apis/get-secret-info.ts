import {MSBErrorCode} from '@core/MSBErrorCode';
import {ResponseErrors} from '@core/ResponseErrors';
import {CARDS_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockSecretInfoResponse from './data-sources/secret-information.json';

export const mockResponseForGetSecretInfo = () => {
  server.use(
    http.get(`${CARDS_API}/:id/secret-info`, () => {
      return HttpResponse.json(mockSecretInfoResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetSecretInfo = () => {
  server.use(
    http.get(`${CARDS_API}/:id/secret-info`, () => {
      return HttpResponse.json(createInternalServerError(), {status: 500});
    }),
  );
};

export const mockWA4ErrorsForGetCardSecretInfo = () => {
  server.use(
    http.get(`${CARDS_API}/:id/secret-info`, () => {
      const error: ResponseErrors = {
        message: 'Error',
        errors: [
          {
            key: MSBErrorCode['WA4.CA.0017'],
            message: 'Error from WA4',
            context: ['first context'],
          },
        ],
      };
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export {mockSecretInfoResponse};
