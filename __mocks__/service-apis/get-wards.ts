import {ADDRESS_BOOK_API, createInternalServerError, server} from 'mocks/msw-node';
import {http, HttpResponse} from 'msw';
import mockGetWardsResponse from './data-sources/wards.json';

export const mockResponseForGetWards = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces/:provinceId/districts/:districtId/wards`, () => {
      return HttpResponse.json(mockGetWardsResponse, {status: 200});
    }),
  );
};

export const mockServerFailureForGetWards = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces/:provinceId/districts/:districtId/wards`, () => {
      const error = createInternalServerError();
      return HttpResponse.json(error, {status: 500});
    }),
  );
};

export const mockInvalidDataForGetWards = () => {
  server.use(
    http.get(`${ADDRESS_BOOK_API}/provinces/:provinceId/districts/:districtId/wards`, () => {
      return HttpResponse.json('data is not array', {status: 200});
    }),
  );
};

export {mockGetWardsResponse};
