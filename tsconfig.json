{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"forceConsistentCasingInFileNames": true, "paths": {"mocks/*": ["./__mocks__/*"], "@*": ["./src/*"], "@core/*": ["./src/core/*"], "@data/*": ["./src/data/*"], "@models/*": ["./src/data/models/*"], "@entities/*": ["./src/domain/entities/*"], "@domain/*": ["./src/domain/*"], "@use-cases/*": ["./src/domain/use-cases/*"], "@di/*": ["./src/di/*"], "@locales": ["./src/locales/index"], "@images": ["./src/assets/images/index"], "@presentation/*": ["./src/presentation/*"], "@components/*": ["./src/presentation/components/*"], "@hooks/*": ["./src/presentation/hooks/*"], "@utils/*": ["./src/utils/*"], "@utils": ["./src/utils/index"], "@constants/*": ["./src/constants/*"], "@constants": ["./src/constants/index"], "@app-navigation/*": ["./src/presentation/navigation/*"], "@app-screens/*": ["./src/presentation/screens/*"], "@view-models/*": ["./src/presentation/view-models/*"], "@store/*": ["./src/presentation/store/*"]}}}