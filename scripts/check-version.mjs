#!/usr/bin/env node

/**
 * This is a <PERSON>sky pre-push hook that ensures package.json version has been incremented
 * before pushing to the remote repository.
 *
 * It compares the current version with the version in the remote repository's package.json
 * to ensure proper versioning.
 */

import {spawnSync} from 'child_process';
import fs from 'fs';
import path from 'path';

const RED = '\x1b[31m%s\x1b[0m';
const GREEN = '\x1b[32m%s\x1b[0m';
const YELLOW = '\x1b[33m%s\x1b[0m';

// Read current package.json
const packageJsonPath = path.resolve(process.cwd(), 'package.json');
let currentPackageJson;

try {
  currentPackageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
} catch (err) {
  console.error(RED, 'Error reading package.json:', err.message);
  process.exit(1);
}

const currentVersion = currentPackageJson.version;

if (!currentVersion) {
  console.error(RED, 'No version specified in package.json');
  process.exit(1);
}

// Get the remote version from the target branch
// const getCurrentBranch = () => {
//   try {
//     const result = spawnSync('git', ['rev-parse', '--abbrev-ref', 'HEAD'], {
//       encoding: 'utf8'
//       shell: false
//     });
//     if (result.error) throw result.error;
//     if (result.status !== 0) throw new Error(`Command failed with status ${result.status}: ${result.stderr}`);
//     return result.stdout.trim();
//   } catch (err) {
//     console.error(RED, 'Error getting current branch:', err.message);
//     process.exit(1);
//   }
// };

const getRemoteVersion = branch => {
  try {
    // Get the remote name (usually 'origin')
    const remotesResult = spawnSync('git', ['remote'], {
      encoding: 'utf8',
      shell: false,
    });

    if (remotesResult.error) throw remotesResult.error;
    if (remotesResult.status !== 0) {
      throw new Error(`Command failed with status ${remotesResult.status}: ${remotesResult.stderr}`);
    }

    const remotes = remotesResult.stdout.trim().split('\n');
    if (!remotes.length) {
      console.error(RED, 'No git remotes found');
      process.exit(1);
    }
    const remote = remotes[0]; // Use the first remote (usually 'origin')

    // Try to fetch the package.json from the remote
    console.log(YELLOW, `Fetching ${remote}/${branch} to check version...`);

    try {
      // Make sure we have the latest from remote
      const fetchResult = spawnSync('git', ['fetch', remote, branch], {
        stdio: 'inherit',
        shell: false,
      });

      if (fetchResult.error) throw fetchResult.error;
      if (fetchResult.status !== 0) {
        throw new Error(`Command failed with status ${fetchResult.status}`);
      }
    } catch (err) {
      // If this is a new branch, there's no remote version to compare with
      console.log(
        YELLOW,
        `Branch ${branch} doesn't exist on remote ${remote} or couldn't be fetched. Assuming this is a new branch.`,
        err,
      );
      return null;
    }

    // Get the package.json content from the remote
    try {
      const showResult = spawnSync('git', ['show', `${remote}/${branch}:package.json`], {
        encoding: 'utf8',
        shell: false,
      });

      if (showResult.error) throw showResult.error;
      if (showResult.status !== 0) {
        throw new Error(`Command failed with status ${showResult.status}: ${showResult.stderr}`);
      }

      return JSON.parse(showResult.stdout).version;
    } catch (err) {
      console.log(
        YELLOW,
        'No package.json found on remote or parsing error. Allowing push without version check.',
        err,
      );
      return null;
    }
  } catch (err) {
    console.error(RED, 'Error getting remote version:', err.message);
    process.exit(1);
  }
};

const compareVersions = (v1, v2) => {
  if (!v1 || !v2) {
    return 0;
  }

  const v1Parts = v1.split('.').map(Number);
  const v2Parts = v2.split('.').map(Number);

  for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
    const part1 = v1Parts[i] || 0;
    const part2 = v2Parts[i] || 0;

    if (part1 > part2) {
      return 1;
    }
    if (part1 < part2) {
      return -1;
    }
  }

  return 0; // Versions are equal
};

// Main execution
// const currentBranch = getCurrentBranch();
const remoteVersion = getRemoteVersion('sit');

// If we couldn't get a remote version, allow the push
if (remoteVersion === null) {
  console.log(
    GREEN,
    `Allowing push as no valid remote version was found to compare with. Current version: ${currentVersion}`,
  );
  process.exit(0);
}

// Compare versions
const comparison = compareVersions(currentVersion, remoteVersion);

if (comparison <= 0) {
  console.error(RED, 'Version in package.json must be incremented before pushing.');
  console.error(RED, `Current version: ${currentVersion}, Remote version: ${remoteVersion}`);
  console.error(RED, 'Please update the version in package.json and try again.');
  process.exit(1);
}

console.log(GREEN, `Version check passed. Current: ${currentVersion}, Remote: ${remoteVersion}`);
process.exit(0);
