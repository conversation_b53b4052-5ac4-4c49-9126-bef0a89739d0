#!/usr/bin/env node
/**
 * -----------------------------------------------------------------------------------
 *  HDSD:
 *    node scripts/msb-template.mjs --domain=<DomainName> --useCase=<FunctionName> --dataModel=<DataModelName> --errorMapper=<ErrorMapperName> [--param]
 *
 *  Ví dụ:
 *    yarn gen:clean --domain=Payment --useCase=PatchPaymentOrderById --dataModel=PaymentOrder --param
 *    hoặc:
 *    node scripts/msb-template.mjs --domain=Payment --useCase=PatchPaymentOrderById --dataModel=PaymentOrder --param
 *
 * Target:
 * - Tự động tạo/chèn method vào:
 *   1) I[ModuleName]Service.ts (interface)
 *   2) [ModuleName]Service.ts (class)
 *   3) I[ModuleName]Repository.ts (interface)
 *   4) [ModuleName]Repository.ts (class)
 *   5) Tạo UseCase: src/domain/use-cases/[domain-name-kebab]/[FunctionNamePascal]UseCase.ts
 *   6) Tạo Model: [FunctionNamePascal]Request.ts, [DataModelNamePascal]DTO.ts
 *      trong src/data/models/[domain-name-kebab]
 *   7) Tạo Entity: [FunctionNamePascal]State.ts (enum), [DataModelNamePascal].ts
 *
 * - Thư mục => kebab-case (vd "get-branch")
 * - File => PascalCase (vd "GetBranchResponse.ts"), KHÔNG hạ thành "GetbranchResponse".
 * - Method => camelCase (vd "getBranch()")
 * - `--param` => có request param.
 *
 *
 * -----------------------------------------------------------------------------------
 */

import {spawnSync} from 'child_process';
import fs from 'fs';

import path from 'path';
import {Project} from 'ts-morph';
import url from 'url';

// =========================== UTILS ===========================

/**
 * "GetBranch" => "getBranch"
 */
function toCamelCase(str) {
  if (!str) {
    return str;
  }
  const first = str.charAt(0).toLowerCase();
  const rest = str.slice(1);
  return first + rest;
}

/**
 * Mới: Tách theo dấu gạch dưới, gạch ngang, khoảng trắng,
 * rồi chỉ viết hoa chữ cái đầu mỗi token, giữ nguyên phần còn lại.
 *
 * "GetBranch" => ["GetBranch"] => "GetBranch"
 * "get_branch" => ["get", "branch"] => "Get" + "Branch" => "GetBranch"
 * "Get_branch" => ["Get", "branch"] => "Get" + "Branch" => "GetBranch"
 */
function toPascalCase(str) {
  if (!str) {
    return str;
  }
  return str
    .split(/[_-\s]+/)
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join('');
}

/**
 * "GetBranch" => "get-branch"
 */
function toKebabCase(str) {
  if (!str) {
    return str;
  }
  return str.replace(/[A-Z]/g, letter => `-${letter.toLowerCase()}`).replace(/^-/, '');
}

/** Tạo file skeleton nếu chưa có, trả về sourceFile. */
function loadOrCreateSourceFile(project, filePath, skeletonContent) {
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, skeletonContent.trim() + '\n');
    console.log(`[CREATE] Skeleton => ${filePath}`);
  }
  return project.addSourceFileAtPath(filePath);
}

// =========================== AST HELPER ===========================

function hadInterfaceInSource(sourceFile, interfaceName, methodName) {
  let iface = sourceFile.getInterface(interfaceName);
  if (!iface) {
    iface = sourceFile.addInterface({
      name: interfaceName,
      isExported: true,
    });
    console.log(`[ADD] Created interface ${interfaceName} in ${sourceFile.getFilePath()}`);
  }

  const exist = iface.getMethods().find(m => m.getName() === methodName);
  if (exist) {
    console.log(`[SKIP] Interface ${interfaceName} đã có method "${methodName}"`);
    return true;
  }

  return false;
}

function addMethodToServiceInterface(
  sourceFile,
  interfaceName,
  methodName,
  hasNoParam,
  functionNamePascal,
  dataModelPascal,
  noDataModel,
) {
  if (hadInterfaceInSource(sourceFile, interfaceName, methodName)) {
    return;
  }

  let iface = sourceFile.getInterface(interfaceName);
  const returnType = noDataModel ? 'Promise<BaseResponse<any>>' : `Promise<BaseResponse<${dataModelPascal}DTO>>`;

  if (hasNoParam) {
    iface.addMethod({
      name: methodName,
      returnType: returnType,
    });
  } else {
    iface.addMethod({
      name: methodName,
      parameters: [
        {
          name: 'request',
          type: `${functionNamePascal}Request`,
        },
      ],
      returnType: returnType,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into interface ${interfaceName}`);
}

function addMethodToRepositoryInterface(
  sourceFile,
  interfaceName,
  methodName,
  hasNoParam,
  functionNamePascal,
  dataModelPascal,
  noDataModel,
) {
  if (hadInterfaceInSource(sourceFile, interfaceName, methodName)) {
    return;
  }

  let iface = sourceFile.getInterface(interfaceName);
  const returnType = noDataModel ? 'Promise<ResultState<any>>' : `Promise<ResultState<${dataModelPascal}>>`;

  if (hasNoParam) {
    iface.addMethod({
      name: methodName,
      returnType: returnType,
    });
  } else {
    iface.addMethod({
      name: methodName,
      parameters: [
        {
          name: 'input',
          type: `${functionNamePascal}Input`,
        },
      ],
      returnType: returnType,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into interface ${interfaceName}`);
}

function addMethodToRepositoryClass({
  sourceFile,
  className,
  methodName,
  hasNoParam,
  functionNamePascal,
  dataModelPascal,
  domainNameCamel,
  noDataModel,
}) {
  let cls = sourceFile.getClass(className);
  if (!cls) {
    cls = sourceFile.addClass({
      name: className,
      isExported: true,
    });
    console.log(`[ADD] Created class ${className} in ${sourceFile.getFilePath()}`);
  }

  const existMethod = cls.getInstanceMethod(methodName);
  if (existMethod) {
    console.log(`[SKIP] Class ${className} đã có method "${methodName}"`);
    return;
  }

  const mapper = noDataModel ? '() => ({})' : `mapTo${dataModelPascal}`;
  const dataModel = noDataModel ? 'any' : `${dataModelPascal}`;
  if (hasNoParam) {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      returnType: `Promise<ResultState<${dataModel}>>`,
      statements: `
        return handleData(this.${domainNameCamel}Service.${methodName}(), ${mapper});
      `,
    });
  } else {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      parameters: [
        {
          name: 'input',
          type: `${functionNamePascal}Input`,
        },
      ],
      returnType: `Promise<ResultState<${dataModel}>>`,
      statements: `
        return handleData(this.${domainNameCamel}Service.${methodName}(input), ${mapper});
      `,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into class ${className}`);
}

// add method to Datasource
function addMethodToServiceClass({
  sourceFile,
  className,
  methodName,
  hasNoParam,
  functionNamePascal,
  rawModuleName,
  dataModelPascal,
  noDataModel,
  errorMapperCamel,
}) {
  let cls = sourceFile.getClass(className);
  if (!cls) {
    cls = sourceFile.addClass({
      name: className,
      isExported: true,
    });
    console.log(`[ADD] Created class ${className} in ${sourceFile.getFilePath()}`);
  }

  const existMethod = cls.getInstanceMethod(methodName);
  if (existMethod) {
    console.log(`[SKIP] Class ${className} đã có method "${methodName}"`);
    return;
  }
  const domainName = toCamelCase(rawModuleName);

  const returnType = noDataModel ? 'Promise<BaseResponse<any>>' : `Promise<BaseResponse<${dataModelPascal}DTO>>`;
  const errorMapper = errorMapperCamel ? `, ${errorMapperCamel}ErrorMapper` : '';
  if (hasNoParam) {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      returnType,
      statements: `
          const url = PathResolver.${domainName}.${methodName}();
          const response = await this.httpClient.post(url, {});
          return handleResponse(response${errorMapper});
      `,
    });
  } else {
    cls.addMethod({
      name: methodName,
      isAsync: true,
      parameters: [
        {
          name: 'request',
          type: `${functionNamePascal}Request`,
        },
      ],
      returnType,
      statements: `
          const url = PathResolver.${domainName}.${methodName}();
          const response = await this.httpClient.post(url, request);
          return handleResponse(response${errorMapper});
      `,
    });
  }

  console.log(`[UPDATE] Added method "${methodName}" into class ${className}`);
}

// add method to Repository

/**
 * Tạo file UseCase nếu chưa, chèn import, skeleton
 */
function createUseCaseFile({
  filePath,
  project,
  moduleNameExact,
  functionNamePascal,
  methodName,
  hasNoParam,
  functionNameKebab,
  dataModelPascal,
  domainNameKebab,
  noDataModel,
}) {
  if (fs.existsSync(filePath)) {
    console.log(`[SKIP] UseCase file đã tồn tại: ${filePath}`);
    return;
  }

  const returnType = noDataModel ? 'ResultState<any>' : `ResultState<${dataModelPascal}>`;

  const skeleton = `
  export class ${functionNamePascal}UseCase {
  private readonly repository: I${moduleNameExact}Repository;

  constructor(repository: I${moduleNameExact}Repository) {
    this.repository = repository;
  }

  public async execute${hasNoParam ? '()' : `(input: ${functionNamePascal}Input)`}: Promise<${returnType}> {
    ${`return this.repository.${methodName}(${hasNoParam ? '' : 'input'});`}
  }
}

  export interface ${functionNamePascal}Input extends BaseInput{
      // add more input
  }
`;
  fs.writeFileSync(filePath, '\n' + skeleton.trim());
  console.log(`[CREATE] UseCase => ${filePath}`);

  const sf = project.addSourceFileAtPath(filePath);
  // import
  const imports = [
    `import {I${moduleNameExact}Repository} from '@domain/repositories/I${moduleNameExact}Repository';`,
    "import {ResultState} from '@core/ResultState';",
    "import {BaseInput} from '@domain/entities/base/BaseInput';",
  ];

  if (!noDataModel) {
    imports.push(`import {${dataModelPascal}} from '@entities/${domainNameKebab}/${dataModelPascal}';`);
  }

  sf.insertStatements(0, imports);

  sf.saveSync();
}

/**
 * Tạo Request/Response model
 */
function createModelFiles(dirPath, project, functionNamePascal, hasNoParam, dataModelPascal, noDataModel) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, {recursive: true});
  }

  // Request
  if (!hasNoParam) {
    const reqPath = path.join(dirPath, `${functionNamePascal}Request.ts`);
    if (!fs.existsSync(reqPath)) {
      fs.writeFileSync(reqPath, `export interface ${functionNamePascal}Request {\n  // TODO: define fields\n}\n`);
      console.log(`[CREATE] => ${reqPath}`);
      project.addSourceFileAtPath(reqPath);
    }
  }

  // DTO
  if (!noDataModel) {
    const resPath = path.join(dirPath, `${dataModelPascal}DTO.ts`);
    if (!fs.existsSync(resPath)) {
      fs.writeFileSync(resPath, `export interface ${dataModelPascal}DTO {\n  // TODO: define fields\n}\n`);
      console.log(`[CREATE] => ${resPath}`);
      project.addSourceFileAtPath(resPath);
    }
  }
}
// =========================== MAIN ===========================
async function main() {
  // Step 1: Parse arguments
  const {moduleName, functionName, hasNoParam, dataModel, noDataModel, errorMapper} = await parseArguments();

  // Step 2: Convert names to different cases
  const names = generateNames(moduleName, functionName, dataModel, errorMapper);

  // Step 3: Set up project and directories
  const {project, rootDir} = setupProject();
  const dirs = createDirectories(rootDir, names);

  // Step 4: Create Entity models
  createEntityModels(dirs.entityDir, project, names, noDataModel);

  // Step 5: Create Mappers
  createMappers(dirs.mapperDir, project, names, noDataModel, errorMapper);

  // Step 6: Create Service interfaces and implementations
  createServices(project, dirs, names, hasNoParam, noDataModel, errorMapper);

  // Step 7: Create Repository interfaces and implementations
  createRepositories(project, dirs, names, hasNoParam, noDataModel);

  // Step 8: Create UseCases
  createUseCases(project, dirs, names, hasNoParam, noDataModel);

  // Step 9: Create Model files
  createModels(dirs.modelDir, project, names, hasNoParam, noDataModel);

  // Step 10: Update PathResolver and DIContainer
  updatePathResolver(rootDir, names, hasNoParam);
  updateDIContainer(rootDir, names);

  // Step 11: Save and format code
  await project.save();
  formatCode();

  console.log(`\n[DONE] Tạo/chèn method thành công! Module=${moduleName}, Function=${functionName}\n`);
}

function formatCode() {
  const prettierResult = spawnSync('npx', ['prettier', '--write', 'src/**/*.{ts,tsx}'], {
    stdio: 'inherit',
    shell: false,
  });

  if (prettierResult.error) {
    console.warn('⚠️ Méo chạy được Prettier. Cài vào ae ơi.');
  }
  if (prettierResult.status !== 0) {
    throw new Error(`Prettier failed with status ${prettierResult.status}`);
  }

  console.log('\n✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨✨ Code formatted');
}

// Parse command line arguments
async function parseArguments() {
  const args = process.argv.slice(2);
  if (args.length < 2) {
    console.log('Cách dùng: node scripts/msb-template.mjs <ModuleName> <FunctionName> [--noparam]');
    process.exit(1);
  }

  const minimistModule = await import('minimist');
  const minimist = minimistModule.default;
  const paths = minimist(args);

  const moduleName = paths.domain;
  const functionName = paths.useCase;
  const hasNoParam = !paths.param;
  const dataModel = paths.dataModel;
  const noDataModel = !dataModel;
  const errorMapper = paths.errorMapper;

  return {moduleName, functionName, hasNoParam, dataModel, noDataModel, errorMapper};
}

// Generate different case formats of names
function generateNames(moduleName, functionName, dataModel, errorMapper) {
  const methodName = toCamelCase(functionName);
  const functionNamePascal = toPascalCase(functionName);
  const dataModelPascal = toPascalCase(dataModel);
  const functionNameKebab = toKebabCase(dataModel);
  const domainNameKebab = toKebabCase(moduleName);
  const domainNameCamel = toCamelCase(moduleName);
  const errorMapperCamel = toCamelCase(errorMapper);
  const errorMapperPascal = toPascalCase(errorMapper);
  const moduleNameExact = moduleName;

  return {
    methodName,
    functionNamePascal,
    dataModelPascal,
    functionNameKebab,
    domainNameKebab,
    domainNameCamel,
    errorMapperCamel,
    errorMapperPascal,
    moduleNameExact,
  };
}

// Set up project and get root directory
function setupProject() {
  const project = new Project();
  const scriptDir = path.dirname(url.fileURLToPath(import.meta.url));
  const rootDir = path.join(scriptDir, '..');

  return {project, rootDir};
}

// Create necessary directories
function createDirectories(rootDir, names) {
  const {domainNameKebab, moduleNameExact} = names;

  // Create data source directories
  const dataSourceDir = path.join(rootDir, 'src', 'data', 'datasources');
  const remoteDir = path.join(dataSourceDir, 'remote');

  // Create repository directories
  const domainRepoDir = path.join(rootDir, 'src', 'domain', 'repositories');
  const dataRepoDir = path.join(rootDir, 'src', 'data', 'repositories');

  // Create use case directory
  const useCaseDir = path.join(rootDir, 'src', 'domain', 'use-cases', toKebabCase(moduleNameExact));

  // Create entity directory
  const entityDir = path.join(rootDir, 'src', 'domain', 'entities', domainNameKebab);

  // Create mapper directory
  const mapperDir = path.join(rootDir, 'src', 'data', 'mappers', domainNameKebab);

  // Create model directory
  const modelDir = path.join(rootDir, 'src', 'data', 'models', domainNameKebab);

  // Ensure all directories exist
  [dataSourceDir, remoteDir, domainRepoDir, dataRepoDir, useCaseDir, entityDir, mapperDir, modelDir].forEach(dir => {
    fs.mkdirSync(dir, {recursive: true});
  });

  return {
    dataSourceDir,
    remoteDir,
    domainRepoDir,
    dataRepoDir,
    useCaseDir,
    entityDir,
    mapperDir,
    modelDir,
  };
}

// Create entity models
function createEntityModels(entityDir, project, names, noDataModel) {
  const {dataModelPascal} = names;

  if (!noDataModel) {
    const modelPath = path.join(entityDir, `${dataModelPascal}.ts`);
    if (!fs.existsSync(modelPath)) {
      fs.writeFileSync(modelPath, `export interface ${dataModelPascal} {\n  // TODO: define fields\n}\n`);
      console.log(`[CREATE] => ${modelPath}`);
      project.addSourceFileAtPath(modelPath);
    }
  }
}

// Create mappers
function createMappers(mapperDir, project, names, noDataModel, errorMapper) {
  const {dataModelPascal, domainNameKebab, errorMapperCamel, errorMapperPascal} = names;

  if (!noDataModel) {
    createDataModelMapper(mapperDir, project, dataModelPascal, domainNameKebab);
  }

  if (errorMapper) {
    createErrorMapper(mapperDir, project, errorMapperCamel, errorMapperPascal);
  }
}

// Create data model mapper
function createDataModelMapper(mapperDir, project, dataModelPascal, domainNameKebab) {
  const mapperPath = path.join(mapperDir, `${dataModelPascal}Mapper.ts`);
  if (!fs.existsSync(mapperPath)) {
    const mapperContent = `
import { ${dataModelPascal}DTO } from '@models/${domainNameKebab}/${dataModelPascal}DTO';
import { ${dataModelPascal} } from '@entities/${domainNameKebab}/${dataModelPascal}';

export function mapTo${dataModelPascal}(dto?: ${dataModelPascal}DTO): ${dataModelPascal} {
  return {};
}
`;
    fs.writeFileSync(mapperPath, mapperContent.trim() + '\n');
    console.log(`[CREATE] => ${mapperPath}`);
    project.addSourceFileAtPath(mapperPath);
  }
}

// Create error mapper
function createErrorMapper(mapperDir, project, errorMapperCamel, errorMapperPascal) {
  const errorMapperPath = path.join(mapperDir, `${errorMapperPascal}ErrorMapper.ts`);
  if (!fs.existsSync(errorMapperPath)) {
    const errorMapperContent = `
import {BaseErrorMapper} from '@core/BaseErrorMapper';
import {MSBErrorCode} from '@core/MSBErrorCode';
import {ResponseCodes} from '@utils/ResponseHandler';

export const ${errorMapperCamel}ErrorMapper: BaseErrorMapper = status => {
  if (!status) {
    return MSBErrorCode.Default;
  }

  const errorCodeMapping: Record<string, string> = {
    [ResponseCodes.GatewayTimeout.toString()]: MSBErrorCode.Timeout,
  };

  return errorCodeMapping[status.toString()] || MSBErrorCode.Default;
};
`;
    fs.writeFileSync(errorMapperPath, errorMapperContent.trim() + '\n');
    console.log(`[CREATE] => ${errorMapperPath}`);
    project.addSourceFileAtPath(errorMapperPath);
  }
}

// Create services (interface, remote and implementations)
function createServices(project, dirs, names, hasNoParam, noDataModel, errorMapper) {
  createServiceInterface(project, dirs, names, hasNoParam, noDataModel);
  createRemoteService(project, dirs, names, hasNoParam, noDataModel, errorMapper);
}

// Create service interface
function createServiceInterface(project, dirs, names, hasNoParam, noDataModel) {
  const {moduleNameExact, functionNamePascal, methodName, dataModelPascal, domainNameKebab} = names;
  const iDataSourceName = `I${moduleNameExact}Service`;
  const iDataSourceFilePath = path.join(dirs.dataSourceDir, `${iDataSourceName}.ts`);

  const skeleton = `export interface ${iDataSourceName} {\n}\n`;
  const sf = loadOrCreateSourceFile(project, iDataSourceFilePath, skeleton);

  // Add imports
  addServiceInterfaceImports(sf, hasNoParam, functionNamePascal, dataModelPascal, domainNameKebab, noDataModel);

  // Add method to interface
  addMethodToServiceInterface(
    sf,
    iDataSourceName,
    methodName,
    hasNoParam,
    functionNamePascal,
    dataModelPascal,
    noDataModel,
  );

  sf.saveSync();
}

// Add imports to service interface
function addServiceInterfaceImports(sf, hasNoParam, functionNamePascal, dataModelPascal, domainNameKebab, noDataModel) {
  if (!hasNoParam) {
    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}Request`))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `@models/${domainNameKebab}/${functionNamePascal}Request`,
        namedImports: [`${functionNamePascal}Request`],
      });
    }
  }

  if (!noDataModel && !sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${dataModelPascal}DTO`))) {
    sf.insertImportDeclaration(0, {
      moduleSpecifier: `@models/${domainNameKebab}/${dataModelPascal}DTO`,
      namedImports: [`${dataModelPascal}DTO`],
    });
  }

  if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('BaseResponse'))) {
    sf.insertImportDeclaration(0, {
      moduleSpecifier: '@core/BaseResponse',
      namedImports: ['BaseResponse'],
    });
  }
}

// Create remote service implementation
function createRemoteService(project, dirs, names, hasNoParam, noDataModel, errorMapper) {
  const {
    moduleNameExact,
    functionNamePascal,
    methodName,
    dataModelPascal,
    domainNameKebab,
    errorMapperCamel,
    errorMapperPascal,
  } = names;
  const iDataSourceName = `I${moduleNameExact}Service`;
  const remoteName = `${moduleNameExact}Service`;
  const remoteFilePath = path.join(dirs.remoteDir, `${remoteName}.ts`);

  const skeleton = `export class ${remoteName} implements ${iDataSourceName} {\n  constructor(private readonly httpClient: IHttpClient) {}\n}\n`;
  const sf = loadOrCreateSourceFile(project, remoteFilePath, skeleton);

  // Add imports
  addRemoteServiceImports({
    sf,
    iDataSourceName,
    hasNoParam,
    functionNamePascal,
    dataModelPascal,
    domainNameKebab,
    errorMapper,
    errorMapperPascal,
  });

  // Add method to class
  addMethodToServiceClass({
    sourceFile: sf,
    className: remoteName,
    methodName,
    hasNoParam,
    functionNamePascal,
    rawModuleName: moduleNameExact,
    dataModelPascal,
    noDataModel,
    errorMapperCamel,
  });

  sf.saveSync();
}

// Add imports to remote service
function addRemoteServiceImports({
  sf,
  iDataSourceName,
  hasNoParam,
  functionNamePascal,
  dataModelPascal,
  domainNameKebab,
  errorMapper,
  errorMapperPascal,
}) {
  const importDeclarations = [
    {
      moduleSpecifier: `../${iDataSourceName}`,
      namedImports: [iDataSourceName],
    },
    {
      moduleSpecifier: '@utils/ResponseHandler',
      namedImports: ['handleResponse'],
    },
    {
      moduleSpecifier: 'msb-host-shared-module',
      namedImports: ['IHttpClient'],
    },
    {
      moduleSpecifier: '@utils/PathResolver',
      namedImports: ['PathResolver'],
    },
    {
      moduleSpecifier: '@core/BaseResponse',
      namedImports: ['BaseResponse'],
    },
    {
      moduleSpecifier: `@models/${domainNameKebab}/${dataModelPascal}DTO`,
      namedImports: [`${dataModelPascal}DTO`],
    },
  ];

  if (!hasNoParam) {
    importDeclarations.push({
      moduleSpecifier: `@models/${domainNameKebab}/${functionNamePascal}Request`,
      namedImports: [`${functionNamePascal}Request`],
    });
  }

  if (errorMapper) {
    importDeclarations.push({
      moduleSpecifier: `@data/mappers/${domainNameKebab}/${errorMapperPascal}ErrorMapper`,
      namedImports: [`${toCamelCase(errorMapper)}ErrorMapper`],
    });
  }

  // Add all imports that don't already exist
  importDeclarations.forEach(importDecl => {
    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(importDecl.moduleSpecifier))) {
      sf.insertImportDeclaration(0, importDecl);
    }
  });
}

// Create repositories (interface and implementation)
function createRepositories(project, dirs, names, hasNoParam, noDataModel) {
  createRepositoryInterface(project, dirs, names, hasNoParam, noDataModel);
  createRepositoryImplementation(project, dirs, names, hasNoParam, noDataModel);
}

// Create repository interface
function createRepositoryInterface(project, dirs, names, hasNoParam, noDataModel) {
  const {moduleNameExact, functionNamePascal, methodName, dataModelPascal, domainNameKebab} = names;
  const iRepoName = `I${moduleNameExact}Repository`;
  const iRepoFilePath = path.join(dirs.domainRepoDir, `${iRepoName}.ts`);

  const iRepoSkeleton = `export interface ${iRepoName} {\n}\n`;
  const sf = loadOrCreateSourceFile(project, iRepoFilePath, iRepoSkeleton);

  // Add imports
  addRepositoryInterfaceImports(sf, hasNoParam, functionNamePascal, dataModelPascal, domainNameKebab, noDataModel);

  // Add method to interface
  addMethodToRepositoryInterface(
    sf,
    iRepoName,
    methodName,
    hasNoParam,
    functionNamePascal,
    dataModelPascal,
    noDataModel,
  );

  sf.saveSync();
}

// Add imports to repository interface
function addRepositoryInterfaceImports(
  sf,
  hasNoParam,
  functionNamePascal,
  dataModelPascal,
  domainNameKebab,
  noDataModel,
) {
  if (!noDataModel && !sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${dataModelPascal}`))) {
    sf.insertImportDeclaration(0, {
      moduleSpecifier: `@entities/${domainNameKebab}/${dataModelPascal}`,
      namedImports: [`${dataModelPascal}`],
    });
  }

  if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes('ResultState'))) {
    sf.insertImportDeclaration(0, {
      moduleSpecifier: '@core/ResultState',
      namedImports: ['ResultState'],
    });
  }

  if (!hasNoParam) {
    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(`${functionNamePascal}UseCase`))) {
      sf.insertImportDeclaration(0, {
        moduleSpecifier: `@use-cases/${domainNameKebab}/${functionNamePascal}UseCase`,
        namedImports: [`${functionNamePascal}Input`],
      });
    }
  }
}

// Create repository implementation
function createRepositoryImplementation(project, dirs, names, hasNoParam, noDataModel) {
  const {moduleNameExact, functionNamePascal, methodName, dataModelPascal, domainNameKebab, domainNameCamel} = names;
  const iRepoName = `I${moduleNameExact}Repository`;
  const repoName = `${moduleNameExact}Repository`;
  const iDataSourceName = `I${moduleNameExact}Service`;
  const repoFilePath = path.join(dirs.dataRepoDir, `${repoName}.ts`);

  const repoSkeleton = `export class ${repoName} implements ${iRepoName} {
  private readonly ${domainNameCamel}Service: ${iDataSourceName};

  constructor(${domainNameCamel}Service: ${iDataSourceName}) {
    this.${domainNameCamel}Service = ${domainNameCamel}Service;
  }
}
`;
  const sf = loadOrCreateSourceFile(project, repoFilePath, repoSkeleton);

  // Add imports
  addRepositoryImplementationImports({
    sf,
    iRepoName,
    iDataSourceName,
    hasNoParam,
    functionNamePascal,
    dataModelPascal,
    domainNameKebab,
    noDataModel,
  });

  // Add method to class
  addMethodToRepositoryClass({
    sourceFile: sf,
    className: repoName,
    methodName,
    hasNoParam,
    functionNamePascal,
    dataModelPascal,
    domainNameCamel,
    noDataModel,
  });

  sf.saveSync();
}

// Add imports to repository implementation
function addRepositoryImplementationImports({
  sf,
  iRepoName,
  iDataSourceName,
  hasNoParam,
  functionNamePascal,
  dataModelPascal,
  domainNameKebab,
  noDataModel,
}) {
  const importDeclarations = [
    {
      moduleSpecifier: `@domain/repositories/${iRepoName}`,
      namedImports: [iRepoName],
    },
    {
      moduleSpecifier: '@core/ResultState',
      namedImports: ['ResultState'],
    },
    {
      moduleSpecifier: `../datasources/${iDataSourceName}`,
      namedImports: [iDataSourceName],
    },
    {
      moduleSpecifier: '@utils/HandleData',
      namedImports: ['handleData'],
    },
  ];

  if (!hasNoParam) {
    importDeclarations.push({
      moduleSpecifier: `@domain/use-cases/${domainNameKebab}/${functionNamePascal}UseCase`,
      namedImports: [`${functionNamePascal}Input`],
    });
  }

  if (!noDataModel) {
    importDeclarations.push({
      moduleSpecifier: `@entities/${domainNameKebab}/${dataModelPascal}`,
      namedImports: [`${dataModelPascal}`],
    });

    importDeclarations.push({
      moduleSpecifier: `@data/mappers/${domainNameKebab}/${dataModelPascal}Mapper`,
      namedImports: [`mapTo${dataModelPascal}`],
    });
  }

  // Add all imports that don't already exist
  importDeclarations.forEach(importDecl => {
    if (!sf.getImportDeclaration(d => d.getModuleSpecifierValue()?.includes(importDecl.moduleSpecifier))) {
      sf.insertImportDeclaration(0, importDecl);
    }
  });
}

// Create use cases
function createUseCases(project, dirs, names, hasNoParam, noDataModel) {
  createMainUseCase(project, dirs, names, hasNoParam, noDataModel);
}

// Create main use case
function createMainUseCase(project, dirs, names, hasNoParam, noDataModel) {
  const {moduleNameExact, functionNamePascal, methodName, functionNameKebab, dataModelPascal, domainNameKebab} = names;
  const useCaseFilePath = path.join(dirs.useCaseDir, `${functionNamePascal}UseCase.ts`);

  createUseCaseFile({
    filePath: useCaseFilePath,
    project,
    moduleNameExact,
    functionNamePascal,
    methodName,
    hasNoParam,
    functionNameKebab,
    dataModelPascal,
    domainNameKebab,
    noDataModel,
  });
}

// Create model files
function createModels(modelDir, project, names, hasNoParam, noDataModel) {
  const {functionNamePascal, dataModelPascal} = names;
  createModelFiles(modelDir, project, functionNamePascal, hasNoParam, dataModelPascal, noDataModel);
}

// Update PathResolver.ts
function updatePathResolver(rootDir, names, hasNoParam) {
  const {moduleNameExact, methodName, domainNameCamel} = names;
  const pathResolverPath = path.join(rootDir, 'src', 'utils', 'PathResolver.ts');
  const apiPath = `/${toKebabCase(moduleNameExact)}/${toKebabCase(methodName)}`;

  if (fs.existsSync(pathResolverPath)) {
    let content = fs.readFileSync(pathResolverPath, 'utf-8');

    const pathResolverRegex = /export\s+const\s+PathResolver\s*=\s*{([\s\S]*?)^};/m;
    const pathResolverMatch = pathResolverRegex.exec(content);
    const domainPattern = new RegExp(`(${domainNameCamel}\\s*:\\s*{)([\\s\\S]*?)(^\\s*})`, 'm');
    const domainMatch = domainPattern.exec(content);

    if (domainMatch) {
      if (domainMatch[2].includes(`${methodName}:`)) {
        console.log(`[SKIP] PathResolver.${domainNameCamel} đã có "${methodName}"`);
      } else {
        const insertion = `    ${methodName}: () => \`\${baseUrl}${apiPath}\`,\n`;
        const updatedBlock = domainMatch[1] + '\n' + domainMatch[2] + insertion + domainMatch[3];
        content = content.replace(domainPattern, updatedBlock);
        fs.writeFileSync(pathResolverPath, content, 'utf-8');
        console.log(`[UPDATE] Đã thêm path "${methodName}" vào PathResolver.${domainNameCamel}`);
      }
    } else if (pathResolverMatch) {
      // Nếu chưa có domain, chèn domain mới với method vào
      const domainInsert = `  ${domainNameCamel}: {\n    ${methodName}: () => \`\${baseUrl}${apiPath}\`,\n  },\n`;
      const updated = content.replace(/(export\s+const\s+PathResolver\s*=\s*{)/, `$1\n${domainInsert}`);
      fs.writeFileSync(pathResolverPath, updated, 'utf-8');
      console.log(`[ADD] Đã tạo mới PathResolver.${domainNameCamel} với path "${methodName}"`);
    } else {
      console.warn('[WARN] Không tìm thấy object PathResolver trong file.');
    }
  } else {
    console.warn('[SKIP] Không tìm thấy file PathResolver.ts');
  }
}

// Update DIContainer.ts
function updateDIContainer(rootDir, names) {
  const {moduleNameExact, functionNamePascal, domainNameKebab, domainNameCamel} = names;
  const diPath = path.join(rootDir, 'src', 'di', 'DIContainer.ts');

  if (!fs.existsSync(diPath)) {
    console.warn('[SKIP] Không tìm thấy DIContainer.ts');
    return;
  }

  // Define imports
  const importLines = [
    `import {I${moduleNameExact}Service} from '@data/datasources/I${moduleNameExact}Service';`,
    `import {${moduleNameExact}Service} from '@data/datasources/remote/${moduleNameExact}Service';`,
    `import {I${moduleNameExact}Repository} from '@domain/repositories/I${moduleNameExact}Repository';`,
    `import {${moduleNameExact}Repository} from '@data/repositories/${moduleNameExact}Repository';`,
    `import {${functionNamePascal}UseCase} from '@use-cases/${domainNameKebab}/${functionNamePascal}UseCase';`,
  ];

  // Read file content
  let content = fs.readFileSync(diPath, 'utf-8');

  // Add imports
  const importsToAdd = importLines.filter(line => !content.includes(line));
  if (importsToAdd.length > 0) {
    content = importsToAdd.join('\n') + '\n' + content;
    console.log(`[UPDATE] Đã thêm ${importsToAdd.length} import vào DIContainer.ts`);
  } else {
    console.log('[SKIP] Tất cả import đã tồn tại trong DIContainer.ts');
  }

  // ADD PRIVATE FIELDS
  const fieldMarker = /private readonly\s+flagToInjectFields\s*=\s*false;/;
  const newFields = `
  \n
    private ${domainNameCamel}Service!: I${moduleNameExact}Service;
    private ${domainNameCamel}Repository!: I${moduleNameExact}Repository;
  `;
  if (!content.includes(`${domainNameCamel}Service`)) {
    content = content.replace(fieldMarker, match => match + newFields);
  }

  // ADD GET SERVICE METHOD
  const getService = `
    private get${moduleNameExact}Service(): I${moduleNameExact}Service {
      if (!this.${domainNameCamel}Service) {
        this.${domainNameCamel}Service = new ${moduleNameExact}Service(this.httpClient);
      }
      return this.${domainNameCamel}Service;
    }
  `;
  if (!content.includes(`get${moduleNameExact}Service()`)) {
    content = content.replace('//DATA SOURCES', `//DATA SOURCES\n${getService}`);
  }

  // ADD GET REPOSITORY METHOD
  const getRepo = `
    public get${moduleNameExact}Repository(): I${moduleNameExact}Repository {
      if (!this.${domainNameCamel}Repository) {
        this.${domainNameCamel}Repository = new ${moduleNameExact}Repository(this.get${moduleNameExact}Service());
      }
      return this.${domainNameCamel}Repository;
    }
  `;
  if (!content.includes(`get${moduleNameExact}Repository()`)) {
    content = content.replace('// REPORITORIES', `// REPORITORIES\n${getRepo}`);
  }

  // ADD GET USECASE METHOD
  const getUseCase = `
    public get${functionNamePascal}UseCase(): ${functionNamePascal}UseCase {
      return new ${functionNamePascal}UseCase(this.get${moduleNameExact}Repository());
    }
  `;
  if (!content.includes(`get${functionNamePascal}UseCase()`)) {
    if (content.includes('// USE CASES')) {
      content = content.replace('// USE CASES', `// USE CASES\n${getUseCase}`);
    } else {
      content += `\n${getUseCase}`;
    }
  }

  fs.writeFileSync(diPath, content, 'utf-8');
  console.log(`[UPDATE] Đã thêm ${functionNamePascal}UseCase, Repository, Service vào DIContainer.`);
}

main().catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
